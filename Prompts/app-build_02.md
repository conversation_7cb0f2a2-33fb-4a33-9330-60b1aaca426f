Objective: build a dashboard panel for training and evaluationg Knowledge Tracing models using existing data sets

Implemntation plan:

- using python flask, and tailwind for styling, build a web app providing a dashboard with options to run a training sessions for KT ML models
- dashboard will provide KT training sessions
- each session will have unique paramters and configruations for running the training
- each session will store the training resultes and data set reporting metrics
- dashboard will provide a list of all training sessions on left side with button to "start new session" 
- dashboard right side 80% will be the training session panel
- training panel will have 3 vertical section 
 1) trainign configuration section
 2) training results section
 3) data set reporting metrics section

- 1) trainign configuration section will provide drop down to select the KT model to train, another drowdown to select the data set to train on, dropdown to select the training validation method and a button to start training. the section will also have a form to enter the training parameters defaulted with predefined values for each KT model and dataset. the form will have a button to save the training parameters.
below dataset selection there should be a link to preview the dataset opening a modal with the dataset preview with data set analysis metrics from the backend apis to be developeed, and another link to open a modal to upload a new dataset
 - 2) training results section will display the training results console on left side showing real-time training process output and a table on right side showing the training results and end of training evaluation metrics.
 - 3) data set reporting metrics section will display the data set reporting metrics

- after training there will be a button to save the training results and metrics to the database for future reference and comparison
- start training button will be disabled until the training parameters are saved, and another button will be enabled to save the trained model version into the model repository directory

ML implmenations
- Use python, pandans, pytorch 
- use EduData to download datasets
- use EduKTM for models implementation
- initial models to be implemented are BKT, PFA, DKT
- validation methods should list all possible cross validation options split, k-folds, leave one out..etc
- evaluation should provide Accuracy, precision, recall, f1-score, ROC, AUC metrics and confusion matrix

Dashboard dev structure
- build app in new folder "Dashboard" with subfolder for datasets, and folder for models repository.
- provide app running instructions document 
- before building refrence existing technical implementaion deatils in @Documentaion/KT_model_dashboard.md

<!-- - training panel will have 3 tabs: 1) training session parameters, 2) training results, 3) data set reporting metrics
- training session parameters tab will have form to enter training session parameters
- training results tab will have table to display training results
- data set reporting metrics tab will have table to display data set reporting metrics -->

Built Dashboard application is showing only the page html, buttons are not working let's apply following to make it functional

- create another link "Avaialble dataset" to open a bootstrap modal that shows all available lists in the EduData with options to download to app /datasets folder. it should show a a table listing data sets, is downloaded, size, other details, download Dataset button
- update "Preview Dataset" to open another model send a xhr request to app endpoint to analyzie and preview the data set