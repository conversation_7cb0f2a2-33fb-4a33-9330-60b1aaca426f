Objective:
The goal of this assignment is to develop a recommendation system using collaborative
filtering techniques only. You are allowed to implement either user-based/item-based nearest
neighbor models or matrix decomposition methods (e.g., Singular Value Decomposition -
SVD, or alternatives like ALS).
Dataset Description:
You are provided with:
•ratings_train.csv: User-item interactions including user IDs, movie IDs, and
•corresponding ratings.
ratings_test.csv: User-item pairs for which you are expected to predict ratings. Note
that the true ratings are hidden.
Instructions:
1. Load Data:
o Load ratings_train.csv and ratings_test.csv using pandas.
o You may preprocess the data as needed (e.g., creating user-item matrices).
2. Model Implementation:
o You must only use collaborative filtering methods:
▪ User-based or item-based collaborative filtering using similarity measures
(cosine, Pearson, etc.) or;
▪ Matrix factorization methods (e.g., truncated SVD, ALS, or other linear
factorization approaches).
o You may not use any content-based or hybrid methods.
o Do not use deep learning or neural network-based recommenders.
3. Prediction Task:
o Predict the missing rating values in ratings_test.csv using your model
trained on ratings_train.csv.
o Save your predictions in a CSV file named predictions.csv with the following
columns: userId, movieId, predicted_rating.
4. Evaluation:
o We will evaluate your results using Root Mean Square Error (RMSE) against
the hidden ground-truth ratings.
5. Deliverables:
o A Jupyter Notebook (.ipynb) containing your code and explanations .
o The file predictions.csv as described above.
Restrictions:
•
You must not use any pre-built recommender system libraries (such as Surprise,
LightFM, or implicit).•
You may use general-purpose libraries such as NumPy, pandas, scikit-learn, and
scipy.