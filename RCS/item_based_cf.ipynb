{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Item-Based Collaborative Filtering\n", "\n", "This notebook implements an item-based collaborative filtering system using similarity measures (cosine and Pearson correlation). The implementation follows a pure collaborative filtering approach without using any content-based or hybrid methods.\n", "\n", "## Implementation Steps\n", "1. Load and preprocess training data\n", "2. Create user-item matrices\n", "3. Calculate item-item similarity\n", "4. Generate predictions using k-nearest neighbors\n", "5. Evaluate the model\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "from scipy.stats import pearsonr\n", "import warnings\n", "warnings.filterwarnings('ignore')  # Suppress warnings for cleaner output\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load training data and take first 500 rows\n", "train_df = pd.read_csv('ratings_train.csv')\n", "train_subset = train_df.head(500)\n", "\n", "print(\"Full training data shape:\", train_df.shape)\n", "print(\"Subset data shape:\", train_subset.shape)\n", "print(\"\\nFirst few rows of subset data:\")\n", "print(train_subset.head())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create user-item matrix\n", "def create_user_item_matrix(df):\n", "    \"\"\"\n", "    Create a user-item matrix from the dataframe\n", "    Returns both the matrix and a mapping of movie indices\n", "    \"\"\"\n", "    # Create pivot table\n", "    matrix = df.pivot(index='userId', columns='movieId', values='rating').fillna(0)\n", "    \n", "    # Create a mapping of movie IDs to matrix column indices\n", "    movie_to_idx = {movie: idx for idx, movie in enumerate(matrix.columns)}\n", "    \n", "    return matrix, movie_to_idx\n", "\n", "# Create the matrix from subset\n", "user_item_matrix, movie_mapping = create_user_item_matrix(train_subset)\n", "\n", "print(\"Matrix shape:\", user_item_matrix.shape)\n", "print(\"\\nNumber of unique movies:\", len(movie_mapping))\n", "print(\"\\nFirst few rows of user-item matrix:\")\n", "print(user_item_matrix.head())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Calculate item-item similarity matrix using both Cosine and Pearson correlation\n", "def calculate_similarity(matrix, method='cosine'):\n", "    \"\"\"\n", "    Calculate item-item similarity matrix using specified method\n", "    \"\"\"\n", "    if method == 'cosine':\n", "        # Transpose matrix to get item features\n", "        item_features = matrix.T\n", "        # Calculate cosine similarity\n", "        similarity_matrix = cosine_similarity(item_features)\n", "        return pd.DataFrame(similarity_matrix, \n", "                          index=matrix.columns,\n", "                          columns=matrix.columns)\n", "    \n", "    elif method == 'pearson':\n", "        n_items = matrix.shape[1]\n", "        similarity_matrix = np.zeros((n_items, n_items))\n", "        \n", "        for i in range(n_items):\n", "            for j in range(i, n_items):\n", "                item1 = matrix.iloc[:, i]\n", "                item2 = matrix.iloc[:, j]\n", "                \n", "                # Calculate Pearson correlation only for users who rated both items\n", "                mask = (item1 != 0) & (item2 != 0)\n", "                if mask.sum() > 1:  # Need at least 2 points for correlation\n", "                    correlation, _ = pearsonr(item1[mask], item2[mask])\n", "                    similarity_matrix[i, j] = correlation\n", "                    similarity_matrix[j, i] = correlation\n", "                \n", "        return pd.DataFrame(similarity_matrix,\n", "                          index=matrix.columns,\n", "                          columns=matrix.columns)\n", "\n", "# Calculate similarities using both methods\n", "cosine_sim = calculate_similarity(user_item_matrix, method='cosine')\n", "pearson_sim = calculate_similarity(user_item_matrix, method='pearson')\n", "\n", "print(\"Cosine similarity matrix shape:\", cosine_sim.shape)\n", "print(\"\\nSample of cosine similarity matrix (first 5x5):\")\n", "print(cosine_sim.iloc[:5, :5])\n", "print(\"\\nPearson similarity matrix shape:\", pearson_sim.shape)\n", "print(\"\\nSample of Pearson similarity matrix (first 5x5):\")\n", "print(pearson_sim.iloc[:5, :5])\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Implement prediction function using k-nearest neighbors\n", "def predict_rating(user_id, movie_id, matrix, similarity_matrix, k=5):\n", "    \"\"\"\n", "    Predict rating for a user-item pair using k most similar items\n", "    \"\"\"\n", "    if movie_id not in matrix.columns or user_id not in matrix.index:\n", "        return 3.0  # Return average rating for unknown users/items\n", "    \n", "    # Get user's ratings\n", "    user_ratings = matrix.loc[user_id]\n", "    \n", "    # Get similarity scores for the target movie\n", "    movie_similarities = similarity_matrix[movie_id]\n", "    \n", "    # Find k most similar items that the user has rated\n", "    rated_items = user_ratings[user_ratings > 0].index\n", "    similar_items = movie_similarities[rated_items].sort_values(ascending=False)[:k]\n", "    \n", "    if len(similar_items) == 0:\n", "        return 3.0  # Return average rating if no similar items found\n", "    \n", "    # Calculate weighted average rating\n", "    numerator = 0\n", "    denominator = 0\n", "    \n", "    for item, similarity in similar_items.items():\n", "        rating = user_ratings[item]\n", "        numerator += similarity * rating\n", "        denominator += abs(similarity)\n", "    \n", "    if denominator == 0:\n", "        return 3.0\n", "    \n", "    predicted_rating = numerator / denominator\n", "    \n", "    # Clip prediction to valid range [1, 5]\n", "    return max(min(predicted_rating, 5), 1)\n", "\n", "# Test the prediction function on a few examples\n", "test_users = user_item_matrix.index[:3]\n", "test_movies = user_item_matrix.columns[:3]\n", "\n", "print(\"Sample predictions using cosine similarity:\")\n", "for user in test_users:\n", "    for movie in test_movies:\n", "        actual = user_item_matrix.loc[user, movie]\n", "        predicted = predict_rating(user, movie, user_item_matrix, cosine_sim)\n", "        print(f\"User {user}, Movie {movie}:\")\n", "        print(f\"  Actual rating: {actual:.2f}\")\n", "        print(f\"  Predicted rating: {predicted:.2f}\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Implementation Details\n", "\n", "This notebook implements an item-based collaborative filtering system using similarity measures. Here's a breakdown of the implementation:\n", "\n", "1. **Data Loading and Preprocessing**:\n", "   - Load training data and select first 500 rows as a subset\n", "   - Create user-item matrix with movies as columns\n", "   - Handle missing values by filling with zeros\n", "\n", "2. **Similarity Calculation**:\n", "   - Implemented two similarity measures:\n", "     * Cosine similarity: Using sklear<PERSON>'s cosine_similarity\n", "     * Pearson correlation: Using sci<PERSON>'s pearsonr\n", "   - Created item-item similarity matrices for both measures\n", "\n", "3. **Rating Prediction**:\n", "   - Used k-nearest neighbors approach (k=5 by default)\n", "   - For each prediction:\n", "     * Find k most similar items that the user has rated\n", "     * Calculate weighted average rating based on similarities\n", "     * Handle edge cases (unknown users/items, no similar items)\n", "     * Clip predictions to valid range [1, 5]\n", "\n", "4. **Key Features**:\n", "   - Pure collaborative filtering approach (no content-based features)\n", "   - <PERSON>les cold-start problems with default predictions\n", "   - Efficient similarity calculations\n", "   - Flexible similarity measure selection (co<PERSON><PERSON> or <PERSON>)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load test data\n", "test_df = pd.read_csv('ratings_test.csv')\n", "print(\"Test data shape:\", test_df.shape)\n", "\n", "# Generate predictions for all test set entries\n", "test_predictions = []\n", "for _, row in test_df.iterrows():\n", "    user_id = row['userId']\n", "    movie_id = row['movieId']\n", "    # Generate predictions using both similarity measures\n", "    cosine_pred = predict_rating(user_id, movie_id, user_item_matrix, cosine_sim)\n", "    pearson_pred = predict_rating(user_id, movie_id, user_item_matrix, pearson_sim)\n", "    test_predictions.append({\n", "        'userId': user_id,\n", "        'movieId': movie_id,\n", "        'cosine_prediction': cosine_pred,\n", "        'pearson_prediction': pearson_pred,\n", "        'actual_rating': row['rating']\n", "    })\n", "\n", "# Convert predictions to DataFrame\n", "predictions_df = pd.DataFrame(test_predictions)\n", "\n", "# Calculate RMSE for both methods\n", "def calculate_rmse(true_ratings, predicted_ratings):\n", "    \"\"\"\n", "    Calculate Root Mean Square Error\n", "    \"\"\"\n", "    mse = mean_squared_error(true_ratings, predicted_ratings)\n", "    rmse = np.sqrt(mse)\n", "    return rmse\n", "\n", "# Calculate RMSE for both similarity measures\n", "cosine_rmse = calculate_rmse(predictions_df['actual_rating'], \n", "                           predictions_df['cosine_prediction'])\n", "pearson_rmse = calculate_rmse(predictions_df['actual_rating'], \n", "                            predictions_df['pearson_prediction'])\n", "\n", "print(\"\\nEvaluation Results:\")\n", "print(f\"Cosine Similarity RMSE: {cosine_rmse:.4f}\")\n", "print(f\"Pearson Correlation RMSE: {pearson_rmse:.4f}\")\n", "\n", "# Save predictions to CSV (using the better performing method)\n", "if cosine_rmse <= pearson_rmse:\n", "    final_predictions = predictions_df[['userId', 'movieId', 'cosine_prediction']]\n", "    final_predictions.columns = ['userId', 'movieId', 'predicted_rating']\n", "else:\n", "    final_predictions = predictions_df[['userId', 'movieId', 'pearson_prediction']]\n", "    final_predictions.columns = ['userId', 'movieId', 'predicted_rating']\n", "\n", "final_predictions.to_csv('predictions.csv', index=False)\n", "print(\"\\nFinal predictions saved to 'predictions.csv'\")\n", "\n", "# Display some statistics about the predictions\n", "print(\"\\nPrediction Statistics:\")\n", "print(\"\\nCosine Similarity Predictions:\")\n", "print(predictions_df['cosine_prediction'].describe())\n", "print(\"\\nPearson Correlation Predictions:\")\n", "print(predictions_df['pearson_prediction'].describe())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Evaluation Results Analysis\n", "\n", "The evaluation compares the performance of two similarity measures:\n", "1. <PERSON><PERSON><PERSON> Similarity\n", "2. Pearson Correlation\n", "\n", "The RMSE (Root Mean Square Error) is calculated for both methods. A lower RMSE indicates better prediction accuracy.\n", "\n", "The evaluation process:\n", "1. Generate predictions for all user-item pairs in the test set\n", "2. Calculate RMSE for both similarity measures\n", "3. Choose the better performing method for final predictions\n", "4. Save predictions in the required format (userId, movieId, predicted_rating)\n", "\n", "Note that we're using only the first 500 rows of training data, which might limit the model's performance. For better results, you could:\n", "1. Use the full training dataset\n", "2. Tune the k parameter in k-nearest neighbors\n", "3. Try different approaches for handling cold-start cases\n", "4. Experiment with other similarity measures\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}