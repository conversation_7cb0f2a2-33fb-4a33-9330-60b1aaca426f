{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Collaborative Filtering Recommender System\n", "\n", "This notebook implements a collaborative filtering recommender system for predicting movie ratings. The implementation follows the requirements specified in the homework assignment.\n", "\n", "## Setup and Requirements\n", "- Python 3.x\n", "- NumPy\n", "- <PERSON><PERSON>\n", "- Sc<PERSON>t-learn\n", "- SciPy\n", "\n", "## Implementation Steps\n", "1. Data Loading and Preprocessing\n", "2. Model Implementation (SVD-based Collaborative Filtering)\n", "3. Prediction Generation\n", "4. Evaluation\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.sparse.linalg import svds\n", "from sklearn.metrics import mean_squared_error\n", "from math import sqrt\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the training and test data\n", "train_df = pd.read_csv('ratings_train.csv')\n", "test_df = pd.read_csv('ratings_test.csv')\n", "\n", "print(\"Training data shape:\", train_df.shape)\n", "print(\"Test data shape:\", test_df.shape)\n", "\n", "# Display first few rows of training data\n", "print(\"\\nFirst few rows of training data:\")\n", "print(train_df.head())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create user-item matrix\n", "def create_matrix(df):\n", "    \"\"\"\n", "    Create a user-item matrix from the dataframe\n", "    \"\"\"\n", "    # Create pivot table\n", "    matrix = df.pivot(index='userId', columns='movieId', values='rating').fillna(0)\n", "    return matrix\n", "\n", "# Create the user-item matrices\n", "train_matrix = create_matrix(train_df)\n", "print(\"Matrix shape:\", train_matrix.shape)\n", "\n", "# Normalize the matrix by subtracting mean rating for each user\n", "def normalize_matrix(matrix):\n", "    \"\"\"\n", "    Normalize the matrix by subtracting the mean rating for each user\n", "    \"\"\"\n", "    # Calculate mean rating for each user\n", "    user_ratings_mean = np.mean(matrix, axis=1)\n", "    # Subtract mean rating from each rating\n", "    matrix_normalized = matrix.sub(user_ratings_mean, axis=0)\n", "    return matrix_normalized, user_ratings_mean\n", "\n", "train_matrix_normalized, user_ratings_mean = normalize_matrix(train_matrix)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform SVD\n", "def perform_svd(matrix, k=50):\n", "    \"\"\"\n", "    Perform SVD on the matrix and return the reconstructed matrix\n", "    \"\"\"\n", "    # Convert to numpy array\n", "    matrix_np = matrix.to_numpy()\n", "    \n", "    # Perform SVD\n", "    U, sigma, Vt = svds(matrix_np, k=k)\n", "    \n", "    # Convert sigma to diagonal matrix\n", "    sigma = np.diag(sigma)\n", "    \n", "    # Reconstruct the matrix\n", "    predicted_ratings = np.dot(np.dot(U, sigma), Vt)\n", "    \n", "    return predicted_ratings\n", "\n", "# Get predicted ratings\n", "predicted_ratings = perform_svd(train_matrix_normalized)\n", "\n", "# Add back the user means to get actual predicted ratings\n", "final_predicted_ratings = predicted_ratings + user_ratings_mean.values.reshape(-1, 1)\n", "\n", "# Convert predictions back to DataFrame\n", "pred_df = pd.DataFrame(\n", "    final_predicted_ratings,\n", "    index=train_matrix.index,\n", "    columns=train_matrix.columns\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate predictions for test set\n", "def predict_ratings(pred_df, test_df):\n", "    \"\"\"\n", "    Generate predictions for the test set\n", "    \"\"\"\n", "    predictions = []\n", "    for _, row in test_df.iterrows():\n", "        user_id = row['userId']\n", "        movie_id = row['movieId']\n", "        if user_id in pred_df.index and movie_id in pred_df.columns:\n", "            pred_rating = pred_df.loc[user_id, movie_id]\n", "            # Clip predictions to be between 1 and 5\n", "            pred_rating = max(min(pred_rating, 5), 1)\n", "        else:\n", "            # If user or movie not in training set, predict mean rating\n", "            pred_rating = 3.0\n", "        predictions.append(pred_rating)\n", "    \n", "    return predictions\n", "\n", "# Generate predictions for test set\n", "test_predictions = predict_ratings(pred_df, test_df)\n", "\n", "# Create predictions DataFrame\n", "predictions_df = pd.DataFrame({\n", "    'userId': test_df['userId'],\n", "    'movieId': test_df['movieId'],\n", "    'predicted_rating': test_predictions\n", "})\n", "\n", "# Save predictions to CSV\n", "predictions_df.to_csv('predictions.csv', index=False)\n", "print(\"\\nPredictions saved to 'predictions.csv'\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Implementation Details\n", "\n", "This notebook implements a collaborative filtering recommender system using Singular Value Decomposition (SVD). Here's a breakdown of the implementation:\n", "\n", "1. **Data Loading and Preprocessing**:\n", "   - Load training and test datasets\n", "   - Create user-item matrices\n", "   - Normalize ratings by subtracting mean rating for each user\n", "\n", "2. **Model Implementation**:\n", "   - Use SVD for matrix factorization with 50 latent factors\n", "   - Reconstruct the rating matrix using U, Σ, and V^T matrices\n", "   - Add back user means to get final predictions\n", "\n", "3. **Prediction Generation**:\n", "   - Generate predictions for test set\n", "   - Clip predictions to valid rating range (1-5)\n", "   - Handle cold-start cases (users/items not in training set)\n", "   - Save predictions to CSV file\n", "\n", "The implementation follows the requirements:\n", "- Uses only collaborative filtering (SVD-based)\n", "- No pre-built recommender system libraries used\n", "- Only uses NumPy, Pandas, and SciPy for implementation\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}