{"cells": [{"cell_type": "markdown", "id": "e002fdf8", "metadata": {}, "source": ["# KDD Cup 2010 —— Data Analysis on algebra_2006_2007_train"]}, {"cell_type": "markdown", "id": "429152ff", "metadata": {}, "source": ["## Data Description"]}, {"cell_type": "markdown", "id": "2c89d116", "metadata": {}, "source": ["### Column Description"]}, {"cell_type": "markdown", "id": "f590eee5", "metadata": {}, "source": ["| Attribute | Annotaion |\n", "|:--:|---|\n", "|Row|The row number|\n", "| Anon Student Id             | Unique, anonymous identifier for a student    |\n", "| Problem Hierarchy           | The hierarchy of curriculum levels containing the problem |\n", "| Problem Name                | Unique identifier for a problem |\n", "| Problem View                | The total number of times the student encountered the problem so far |\n", "| Step Name                   | Unique identifier for one of the steps in a problem |\n", "| Step Start Time             | The starting time of the step (Can be null) |\n", "| First Transaction Time      | The time of the first transaction toward the step |\n", "| Correct Transaction Time    | The time of the correct attempt toward the step, if there was one |\n", "| Step End Time               | The time of the last transaction toward the step |\n", "| Step Duration (sec)         | The elapsed time of the step in seconds, calculated by adding all of the durations for transactions that were attributed to the step (Can be null if step start time is null) |\n", "| Correct Step Duration (sec) | The step duration if the first attempt for the step was correct |\n", "| Error Step Duration (sec)   | The step duration if the first attempt for the step was an error (incorrect attempt or hint request) |\n", "| Correct First Attempt       | The tutor's evaluation of the student's first attempt on the step—1 if correct, 0 if an error |\n", "| Incorrects                  | Total number of incorrect attempts by the student on the step |\n", "| Hints                       | Total number of hints requested by the student for the step |\n", "| Corrects                    | Total correct attempts by the student for the step (only increases if the step is encountered more than once) |\n", "| KC(KC Model Name)           | The identified skills that are used in a problem, where available |\n", "| Opportunity(KC Model Name)  | A count that increases by one each time the student encounters a step with the listed knowledge component |\n", "|| Additional KC models, which exist for the challenge data sets, will appear as additional pairs of columns (KC and Opportunity columns for each model) |"]}, {"cell_type": "markdown", "id": "2c2a2d3e", "metadata": {}, "source": ["For the test portion of the challenge data sets, values will not be provided for the following columns:"]}, {"cell_type": "markdown", "id": "f19eb949", "metadata": {}, "source": ["&diams; Step Start Time\n", "\n", "&diams; First Transaction Time\n", "\n", "&diams; Correct Transaction Time\n", "\n", "&diams; Step End Time\n", "\n", "&diams; Step Duration (sec)\n", "\n", "&diams; Correct Step Duration (sec)\n", "\n", "&diams; Error Step Duration (sec)\n", "\n", "&diams; Correct First Attempt\n", "\n", "&diams; Incorrects\n", "\n", "&diams; Hints\n", "\n", "&diams; Corrects"]}, {"cell_type": "code", "execution_count": 1, "id": "123674b7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import plotly.express as px"]}, {"cell_type": "code", "execution_count": 2, "id": "efa6be16", "metadata": {}, "outputs": [], "source": ["path = \"algebra_2006_2007_train.txt\"\n", "data = pd.read_table(path, encoding=\"ISO-8859-15\", low_memory=False)"]}, {"cell_type": "markdown", "id": "993f1986", "metadata": {}, "source": ["## Record Examples"]}, {"cell_type": "code", "execution_count": 3, "id": "8b2af14e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th><PERSON><PERSON> Student Id</th>\n", "      <th>Problem Hierarchy</th>\n", "      <th>Problem Name</th>\n", "      <th>Problem View</th>\n", "      <th>Step Name</th>\n", "      <th>Step Start Time</th>\n", "      <th>First Transaction Time</th>\n", "      <th>Correct Transaction Time</th>\n", "      <th>Step End Time</th>\n", "      <th>Step Duration (sec)</th>\n", "      <th>Correct Step Duration (sec)</th>\n", "      <th>Error Step Duration (sec)</th>\n", "      <th>Correct First Attempt</th>\n", "      <th>Incorrects</th>\n", "      <th>Hints</th>\n", "      <th>Corrects</th>\n", "      <th><PERSON>(De<PERSON><PERSON>)</th>\n", "      <th>Opportunity(Default)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>JG4Tz</td>\n", "      <td>Unit CTA1_01, Section CTA1_01-1</td>\n", "      <td>LDEMO_WKST</td>\n", "      <td>1</td>\n", "      <td>R1C1</td>\n", "      <td>2006-10-26 09:51:58.0</td>\n", "      <td>2006-10-26 09:52:30.0</td>\n", "      <td>2006-10-26 09:53:30.0</td>\n", "      <td>2006-10-26 09:53:30.0</td>\n", "      <td>92.0</td>\n", "      <td>NaN</td>\n", "      <td>92.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>JG4Tz</td>\n", "      <td>Unit CTA1_01, Section CTA1_01-1</td>\n", "      <td>LDEMO_WKST</td>\n", "      <td>1</td>\n", "      <td>R1C2</td>\n", "      <td>2006-10-26 09:53:30.0</td>\n", "      <td>2006-10-26 09:53:41.0</td>\n", "      <td>2006-10-26 09:53:41.0</td>\n", "      <td>2006-10-26 09:53:41.0</td>\n", "      <td>11.0</td>\n", "      <td>11.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>JG4Tz</td>\n", "      <td>Unit CTA1_01, Section CTA1_01-1</td>\n", "      <td>LDEMO_WKST</td>\n", "      <td>1</td>\n", "      <td>R2C1</td>\n", "      <td>2006-10-26 09:53:41.0</td>\n", "      <td>2006-10-26 09:53:46.0</td>\n", "      <td>2006-10-26 09:53:46.0</td>\n", "      <td>2006-10-26 09:53:46.0</td>\n", "      <td>5.0</td>\n", "      <td>5.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>Identifying units</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>JG4Tz</td>\n", "      <td>Unit CTA1_01, Section CTA1_01-1</td>\n", "      <td>LDEMO_WKST</td>\n", "      <td>1</td>\n", "      <td>R2C2</td>\n", "      <td>2006-10-26 09:53:46.0</td>\n", "      <td>2006-10-26 09:53:50.0</td>\n", "      <td>2006-10-26 09:53:50.0</td>\n", "      <td>2006-10-26 09:53:50.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>Identifying units</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>JG4Tz</td>\n", "      <td>Unit CTA1_01, Section CTA1_01-1</td>\n", "      <td>LDEMO_WKST</td>\n", "      <td>1</td>\n", "      <td>R4C1</td>\n", "      <td>2006-10-26 09:53:50.0</td>\n", "      <td>2006-10-26 09:54:05.0</td>\n", "      <td>2006-10-26 09:54:05.0</td>\n", "      <td>2006-10-26 09:54:05.0</td>\n", "      <td>15.0</td>\n", "      <td>15.0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>Entering a given</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Row Anon Student Id                Problem Hierarchy Problem Name  \\\n", "0    1           JG4Tz  Unit CTA1_01, Section CTA1_01-1   LDEMO_WKST   \n", "1    2           JG4Tz  Unit CTA1_01, Section CTA1_01-1   LDEMO_WKST   \n", "2    3           JG4Tz  Unit CTA1_01, Section CTA1_01-1   LDEMO_WKST   \n", "3    4           JG4Tz  Unit CTA1_01, Section CTA1_01-1   LDEMO_WKST   \n", "4    5           JG4Tz  Unit CTA1_01, Section CTA1_01-1   LDEMO_WKST   \n", "\n", "   Problem View Step Name        Step Start Time First Transaction Time  \\\n", "0             1      R1C1  2006-10-26 09:51:58.0  2006-10-26 09:52:30.0   \n", "1             1      R1C2  2006-10-26 09:53:30.0  2006-10-26 09:53:41.0   \n", "2             1      R2C1  2006-10-26 09:53:41.0  2006-10-26 09:53:46.0   \n", "3             1      R2C2  2006-10-26 09:53:46.0  2006-10-26 09:53:50.0   \n", "4             1      R4C1  2006-10-26 09:53:50.0  2006-10-26 09:54:05.0   \n", "\n", "  Correct Transaction Time          Step End Time  Step Duration (sec)  \\\n", "0    2006-10-26 09:53:30.0  2006-10-26 09:53:30.0                 92.0   \n", "1    2006-10-26 09:53:41.0  2006-10-26 09:53:41.0                 11.0   \n", "2    2006-10-26 09:53:46.0  2006-10-26 09:53:46.0                  5.0   \n", "3    2006-10-26 09:53:50.0  2006-10-26 09:53:50.0                  4.0   \n", "4    2006-10-26 09:54:05.0  2006-10-26 09:54:05.0                 15.0   \n", "\n", "   Correct Step Duration (sec)  Error Step Duration (sec)  \\\n", "0                          NaN                       92.0   \n", "1                         11.0                        NaN   \n", "2                          5.0                        NaN   \n", "3                          4.0                        NaN   \n", "4                         15.0                        NaN   \n", "\n", "   Correct First Attempt  Incorrects  Hints  Corrects        KC(Default)  \\\n", "0                      0           2      0         1                NaN   \n", "1                      1           0      0         1                NaN   \n", "2                      1           0      0         1  Identifying units   \n", "3                      1           0      0         1  Identifying units   \n", "4                      1           0      0         1   Entering a given   \n", "\n", "  Opportunity(Default)  \n", "0                  NaN  \n", "1                  NaN  \n", "2                    1  \n", "3                    2  \n", "4                    1  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.set_option('display.max_column', 500)\n", "data.head()"]}, {"cell_type": "code", "execution_count": 4, "id": "9d5e5859", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th>Problem View</th>\n", "      <th>Step Duration (sec)</th>\n", "      <th>Correct Step Duration (sec)</th>\n", "      <th>Error Step Duration (sec)</th>\n", "      <th>Correct First Attempt</th>\n", "      <th>Incorrects</th>\n", "      <th>Hints</th>\n", "      <th>Corrects</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2.270384e+06</td>\n", "      <td>2.270384e+06</td>\n", "      <td>2.267551e+06</td>\n", "      <td>1.751638e+06</td>\n", "      <td>515913.000000</td>\n", "      <td>2.270384e+06</td>\n", "      <td>2.270384e+06</td>\n", "      <td>2.270384e+06</td>\n", "      <td>2.270384e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1.513120e+06</td>\n", "      <td>1.092910e+00</td>\n", "      <td>1.958364e+01</td>\n", "      <td>1.171716e+01</td>\n", "      <td>46.292087</td>\n", "      <td>7.722359e-01</td>\n", "      <td>4.455044e-01</td>\n", "      <td>1.184311e-01</td>\n", "      <td>1.062878e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>8.736198e+05</td>\n", "      <td>3.448857e-01</td>\n", "      <td>4.768345e+01</td>\n", "      <td>2.645318e+01</td>\n", "      <td>81.817794</td>\n", "      <td>4.193897e-01</td>\n", "      <td>2.000914e+00</td>\n", "      <td>6.199071e-01</td>\n", "      <td>6.894285e-01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>7.577408e+05</td>\n", "      <td>1.000000e+00</td>\n", "      <td>3.000000e+00</td>\n", "      <td>3.000000e+00</td>\n", "      <td>11.000000</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.511844e+06</td>\n", "      <td>1.000000e+00</td>\n", "      <td>7.000000e+00</td>\n", "      <td>5.000000e+00</td>\n", "      <td>22.000000</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2.269432e+06</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.700000e+01</td>\n", "      <td>1.100000e+01</td>\n", "      <td>47.000000</td>\n", "      <td>1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>3.025933e+06</td>\n", "      <td>1.000000e+01</td>\n", "      <td>3.208000e+03</td>\n", "      <td>1.204000e+03</td>\n", "      <td>3208.000000</td>\n", "      <td>1.000000e+00</td>\n", "      <td>3.600000e+02</td>\n", "      <td>1.020000e+02</td>\n", "      <td>9.200000e+01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Row  Problem View  Step Duration (sec)  \\\n", "count  2.270384e+06  2.270384e+06         2.267551e+06   \n", "mean   1.513120e+06  1.092910e+00         1.958364e+01   \n", "std    8.736198e+05  3.448857e-01         4.768345e+01   \n", "min    1.000000e+00  1.000000e+00         0.000000e+00   \n", "25%    7.577408e+05  1.000000e+00         3.000000e+00   \n", "50%    1.511844e+06  1.000000e+00         7.000000e+00   \n", "75%    2.269432e+06  1.000000e+00         1.700000e+01   \n", "max    3.025933e+06  1.000000e+01         3.208000e+03   \n", "\n", "       Correct Step Duration (sec)  Error Step Duration (sec)  \\\n", "count                 1.751638e+06              515913.000000   \n", "mean                  1.171716e+01                  46.292087   \n", "std                   2.645318e+01                  81.817794   \n", "min                   0.000000e+00                   0.000000   \n", "25%                   3.000000e+00                  11.000000   \n", "50%                   5.000000e+00                  22.000000   \n", "75%                   1.100000e+01                  47.000000   \n", "max                   1.204000e+03                3208.000000   \n", "\n", "       Correct First Attempt    Incorrects         Hints      Corrects  \n", "count           2.270384e+06  2.270384e+06  2.270384e+06  2.270384e+06  \n", "mean            7.722359e-01  4.455044e-01  1.184311e-01  1.062878e+00  \n", "std             4.193897e-01  2.000914e+00  6.199071e-01  6.894285e-01  \n", "min             0.000000e+00  0.000000e+00  0.000000e+00  0.000000e+00  \n", "25%             1.000000e+00  0.000000e+00  0.000000e+00  1.000000e+00  \n", "50%             1.000000e+00  0.000000e+00  0.000000e+00  1.000000e+00  \n", "75%             1.000000e+00  0.000000e+00  0.000000e+00  1.000000e+00  \n", "max             1.000000e+00  3.600000e+02  1.020000e+02  9.200000e+01  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "code", "execution_count": 5, "id": "92cc0aab", "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Part of missing values for every column\n", "Row                            0.000000\n", "Anon Student Id                0.000000\n", "Problem Hierarchy              0.000000\n", "Problem Name                   0.000000\n", "Problem View                   0.000000\n", "Step Name                      0.000000\n", "Step Start Time                0.001103\n", "First Transaction Time         0.000000\n", "Correct Transaction Time       0.034757\n", "Step End Time                  0.000000\n", "Step Duration (sec)            0.001248\n", "Correct Step Duration (sec)    0.228484\n", "Error Step Duration (sec)      0.772764\n", "Correct First Attempt          0.000000\n", "Incorrects                     0.000000\n", "Hints                          0.000000\n", "Corrects                       0.000000\n", "KC(Default)                    0.203407\n", "Opportunity(Default)           0.203407\n", "dtype: float64\n"]}], "source": ["print(\"Part of missing values for every column\")\n", "print(data.isnull().sum() / len(data))"]}, {"cell_type": "code", "execution_count": 6, "id": "0187b3b5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the number of records:\n", "2270384\n"]}], "source": ["print(\"the number of records:\")\n", "print(len(data))"]}, {"cell_type": "code", "execution_count": 7, "id": "701b6633", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["how many students are there in the table:\n", "1338\n"]}], "source": ["print(\"how many students are there in the table:\")\n", "print(len(data['Anon Student Id'].unique()))"]}, {"cell_type": "code", "execution_count": 8, "id": "bf7b246f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["how many problems are there in the table:\n", "91913\n"]}], "source": ["print(\"how many problems are there in the table:\")\n", "print(len(data['Problem Name'].unique()))"]}, {"cell_type": "markdown", "id": "e0602c47", "metadata": {}, "source": ["## Sort by <PERSON><PERSON> Id"]}, {"cell_type": "code", "execution_count": 9, "id": "8051cc2b", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-4dbb31\"><g class=\"clips\"><clipPath id=\"clip4dbb31xyplot\" class=\"plotclip\"><rect width=\"486\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4dbb31x\"><rect x=\"134\" y=\"0\" width=\"486\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4dbb31y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip4dbb31xy\"><rect x=\"134\" y=\"100\" width=\"486\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"134\" y=\"100\" width=\"486\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(241.85,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(349.7,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(457.55,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(565.39,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(134,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(134,100)\" clip-path=\"url(#clip4dbb31xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,319.2V312.8H273.88V319.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,311.2V304.8H275.88V311.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,303.2V296.8H276.25V303.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,295.2V288.8H277.17V295.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,287.2V280.8H277.6V287.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,279.2V272.8H277.87V279.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,271.2V264.8H277.93V271.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,263.2V256.8H278.3V263.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,255.2V248.8H279.06V255.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,247.2V240.8H279.17V247.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,239.2V232.8H279.44V239.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,231.2V224.8H279.65V231.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,223.2V216.8H282.4V223.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,215.2V208.8H283.1V215.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,207.2V200.8H284.45V207.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,199.2V192.8H287.42V199.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,191.2V184.8H294.05V191.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,183.2V176.8H294.75V183.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,175.2V168.8H296.58V175.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,167.2V160.8H299.87V167.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,159.2V152.8H301.92V159.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,151.2V144.8H312.38V151.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,143.2V136.8H314.97V143.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,135.2V128.8H316V135.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,127.2V120.8H318.8V127.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,119.2V112.8H321.17V119.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,111.2V104.8H323.65V111.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,103.2V96.8H323.82V103.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,95.2V88.8H325.33V95.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,87.2V80.8H330.45V87.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,79.2V72.8H335.14V79.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,71.2V64.8H350.51V71.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,63.2V56.8H355.63V63.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,55.2V48.8H360.54V55.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,47.2V40.8H368.9V47.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,39.2V32.8H370.68V39.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,31.2V24.8H377.09V31.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,23.2V16.8H411.71V23.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,15.2V8.8H445.47V15.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,7.2V0.8H461.7V7.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(134,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(241.85,0)\">2000</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(349.7,0)\">4000</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(457.55,0)\">6000</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(565.39,0)\">8000</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,416)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271emtbxq8pa-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,400)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">3cjD21W-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,384)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271zbdm1lcgj-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,368)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271swzglvvxm-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,352)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271jonvpgijj-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,336)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">24841uicq-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,320)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">E9dzBix-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,304)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">7OalbuD-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,288)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">a8YLu01-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,272)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271sjweu45ee-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,256)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">MYmjG5R-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,240)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">ug982yk-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,224)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271g8nye4tne-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,208)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271zzbwqtqht-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,192)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271k4y8incfb-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,176)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271tt6j61n7d-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,160)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271rvro73lce-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,144)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">7LZr10z-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,128)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">F713eQN-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"133\" y=\"4.199999999999999\" transform=\"translate(0,112)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">271g7beuc4s1-</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-4dbb31\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Top 40 students by number of steps they have done</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"377\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">count</text></g><g class=\"g-ytitle\" transform=\"translate(2.0654296875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,11.934375000000003,260)\" x=\"11.934375000000003\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Anon Student Id</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ds = data['Anon Student Id'].value_counts().reset_index()\n", "ds.columns = [\n", "    'Anon Student Id',\n", "    'count'\n", "]\n", "ds['Anon Student Id'] = ds['Anon Student Id'].astype(str) + '-'\n", "ds = ds.sort_values('count').tail(40)\n", "\n", "fig = px.bar(\n", "    ds,\n", "    x='count',\n", "    y='Anon Student Id',\n", "    orientation='h',\n", "    title='Top 40 students by number of steps they have done'\n", ")\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "66ef53ad", "metadata": {}, "source": ["## Percent of corrects, hints and incorrects"]}, {"cell_type": "code", "execution_count": 10, "id": "c8f1539c", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-ab78f9\"><g class=\"clips\"/><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M326.5,260l0,-160a160,160 0 1 1 -131.39328010223952,251.30063495931688Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(397.3553145632505,301.5673346872103)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\">65.3%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M326.5,260l-158.20673170751922,23.887863915072586a160,160 0 0 1 158.20673170751922,-183.8878639150726Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(257.470529597744,205.1445497761319)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">27.4%</text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M326.5,260l-131.39328010223954,91.30063495931678a160,160 0 0 1 -26.81345160527968,-67.4127710442442Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(205.26650878242097,312.70512343448286)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\">7.28%</text></g></g></g></g><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-ab78f9\"><g class=\"clips\"/><clipPath id=\"legendab78f9\"><rect width=\"105\" height=\"67\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(582.86,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\" width=\"105\" height=\"67\" x=\"0\" y=\"0\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url(#legendab78f9)\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">corrects</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"99.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">incorrects</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"99.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,52.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">hints</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendpie\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"99.796875\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\" x=\"0\" y=\"0\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Percent of corrects, hints and incorrects</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["count_corrects = data['Corrects'].sum()\n", "count_hints = data['Hints'].sum()\n", "count_incorrects = data['Incorrects'].sum()\n", "\n", "total = count_corrects + count_hints + count_incorrects\n", "\n", "percent_corrects = count_corrects / total\n", "percent_hints = count_hints / total\n", "percent_incorrects = count_incorrects / total\n", "\n", "dfl = [['corrects', percent_corrects], ['hints', percent_hints], ['incorrects', percent_incorrects]]\n", "\n", "df = pd.DataFrame(dfl, columns=['transaction type', 'percent'])\n", "\n", "fig = px.pie(\n", "    df,\n", "    names=['corrects', 'hints', 'incorrects'],\n", "    values='percent',\n", "    title='Percent of corrects, hints and incorrects'\n", ")\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "3b097141", "metadata": {}, "source": ["## Sort by Problem Name"]}, {"cell_type": "code", "execution_count": 11, "id": "6d668c43", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-01a5b2\"><g class=\"clips\"><clipPath id=\"clip01a5b2xyplot\" class=\"plotclip\"><rect width=\"400\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip01a5b2x\"><rect x=\"220\" y=\"0\" width=\"400\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip01a5b2y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip01a5b2xy\"><rect x=\"220\" y=\"100\" width=\"400\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"220\" y=\"100\" width=\"400\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(293.08,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(366.15,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(439.23,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(512.31,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(585.38,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(220,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(220,100)\" clip-path=\"url(#clip01a5b2xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,319.2V312.8H121.31V319.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,311.2V304.8H121.67V311.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,303.2V296.8H122.77V303.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,295.2V288.8H123.5V295.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,287.2V280.8H125.69V287.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,279.2V272.8H127.52V279.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,271.2V264.8H127.88V271.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,263.2V256.8H128.25V263.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,255.2V248.8H128.62V255.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,247.2V240.8H129.71V247.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,239.2V232.8H139.58V239.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,231.2V224.8H150.17V231.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,223.2V216.8H153.1V223.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,215.2V208.8H156.02V215.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,207.2V200.8H157.48V207.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,199.2V192.8H157.85V199.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,191.2V184.8H162.23V191.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,183.2V176.8H164.42V183.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,175.2V168.8H165.52V175.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,167.2V160.8H170.27V167.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,159.2V152.8H172.46V159.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,151.2V144.8H174.65V151.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,143.2V136.8H174.65V143.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,135.2V128.8H177.94V135.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,127.2V120.8H179.04V127.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,119.2V112.8H183.79V119.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,111.2V104.8H184.15V111.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,103.2V96.8H184.15V103.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,95.2V88.8H188.17V95.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,87.2V80.8H195.85V87.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,79.2V72.8H196.94V79.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,71.2V64.8H203.15V71.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,63.2V56.8H211.56V63.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,55.2V48.8H231.65V55.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,47.2V40.8H239.69V47.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,39.2V32.8H241.15V39.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,31.2V24.8H308.38V31.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,23.2V16.8H338.35V23.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,15.2V8.8H340.54V15.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,7.2V0.8H380V7.2Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(220,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(293.08,0)\">200</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(366.15,0)\">400</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(439.23,0)\">600</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(512.31,0)\">800</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(585.38,0)\">1000</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,416)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">1PTFB11-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,400)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DISTFB03_SP-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,384)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DIST11_SP-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,368)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROP06-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,352)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DIST09_SP-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,336)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">DIST10_SP-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,320)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EG4-CONSTANT 3(x+2) = 15-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,304)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">JAN06-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,288)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">FEB04-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,272)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">NOV13-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,256)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROP03-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,240)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROP12-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,224)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">FEB11-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,208)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIO2-001-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,192)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROP05-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,176)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">JAN09-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,160)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROP04-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,144)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EG1-CONSTANT 7(8+2)-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,128)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">LDEMO_WSLVR-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"219\" y=\"4.199999999999999\" transform=\"translate(0,112)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">L5FB16-</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-01a5b2\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Top 40 useful problem</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"420\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">count</text></g><g class=\"g-ytitle\" transform=\"translate(2.3154296875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,11.684374999999989,260)\" x=\"11.684374999999989\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Name</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["storeProblemCount = [1]\n", "storeProblemName = [data['Problem Name'][0]]\n", "currentProblemName = data['Problem Name'][0]\n", "currentStepName = [data['Step Name'][0]]\n", "lastIndex = 0\n", "\n", "for i in range(1, len(data), 1):\n", "    pbNameI = data['Problem Name'][i]\n", "    stNameI = data['Step Name'][i]\n", "    if pbNameI != data['Problem Name'][lastIndex]:\n", "        currentStepName = [stNameI]\n", "        currentProblemName = pbNameI\n", "        if pbNameI not in storeProblemName:\n", "            storeProblemName.append(pbNameI)\n", "            storeProblemCount.append(1)\n", "        else:\n", "            storeProblemCount[storeProblemName.index(pbNameI)] += 1\n", "        lastIndex = i\n", "    elif stN<PERSON>I not in currentStepName:\n", "        currentStepName.append(stNameI)\n", "        lastIndex = i\n", "    else:\n", "        currentStepName = [stNameI]\n", "        storeProblemCount[storeProblemName.index(pbNameI)] += 1\n", "        lastIndex = i\n", "\n", "dfData = {\n", "    'Problem Name': storeProblemName,\n", "    'count': storeProblemCount\n", "}\n", "df = pd.DataFrame(dfData).sort_values('count').tail(40)\n", "df[\"Problem Name\"] += '-'\n", "\n", "fig = px.bar(\n", "    df,\n", "    x='count',\n", "    y='Problem Name',\n", "    orientation='h',\n", "    title='Top 40 useful problem'\n", ")\n", "fig.show(\"svg\")"]}, {"cell_type": "code", "execution_count": 12, "id": "1b965aa4", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-018bd2\"><g class=\"clips\"><clipPath id=\"clip018bd2xyplot\" class=\"plotclip\"><rect width=\"268\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip018bd2x\"><rect x=\"352\" y=\"0\" width=\"268\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip018bd2y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip018bd2xy\"><rect x=\"352\" y=\"100\" width=\"268\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"352\" y=\"100\" width=\"268\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(484.89,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(617.78,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(352,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(352,100)\" clip-path=\"url(#clip018bd2xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H223.25V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(191.6262500000002,314.03999999999996)scale(0.4533333333333303)\">SYLT-2X&amp;YGE-2X+9-</text></g><g class=\"point\"><path d=\"M0,302.4V289.6H224.38V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(202.36125000000013,298.03999999999996)scale(0.4533333333333303)\">ROOTS1-001-</text></g><g class=\"point\"><path d=\"M0,286.4V273.6H225.38V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(197.13500000000016,282.03999999999996)scale(0.4533333333333303)\">SY=2X&amp;Y=-3X+5-</text></g><g class=\"point\"><path d=\"M0,270.4V257.6H225.71V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(194.90791666666686,266.03999999999996)scale(0.4533333333333303)\">PROBABILITY1-006-</text></g><g class=\"point\"><path d=\"M0,254.4V241.6H226.05V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(173.79958333333326,250.04)scale(0.4533333333333341)\">EG-RE-DISTRIB-05 (2x+4)/(3x+6)-</text></g><g class=\"point\"><path d=\"M0,238.4V225.6H227.43V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(196.62791666666664,234.04)scale(0.4533333333333341)\">PROBABILITY1-070-</text></g><g class=\"point\"><path d=\"M0,222.4V209.6H227.5V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(194.5091666666666,218.04)scale(0.4533333333333341)\">G3X-YLE5&amp;3X-YGE15-</text></g><g class=\"point\"><path d=\"M0,206.4V193.6H228.09V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(214.5570833333333,202.04)scale(0.4533333333333341)\">BUSES-</text></g><g class=\"point\"><path d=\"M0,190.4V177.6H228.79V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(196.63499999999993,186.04)scale(0.4533333333333341)\">PEANUTS-CASHEWS-</text></g><g class=\"point\"><path d=\"M0,174.4V161.6H230.68V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(203.4620833333333,170.04)scale(0.4533333333333341)\">EXPONENT2-012-</text></g><g class=\"point\"><path d=\"M0,158.4V145.6H230.9V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(219.53458333333333,154.04)scale(0.4533333333333341)\">TVS3-</text></g><g class=\"point\"><path d=\"M0,142.4V129.6H233.46V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(202.65791666666664,138.04)scale(0.4533333333333341)\">PROBABILITY5-001-</text></g><g class=\"point\"><path d=\"M0,126.4V113.6H233.86V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(206.64208333333332,122.04)scale(0.4533333333333341)\">EXPONENT3-071-</text></g><g class=\"point\"><path d=\"M0,110.4V97.6H236.46V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(209.2420833333333,106.04)scale(0.4533333333333341)\">EXPONENT2-046-</text></g><g class=\"point\"><path d=\"M0,94.4V81.6H238.31V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(211.0920833333333,90.04)scale(0.4533333333333341)\">EXPONENT5-001-</text></g><g class=\"point\"><path d=\"M0,78.4V65.6H244.38V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(213.57791666666662,74.04)scale(0.4533333333333341)\">PROBABILITY2-001-</text></g><g class=\"point\"><path d=\"M0,62.4V49.6H245.79V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(223.54458333333332,58.04)scale(0.45333333333333314)\">GLFM-BUSES-</text></g><g class=\"point\"><path d=\"M0,46.4V33.6H248.35V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(174.09875000000002,42.04)scale(0.45333333333333314)\">EG-EPS01-CONSTANT (4(x^2)*y^4)(3(x^3)*y^3)-</text></g><g class=\"point\"><path d=\"M0,30.4V17.6H251.79V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(220.98791666666668,26.04)scale(0.45333333333333314)\">PROBABILITY6-002-</text></g><g class=\"point\"><path d=\"M0,14.4V1.6H254.6V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(223.79791666666665,10.04)scale(0.45333333333333337)\">PROBABILITY6-001-</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(352,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(484.89,0)\">0.5</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(617.78,0)\">1</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">SYLT-2X&amp;YGE-2X+9-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">ROOTS1-001-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">SY=2X&amp;Y=-3X+5-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROBABILITY1-006-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EG-RE-DISTRIB-05 (2x+4)/(3x+6)-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROBABILITY1-070-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">G3X-YLE5&amp;3X-YGE15-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">BUSES-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PEANUTS-CASHEWS-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EXPONENT2-012-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">TVS3-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROBABILITY5-001-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EXPONENT3-071-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EXPONENT2-046-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EXPONENT5-001-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROBABILITY2-001-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">GLFM-BUSES-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EG-EPS01-CONSTANT (4(x^2)*y^4)(3(x^3)*y^3)-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROBABILITY6-002-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"351\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">PROBABILITY6-001-</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-018bd2\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 10px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Correct rate of each problem (top 20)  (total transactions of each problem are required to be more than 500)</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"486\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Correct rate</text></g><g class=\"g-ytitle\" transform=\"translate(2.1279296875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,11.871874999999989,260)\" x=\"11.871874999999989\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Name</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-c47b1a\"><g class=\"clips\"><clipPath id=\"clipc47b1axyplot\" class=\"plotclip\"><rect width=\"348\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc47b1ax\"><rect x=\"272\" y=\"0\" width=\"348\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc47b1ay\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc47b1axy\"><rect x=\"272\" y=\"100\" width=\"348\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"272\" y=\"100\" width=\"348\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(368.54,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(465.08000000000004,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(561.62,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(272,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(272,100)\" clip-path=\"url(#clipc47b1axyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H72.72V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(46.36625000000015,314.03999999999996)scale(0.4533333333333303)\">RATIONAL1-091-</text></g><g class=\"point\"><path d=\"M0,302.4V289.6H76.8V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(50.44625000000015,298.03999999999996)scale(0.4533333333333303)\">RATIONAL1-165-</text></g><g class=\"point\"><path d=\"M0,286.4V273.6H77.99V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(51.636250000000146,282.03999999999996)scale(0.4533333333333303)\">RATIONAL1-034-</text></g><g class=\"point\"><path d=\"M0,270.4V257.6H94.19V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(67.83625000000015,266.03999999999996)scale(0.4533333333333303)\">RATIONAL1-058-</text></g><g class=\"point\"><path d=\"M0,254.4V241.6H95.61V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(69.25624999999997,250.04)scale(0.4533333333333341)\">RATIONAL1-075-</text></g><g class=\"point\"><path d=\"M0,238.4V225.6H98.14V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(71.78624999999997,234.04)scale(0.4533333333333341)\">RATIONAL1-035-</text></g><g class=\"point\"><path d=\"M0,222.4V209.6H115.7V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(89.34624999999997,218.04)scale(0.4533333333333341)\">RATIONAL1-281-</text></g><g class=\"point\"><path d=\"M0,206.4V193.6H117.54V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(91.18624999999997,202.04)scale(0.4533333333333341)\">RATIONAL1-261-</text></g><g class=\"point\"><path d=\"M0,190.4V177.6H124.43V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(98.07624999999997,186.04)scale(0.4533333333333341)\">RATIONAL1-177-</text></g><g class=\"point\"><path d=\"M0,174.4V161.6H132.06V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(105.70624999999997,170.04)scale(0.4533333333333341)\">RATIONAL1-064-</text></g><g class=\"point\"><path d=\"M0,158.4V145.6H135.39V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(109.03624999999995,154.04)scale(0.4533333333333341)\">RATIONAL1-147-</text></g><g class=\"point\"><path d=\"M0,142.4V129.6H135.57V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(109.21624999999996,138.04)scale(0.4533333333333341)\">RATIONAL1-021-</text></g><g class=\"point\"><path d=\"M0,126.4V113.6H136.96V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(110.60624999999997,122.04)scale(0.4533333333333341)\">RATIONAL1-121-</text></g><g class=\"point\"><path d=\"M0,110.4V97.6H162.16V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(135.80624999999995,106.04)scale(0.4533333333333341)\">RATIONAL1-008-</text></g><g class=\"point\"><path d=\"M0,94.4V81.6H171.5V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(145.14624999999995,90.04)scale(0.4533333333333341)\">RATIONAL1-288-</text></g><g class=\"point\"><path d=\"M0,78.4V65.6H240.5V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(214.14624999999995,74.04)scale(0.4533333333333341)\">RATIONAL1-109-</text></g><g class=\"point\"><path d=\"M0,62.4V49.6H304.79V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(287.89958333333334,58.04)scale(0.45333333333333314)\">BH1T31B-</text></g><g class=\"point\"><path d=\"M0,46.4V33.6H318.11V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(300.6670833333334,42.04)scale(0.45333333333333314)\">RXMX_3C-</text></g><g class=\"point\"><path d=\"M0,30.4V17.6H326.37V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(270.2875,26.04)scale(0.45333333333333314)\">EG-RE-CANCEL-04 ((x-9)^3)/(x-9)^4-</text></g><g class=\"point\"><path d=\"M0,14.4V1.6H330.6V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(304.24625000000003,10.04)scale(0.45333333333333337)\">RATIONAL2-205-</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(272,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(368.54,0)\">0.1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(465.08000000000004,0)\">0.2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(561.62,0)\">0.3</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,412)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-091-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,396)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-165-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,380)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-034-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,364)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-058-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,348)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-075-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,332)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-035-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,316)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-281-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,300)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-261-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,284)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-177-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,268)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-064-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,252)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-147-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,236)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-021-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,220)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-121-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,204)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-008-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,188)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-288-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,172)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL1-109-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,156)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">BH1T31B-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,140)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RXMX_3C-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,124)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">EG-RE-CANCEL-04 ((x-9)^3)/(x-9)^4-</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"271\" y=\"4.199999999999999\" transform=\"translate(0,108)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">RATIONAL2-205-</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-c47b1a\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 10px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Correct rate of each problem (bottom 20)  (total transactions of each problem are required to be more than 500)</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"446\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Correct rate</text></g><g class=\"g-ytitle\" transform=\"translate(1.9873046875,0)\"><text class=\"ytitle\" transform=\"rotate(-90,12.012499999999989,260)\" x=\"12.012499999999989\" y=\"260\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Name</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data['total transactions'] = data['Incorrects'] + data['Hints'] + data['Corrects']\n", "df1 = data.groupby('Problem Name')['total transactions'].sum().reset_index()\n", "df2 = data.groupby('Problem Name')['Corrects'].sum().reset_index()\n", "df1['Corrects'] = df2['Corrects']\n", "df1['Correct rate'] = df1['Corrects'] / df1['total transactions']\n", "\n", "df1 = df1.sort_values('total transactions')\n", "count = 0\n", "standard = 500\n", "for i in df1['total transactions']:\n", "    if i > standard:\n", "        count += 1\n", "df1 = df1.tail(count)\n", "\n", "df1 = df1.sort_values('Correct rate')\n", "\n", "df1['Problem Name'] = df1['Problem Name'].astype(str) + \"-\"\n", "\n", "df_px = df1.tail(20)\n", "\n", "fig = px.bar(\n", "    df_px,\n", "    x='Correct rate',\n", "    y='Problem Name',\n", "    orientation='h',\n", "    title='Correct rate of each problem (top 20)  (total transactions of \\\n", "each problem are required to be more than 500)',\n", "    text='Problem Name'\n", ")\n", "fig.update_layout(title_font_size=10)\n", "fig.show(\"svg\")\n", "\n", "df_px = df1.head(20)\n", "\n", "fig = px.bar(\n", "    df_px,\n", "    x='Correct rate',\n", "    y='Problem Name',\n", "    orientation='h',\n", "    title='Correct rate of each problem (bottom 20)  (total transactions of \\\n", "each problem are required to be more than 500)',\n", "    text='Problem Name'\n", ")\n", "fig.update_layout(title_font_size=10)\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "2bee6e04", "metadata": {}, "source": ["These two figures present the correct rate of problems. Problems with low correct rate deserve more attention from teachers and students."]}, {"cell_type": "markdown", "id": "0047e839", "metadata": {}, "source": ["## Sort by <PERSON>"]}, {"cell_type": "code", "execution_count": 13, "id": "c6e910e0", "metadata": {"scrolled": false}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-c432a8\"><g class=\"clips\"><clipPath id=\"clipc432a8xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc432a8x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc432a8y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clipc432a8xy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(183.89,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(287.78,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(391.66,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(495.55,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(599.44,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(80,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url(#clipc432a8xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H472.41V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(318.42875000000106,314.03999999999996)scale(0.4533333333333303)\">[Rule: RF right ([SolverOperation rf],{components with property canReduceFractionsNoMultWhole of right})]-</text></g><g class=\"point\"><path d=\"M0,302.4V289.6H473.99V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(423.43250000000035,298.03999999999996)scale(0.4533333333333303)\">Enter second extreme in equation-</text></g><g class=\"point\"><path d=\"M0,286.4V273.6H474.53V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(381.0120833333339,282.03999999999996)scale(0.4533333333333303)\">Proportional-Constant-Expression-Gla:Student-Modeling-Analysis-</text></g><g class=\"point\"><path d=\"M0,270.4V257.6H475.6V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(430.7375000000003,266.03999999999996)scale(0.4533333333333303)\">Enter Calculated value of rate-</text></g><g class=\"point\"><path d=\"M0,254.4V241.6H475.65V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(410.9045833333332,250.04)scale(0.4533333333333341)\">[SkillRule: Combine like terms, no var; CLT]-</text></g><g class=\"point\"><path d=\"M0,238.4V225.6H477.62V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(439.9045833333333,234.04)scale(0.4533333333333341)\">Enter square of leg label-</text></g><g class=\"point\"><path d=\"M0,222.4V209.6H478.83V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(425.0991666666666,218.04)scale(0.4533333333333341)\">Compare medians - removed outlier-</text></g><g class=\"point\"><path d=\"M0,206.4V193.6H480.33V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(421.2866666666666,202.04)scale(0.4533333333333341)\">Enter number of total outcomes in table-</text></g><g class=\"point\"><path d=\"M0,190.4V177.6H484.32V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(447.2491666666666,186.04)scale(0.4533333333333341)\">Find square of given leg-</text></g><g class=\"point\"><path d=\"M0,174.4V161.6H487.56V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(435.8762499999999,170.04)scale(0.4533333333333341)\">Enter ratio quantity to right of \"to\"-</text></g><g class=\"point\"><path d=\"M0,158.4V145.6H487.75V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(435.0887499999999,154.04)scale(0.4533333333333341)\">Enter fractional probability of event-</text></g><g class=\"point\"><path d=\"M0,142.4V129.6H488.25V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(408.9766666666666,138.04)scale(0.4533333333333341)\">[SkillRule: Select Multiply; {MT; MT no fraction coeff}]-</text></g><g class=\"point\"><path d=\"M0,126.4V113.6H488.57V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(397.98458333333315,122.04)scale(0.4533333333333341)\">Write base of exponential from given whole number as product-</text></g><g class=\"point\"><path d=\"M0,110.4V97.6H488.7V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(411.1974999999999,106.04)scale(0.4533333333333341)\">Write decimal multiplier from given scientific notation-</text></g><g class=\"point\"><path d=\"M0,94.4V81.6H489.46V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(435.80708333333325,90.04)scale(0.4533333333333341)\">Enter ratio quantity to right of colon-</text></g><g class=\"point\"><path d=\"M0,78.4V65.6H489.9V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(470.87749999999994,74.04)scale(0.4533333333333341)\">PM-ROW-1-</text></g><g class=\"point\"><path d=\"M0,62.4V49.6H504.18V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(468.115,58.04)scale(0.45333333333333314)\">Changing axis intervals-</text></g><g class=\"point\"><path d=\"M0,46.4V33.6H507.48V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(487.88375,42.04)scale(0.45333333333333314)\">unspecified-</text></g><g class=\"point\"><path d=\"M0,30.4V17.6H511.04V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(431.4125,26.04)scale(0.45333333333333314)\">[Rule: unnec-elems ([SolverOperation unnec-elems],)]-</text></g><g class=\"point\"><path d=\"M0,14.4V1.6H513V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(481.42583333333334,10.04)scale(0.45333333333333337)\">Select second event-</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(80,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(183.89,0)\">0.2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(287.78,0)\">0.4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(391.66,0)\">0.6</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(495.55,0)\">0.8</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(599.44,0)\">1</text></g></g><g class=\"yaxislayer-above\"/><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-c432a8\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 10px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Correct rate of each KC(Default) (top 20)  (total transactions of each KC are required to be more than 300)</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">correct rate</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"500\" style=\"\" viewBox=\"0 0 700 500\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"500\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-3fbae4\"><g class=\"clips\"><clipPath id=\"clip3fbae4xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip3fbae4x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"500\"/></clipPath><clipPath class=\"axesclip\" id=\"clip3fbae4y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"320\"/></clipPath><clipPath class=\"axesclip\" id=\"clip3fbae4xy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"320\"/></clipPath></g><g class=\"gradients\"/><g class=\"patterns\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"320\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"><path class=\"xgrid crisp\" transform=\"translate(155.53,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(231.07,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(306.6,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(382.14,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(457.67,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(533.21,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"xgrid crisp\" transform=\"translate(608.74,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g><g class=\"y\"/></g><g class=\"zerolinelayer\"><path class=\"xzl zl crisp\" transform=\"translate(80,0)\" d=\"M0,100v320\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url(#clip3fbae4xyplot)\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0,318.4V305.6H0V318.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,302.4V289.6H0V302.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M0,286.4V273.6H21.26V286.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(140.5399999999996,283.84)scale(0.8533333333333303)\">unknown bug element~~CLT-ROW-1-COEFF-</text></g><g class=\"point\"><path d=\"M0,270.4V257.6H21.87V270.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(122.09666666666632,267.84)scale(0.8533333333333303)\">unknown bug element~~CLT-ROW-1-</text></g><g class=\"point\"><path d=\"M0,254.4V241.6H216.89V254.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(164.7387499999999,250.04)scale(0.4533333333333341)\">Plot terminating improper fractions-</text></g><g class=\"point\"><path d=\"M0,238.4V225.6H300.62V238.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(259.82333333333327,234.04)scale(0.4533333333333341)\">Plot decimal - thousandths-</text></g><g class=\"point\"><path d=\"M0,222.4V209.6H314.21V222.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(267.33583333333326,218.04)scale(0.4533333333333341)\">Plot terminating mixed number-</text></g><g class=\"point\"><path d=\"M0,206.4V193.6H335.71V206.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(302.5349999999999,202.04)scale(0.4533333333333341)\">Plot imperfect radical-</text></g><g class=\"point\"><path d=\"M0,190.4V177.6H344.19V190.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(304.68958333333325,186.04)scale(0.4533333333333341)\">Plot decimal - hundredths-</text></g><g class=\"point\"><path d=\"M0,174.4V161.6H352.82V174.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(325.34,170.04)scale(0.4533333333333341)\">Setting the slope-</text></g><g class=\"point\"><path d=\"M0,158.4V145.6H362.59V158.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(315.2483333333332,154.04)scale(0.4533333333333341)\">Plot terminating proper fraction-</text></g><g class=\"point\"><path d=\"M0,142.4V129.6H368.62V142.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(348.06041666666664,138.04)scale(0.4533333333333341)\">Plot percent-</text></g><g class=\"point\"><path d=\"M0,126.4V113.6H403.34V126.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(349.66583333333324,122.04)scale(0.4533333333333341)\">Plot non-terminating proper fraction-</text></g><g class=\"point\"><path d=\"M0,110.4V97.6H439V110.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(381.93999999999994,106.04)scale(0.4533333333333341)\">Plot non-terminating improper fraction-</text></g><g class=\"point\"><path d=\"M0,94.4V81.6H453.21V94.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(422.29458333333326,90.04)scale(0.4533333333333341)\">Entering slope, GLF-</text></g><g class=\"point\"><path d=\"M0,78.4V65.6H465.86V78.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(428.9095833333333,74.04)scale(0.4533333333333341)\">Placing coordinate point-</text></g><g class=\"point\"><path d=\"M0,62.4V49.6H470.41V62.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(437.83000000000004,58.04)scale(0.45333333333333314)\">Plot decimal - tenths-</text></g><g class=\"point\"><path d=\"M0,46.4V33.6H472.76V46.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(429.83125,42.04)scale(0.45333333333333314)\">Finding the intersection, SIF-</text></g><g class=\"point\"><path d=\"M0,30.4V17.6H482V30.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(435.7066666666667,26.04)scale(0.45333333333333314)\">Finding the intersection, Mixed-</text></g><g class=\"point\"><path d=\"M0,14.4V1.6H513V14.4Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(438.3308333333333,10.04)scale(0.45333333333333337)\">[Rule: CLT nested, no LCD ([SolverOperation clt],)]-</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" transform=\"translate(80,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(155.53,0)\">0.05</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(231.07,0)\">0.1</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(306.6,0)\">0.15</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(382.14,0)\">0.2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(457.67,0)\">0.25</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(533.21,0)\">0.3</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"433\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre; opacity: 1;\" transform=\"translate(608.74,0)\">0.35</text></g></g><g class=\"yaxislayer-above\"/><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"iciclelayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-3fbae4\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 10px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Correct rate of each KC(Default) (bottom 20)  (total transactions of each KC are required to be more than 300)</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"460.8\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">correct rate</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["data.dropna(subset=['<PERSON>(Default)'], inplace=True)\n", "\n", "data['total transactions'] = data['Corrects'] + data['Hints'] + data['Incorrects']\n", "df1 = data.groupby('<PERSON>(Default)')['total transactions'].sum().reset_index()\n", "df2 = data.groupby('<PERSON>(<PERSON><PERSON>ult)')['Corrects'].sum().reset_index()\n", "df1['Corrects'] = df2['Corrects']\n", "df1['correct rate'] = df1['Corrects'] / df1['total transactions']\n", "\n", "count = 0\n", "standard = 300\n", "for i in df1['total transactions']:\n", "    if i > standard:\n", "        count += 1\n", "df1 = df1.sort_values('total transactions').tail(count)\n", "\n", "df1 = df1.sort_values('correct rate')\n", "\n", "df1['<PERSON>(<PERSON><PERSON><PERSON>)'] = df1['<PERSON>(<PERSON><PERSON><PERSON>)'].astype(str) + '-'\n", "\n", "df_px = df1.tail(20)\n", "\n", "fig = px.bar(\n", "    df_px,\n", "    x='correct rate',\n", "    y='<PERSON>(<PERSON><PERSON><PERSON>)',\n", "    orientation='h',\n", "    title='Correct rate of each KC(Default) (top 20)  (total transactions of \\\n", "each KC are required to be more than 300)',\n", "    text='<PERSON>(<PERSON><PERSON><PERSON>)'\n", ")\n", "fig.update_yaxes(visible=False)\n", "fig.update_layout(title_font_size=10)\n", "fig.show(\"svg\")\n", "\n", "df_px = df1.head(20)\n", "\n", "fig = px.bar(\n", "    df_px,\n", "    x='correct rate',\n", "    y='<PERSON>(<PERSON><PERSON><PERSON>)',\n", "    orientation='h',\n", "    title='Correct rate of each KC(Default) (bottom 20)  (total transactions of \\\n", "each KC are required to be more than 300)',\n", "    text='<PERSON>(<PERSON><PERSON><PERSON>)'\n", ")\n", "fig.update_yaxes(visible=False)\n", "fig.update_layout(title_font_size=10)\n", "fig.show(\"svg\")"]}, {"cell_type": "markdown", "id": "0feef8a1", "metadata": {}, "source": ["These two figures present the correct rate of KCs. KCs with low correct rate deserve more attention from teachers and students."]}, {"cell_type": "markdown", "id": "22d99527", "metadata": {}, "source": ["## Postscript"]}, {"cell_type": "markdown", "id": "09bc0903", "metadata": {}, "source": ["Given that the whole data package is composed of 5 data sets and data files in these 5 data sets that can be used to conduct data analysis share the same data format, the following analysis based on \"algebra_2006_2007_train\" is just an example of data analysis on KDD Cup, and the code can be used to analyse other data files with some small changes on the file path and column names.\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}