{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#  OLI data in fall, 2011（step）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import pandas as pd\n", "import numpy as np\n", "# global configuration: show every rows and cols\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('max_colwidth',None)\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. Data Description\n", "## 1.1 Column Description"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Field</th>\n", "      <th>Annotation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>A row counter.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON>ple</td>\n", "      <td>The sample that includes this step. If you select more than one sample to                    export, steps that occur in more than one sample will be duplicated in the export.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Anon Student ID</td>\n", "      <td>The student that performed the step.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Problem Hierarchy</td>\n", "      <td>The location in the curriculum hierarchy where this step occurs.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Problem Name</td>\n", "      <td>The name of the problem in which the step occurs.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Problem View</td>\n", "      <td>The number of times the student encountered the problem so far. This counter increases                     with each instance of the same problem.                    Note that problem view increases regardless of whether or not the step was                    encountered in previous problem views. For example, a step can have a \"Problem View\" of                    \"3\", indicating the problem was viewed three times by this student, but that same step                    need not have been encountered by that student in all instances of the problem. If this                    number does not increase as you expect it to, it might be that DataShop has identified                    similar problems as distinct: two problems with the same \"Problem Name\" are considered                    different \"problems\" by DataShop if the following logged values are not identical:                    problem name, context, tutor_flag (whether or not the problem or activity is tutored)                    and \"other\" field. For more on the logging of these fields, see the  description of the                    \"problem\" element in the Guide to the Tutor Message Format. For more detail                    on how problem view is determined, see Determining                     Problem View.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Step Name</td>\n", "      <td>Formed by concatenating the \"selection\" and \"action\". Also see the glossary entry for \"step\".</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Step Start Time</td>\n", "      <td>The step start time is determined one of three ways:                                            If it's the first step of the problem, the step start time is the same as the problem start time                        If it's a subsequent step, then the step start time is the time of the preceding transaction,                         if that transaction is within 10 minutes.                        If it's a subsequent step and the elapsed time between the previous transaction and the first                         transaction of this step is more than 10 minutes, then the step start time is set to null as it's                         considered an unreliable value.                                        For a visual example, see the Examples page.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>First Transaction Time</td>\n", "      <td>The time of the first transaction toward the step.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Correct Transaction Time</td>\n", "      <td>The time of the correct attempt toward the step, if there was one.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Step End Time</td>\n", "      <td>The time of the last transaction toward the step.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Step Duration (sec)</td>\n", "      <td>The elapsed time of the step in seconds, calculated by adding all of the                    durations for transactions that were attributed to the step. See the glossary entry for more detail. This column                    was previously labeled \"Assistance Time\". It differs from \"Assistance Time\" in that its                    values are derived by summing transaction durations, not finding the difference between                    only two points in time (step start time and the last correct attempt).</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Correct Step Duration (sec)</td>\n", "      <td>The step duration if the first attempt for the step was correct. This might                    also be described as \"reaction time\" since it's the duration of time from the previous                    transaction or problem start event to the correct attempt. See the glossary entry for more detail.                    This column was previously labeled \"Correct Step Time (sec)\".</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Error Step Duration (sec)</td>\n", "      <td>The step duration if the first attempt for the step was an error (incorrect                    attempt or hint request).</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>First Attempt</td>\n", "      <td>The tutor's response to the student's first attempt on the step. Example values                    are \"hint\", \"correct\", and \"incorrect\".</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Incorrects</td>\n", "      <td>Total number of incorrect attempts by the student on the step.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Hints</td>\n", "      <td>Total number of hints requested by the student for the step.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Corrects</td>\n", "      <td>Total correct attempts by the student for the step. (Only increases if the step                    is encountered more than once.)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Condition</td>\n", "      <td>The name and type of the condition the student is assigned to. In the case of a                    student assigned to multiple conditions (factors in a factorial design), condition names                    are separated by a comma and space. This differs from the transaction format, which                    optionally has \"Condition Name\" and \"Condition Type\" columns.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td><PERSON> (model_name)</td>\n", "      <td>(Only shown when the \"Knowledge Components\" option is selected.) Knowledge                    component(s) associated with the correct performance of this step. In the case of                     multiple KCs assigned to a single step, KC names are separated by two tildes (\"~~\").</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Opportunity (model_name)</td>\n", "      <td>(Only shown when the \"Knowledge Components\" option is selected.) An opportunity                    is the first chance on a step for a student to demonstrate whether he or she has learned                    the associated knowledge component. Opportunity number is therefore a count that                    increases by one each time the student encounters a step with the listed knowledge                    component. In the case of multiple KCs assigned to a single step, opportunity                    number values are separated by two tildes (\"~~\") and are given in the same order as                     the KC names. Check here to see how opportunity count                    is computed when Event Type column is present in transaction data.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Predicted Error Rate (model_name)</td>\n", "      <td>A hypothetical error rate based on the Additive Factor Model (AFM)                    algorithm. A value of \"1\" is a prediction that a student's first attempt will be an                    error (incorrect attempt or hint request); a value of \"0\" is a prediction that the                    student's first attempt will be correct. For specifics, see below \"Predicted Error Rate\" and how it's calculated.                     In the case of multiple KCs assigned to a single step, Datashop implements a compensatory sum across all of the KCs,                    thus a single value of predicted error rate is provided (i.e., the same predicted error rate for each KC assigned to a step).                     For more detail on Datashop's implementation for multi-skilled step, see                      Model Values page.</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                Field  \\\n", "0                                 Row   \n", "1                              Sample   \n", "2                     Anon Student ID   \n", "3                   Problem Hierarchy   \n", "4                        Problem Name   \n", "5                        Problem View   \n", "6                           Step Name   \n", "7                     Step Start Time   \n", "8              First Transaction Time   \n", "9            Correct Transaction Time   \n", "10                      Step End Time   \n", "11                Step Duration (sec)   \n", "12        Correct Step Duration (sec)   \n", "13          Error Step Duration (sec)   \n", "14                      First Attempt   \n", "15                         Incorrects   \n", "16                              Hints   \n", "17                           Corrects   \n", "18                          Condition   \n", "19                    KC (model_name)   \n", "20           Opportunity (model_name)   \n", "21  Predicted Error Rate (model_name)   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               Annotation  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          A row counter.  \n", "1                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         The sample that includes this step. If you select more than one sample to                    export, steps that occur in more than one sample will be duplicated in the export.  \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    The student that performed the step.  \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        The location in the curriculum hierarchy where this step occurs.  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       The name of the problem in which the step occurs.  \n", "5   The number of times the student encountered the problem so far. This counter increases                     with each instance of the same problem.                    Note that problem view increases regardless of whether or not the step was                    encountered in previous problem views. For example, a step can have a \"Problem View\" of                    \"3\", indicating the problem was viewed three times by this student, but that same step                    need not have been encountered by that student in all instances of the problem. If this                    number does not increase as you expect it to, it might be that DataShop has identified                    similar problems as distinct: two problems with the same \"Problem Name\" are considered                    different \"problems\" by DataShop if the following logged values are not identical:                    problem name, context, tutor_flag (whether or not the problem or activity is tutored)                    and \"other\" field. For more on the logging of these fields, see the  description of the                    \"problem\" element in the Guide to the Tutor Message Format. For more detail                    on how problem view is determined, see Determining                     Problem View.  \n", "6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Formed by concatenating the \"selection\" and \"action\". Also see the glossary entry for \"step\".  \n", "7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      The step start time is determined one of three ways:                                            If it's the first step of the problem, the step start time is the same as the problem start time                        If it's a subsequent step, then the step start time is the time of the preceding transaction,                         if that transaction is within 10 minutes.                        If it's a subsequent step and the elapsed time between the previous transaction and the first                         transaction of this step is more than 10 minutes, then the step start time is set to null as it's                         considered an unreliable value.                                        For a visual example, see the Examples page.  \n", "8                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      The time of the first transaction toward the step.  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      The time of the correct attempt toward the step, if there was one.  \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      The time of the last transaction toward the step.  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          The elapsed time of the step in seconds, calculated by adding all of the                    durations for transactions that were attributed to the step. See the glossary entry for more detail. This column                    was previously labeled \"Assistance Time\". It differs from \"Assistance Time\" in that its                    values are derived by summing transaction durations, not finding the difference between                    only two points in time (step start time and the last correct attempt).  \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           The step duration if the first attempt for the step was correct. This might                    also be described as \"reaction time\" since it's the duration of time from the previous                    transaction or problem start event to the correct attempt. See the glossary entry for more detail.                    This column was previously labeled \"Correct Step Time (sec)\".  \n", "13                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               The step duration if the first attempt for the step was an error (incorrect                    attempt or hint request).  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             The tutor's response to the student's first attempt on the step. Example values                    are \"hint\", \"correct\", and \"incorrect\".  \n", "15                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Total number of incorrect attempts by the student on the step.  \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Total number of hints requested by the student for the step.  \n", "17                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Total correct attempts by the student for the step. (Only increases if the step                    is encountered more than once.)  \n", "18                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    The name and type of the condition the student is assigned to. In the case of a                    student assigned to multiple conditions (factors in a factorial design), condition names                    are separated by a comma and space. This differs from the transaction format, which                    optionally has \"Condition Name\" and \"Condition Type\" columns.  \n", "19                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               (Only shown when the \"Knowledge Components\" option is selected.) Knowledge                    component(s) associated with the correct performance of this step. In the case of                     multiple KCs assigned to a single step, KC names are separated by two tildes (\"~~\").  \n", "20                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          (Only shown when the \"Knowledge Components\" option is selected.) An opportunity                    is the first chance on a step for a student to demonstrate whether he or she has learned                    the associated knowledge component. Opportunity number is therefore a count that                    increases by one each time the student encounters a step with the listed knowledge                    component. In the case of multiple KCs assigned to a single step, opportunity                    number values are separated by two tildes (\"~~\") and are given in the same order as                     the KC names. Check here to see how opportunity count                    is computed when Event Type column is present in transaction data.  \n", "21                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    A hypothetical error rate based on the Additive Factor Model (AFM)                    algorithm. A value of \"1\" is a prediction that a student's first attempt will be an                    error (incorrect attempt or hint request); a value of \"0\" is a prediction that the                    student's first attempt will be correct. For specifics, see below \"Predicted Error Rate\" and how it's calculated.                     In the case of multiple KCs assigned to a single step, Datashop implements a compensatory sum across all of the KCs,                    thus a single value of predicted error rate is provided (i.e., the same predicted error rate for each KC assigned to a step).                     For more detail on Datashop's implementation for multi-skilled step, see                      Model Values page.  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# help_table2: the description for data by steps\n", "df2 = pd.read_csv('OLI_data/help_table2.csv',sep=',',encoding=\"gbk\")\n", "df2 = df2.loc[:, ['Field', 'Annotation']]\n", "df2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.2 Summarization of Data\n", "\n", "**This table organizes the data as student-problem-step**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON> Student Id</th>\n", "      <th>Problem Hierarchy</th>\n", "      <th>Problem Name</th>\n", "      <th>Problem View</th>\n", "      <th>Step Name</th>\n", "      <th>Step Start Time</th>\n", "      <th>First Transaction Time</th>\n", "      <th>Correct Transaction Time</th>\n", "      <th>Step End Time</th>\n", "      <th>Step Duration (sec)</th>\n", "      <th>Correct Step Duration (sec)</th>\n", "      <th>Error Step Duration (sec)</th>\n", "      <th>First Attempt</th>\n", "      <th>Incorrects</th>\n", "      <th>Hints</th>\n", "      <th>Corrects</th>\n", "      <th>Condition</th>\n", "      <th>KC (F2011)</th>\n", "      <th>Opportunity (F2011)</th>\n", "      <th>Predicted Error Rate (F2011)</th>\n", "      <th><PERSON> (Single-KC)</th>\n", "      <th>Opportunity (Single-KC)</th>\n", "      <th>Predicted Error Rate (Single-KC)</th>\n", "      <th><PERSON> (Unique-step)</th>\n", "      <th>Opportunity (Unique-step)</th>\n", "      <th>Predicted Error Rate (Unique-step)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>All Data</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>q1_point1i1 UpdateComboBox</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>23.13</td>\n", "      <td>23.13</td>\n", "      <td>.</td>\n", "      <td>correct</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>.</td>\n", "      <td>identify_interaction</td>\n", "      <td>1</td>\n", "      <td>0.3991</td>\n", "      <td>Single-KC</td>\n", "      <td>1</td>\n", "      <td>0.4373</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>All Data</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>q1_point3i3 UpdateComboBox</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>23.13</td>\n", "      <td>23.13</td>\n", "      <td>.</td>\n", "      <td>correct</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>.</td>\n", "      <td>gravitational_forces</td>\n", "      <td>1</td>\n", "      <td>0.1665</td>\n", "      <td>Single-KC</td>\n", "      <td>2</td>\n", "      <td>0.4373</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Row    Sample                       Anon Student Id  \\\n", "0    1  All Data  Stu_00b2b35fd027e7891e8a1a527125dd65   \n", "1    2  All Data  Stu_00b2b35fd027e7891e8a1a527125dd65   \n", "\n", "                                                                                         Problem Hierarchy  \\\n", "0  sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams   \n", "1  sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams   \n", "\n", "  Problem Name  Problem View                   Step Name  Step Start Time  \\\n", "0   _m2_assess             1  q1_point1i1 UpdateComboBox  2011/9/21 17:35   \n", "1   _m2_assess             1  q1_point3i3 UpdateComboBox  2011/9/21 17:35   \n", "\n", "  First Transaction Time Correct Transaction Time    Step End Time  \\\n", "0        2011/9/21 17:35          2011/9/21 17:35  2011/9/21 17:35   \n", "1        2011/9/21 17:35          2011/9/21 17:35  2011/9/21 17:35   \n", "\n", "  Step Duration (sec) Correct Step Duration (sec) Error Step Duration (sec)  \\\n", "0               23.13                       23.13                         .   \n", "1               23.13                       23.13                         .   \n", "\n", "  First Attempt  Incorrects  Hints  Corrects Condition            KC (F2011)  \\\n", "0       correct           0      0         1         .  identify_interaction   \n", "1       correct           0      0         1         .  gravitational_forces   \n", "\n", "  Opportunity (F2011)  Predicted Error Rate (F2011) KC (Single-KC)  \\\n", "0                   1                        0.3991      Single-KC   \n", "1                   1                        0.1665      Single-KC   \n", "\n", "   Opportunity (Single-KC)  Predicted Error Rate (Single-KC) KC (Unique-step)  \\\n", "0                        1                            0.4373              NaN   \n", "1                        2                            0.4373              NaN   \n", "\n", "   Opportunity (Unique-step)  Predicted Error Rate (Unique-step)  \n", "0                        NaN                                 NaN  \n", "1                        NaN                                 NaN  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df_step =  pd.read_csv('OLI_data/AllData_student_step_2011F.csv',low_memory=False) # sep=\"\\t\"\n", "df_step.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Data Analysis"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th>Problem View</th>\n", "      <th>Incorrects</th>\n", "      <th>Hints</th>\n", "      <th>Corrects</th>\n", "      <th>Predicted Error Rate (F2011)</th>\n", "      <th>Opportunity (Single-KC)</th>\n", "      <th>Predicted Error Rate (Single-KC)</th>\n", "      <th>Opportunity (Unique-step)</th>\n", "      <th>Predicted Error Rate (Unique-step)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>194947.000000</td>\n", "      <td>194947.000000</td>\n", "      <td>194947.000000</td>\n", "      <td>194947.000000</td>\n", "      <td>194947.000000</td>\n", "      <td>113992.000000</td>\n", "      <td>194947.000000</td>\n", "      <td>194947.000000</td>\n", "      <td>193043.000000</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>97474.000000</td>\n", "      <td>1.133154</td>\n", "      <td>0.379611</td>\n", "      <td>0.143172</td>\n", "      <td>0.964072</td>\n", "      <td>0.237508</td>\n", "      <td>419.751066</td>\n", "      <td>0.252233</td>\n", "      <td>1.035971</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>56276.495801</td>\n", "      <td>0.760515</td>\n", "      <td>1.373797</td>\n", "      <td>0.852520</td>\n", "      <td>0.480346</td>\n", "      <td>0.158128</td>\n", "      <td>288.365862</td>\n", "      <td>0.086406</td>\n", "      <td>0.384182</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.002900</td>\n", "      <td>1.000000</td>\n", "      <td>0.038600</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>48737.500000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.117900</td>\n", "      <td>171.000000</td>\n", "      <td>0.188100</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>97474.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.201400</td>\n", "      <td>382.000000</td>\n", "      <td>0.240500</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>146210.500000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.319500</td>\n", "      <td>635.000000</td>\n", "      <td>0.294700</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>194947.000000</td>\n", "      <td>32.000000</td>\n", "      <td>413.000000</td>\n", "      <td>43.000000</td>\n", "      <td>86.000000</td>\n", "      <td>0.969300</td>\n", "      <td>1410.000000</td>\n", "      <td>0.773600</td>\n", "      <td>24.000000</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 Row   Problem View     Incorrects          Hints  \\\n", "count  194947.000000  194947.000000  194947.000000  194947.000000   \n", "mean    97474.000000       1.133154       0.379611       0.143172   \n", "std     56276.495801       0.760515       1.373797       0.852520   \n", "min         1.000000       1.000000       0.000000       0.000000   \n", "25%     48737.500000       1.000000       0.000000       0.000000   \n", "50%     97474.000000       1.000000       0.000000       0.000000   \n", "75%    146210.500000       1.000000       0.000000       0.000000   \n", "max    194947.000000      32.000000     413.000000      43.000000   \n", "\n", "            Corrects  Predicted Error Rate (F2011)  Opportunity (Single-KC)  \\\n", "count  194947.000000                 113992.000000            194947.000000   \n", "mean        0.964072                      0.237508               419.751066   \n", "std         0.480346                      0.158128               288.365862   \n", "min         0.000000                      0.002900                 1.000000   \n", "25%         1.000000                      0.117900               171.000000   \n", "50%         1.000000                      0.201400               382.000000   \n", "75%         1.000000                      0.319500               635.000000   \n", "max        86.000000                      0.969300              1410.000000   \n", "\n", "       Predicted Error Rate (Single-KC)  Opportunity (Unique-step)  \\\n", "count                     194947.000000              193043.000000   \n", "mean                           0.252233                   1.035971   \n", "std                            0.086406                   0.384182   \n", "min                            0.038600                   1.000000   \n", "25%                            0.188100                   1.000000   \n", "50%                            0.240500                   1.000000   \n", "75%                            0.294700                   1.000000   \n", "max                            0.773600                  24.000000   \n", "\n", "       Predicted Error Rate (Unique-step)  \n", "count                                 0.0  \n", "mean                                  NaN  \n", "std                                   NaN  \n", "min                                   <PERSON><PERSON>  \n", "25%                                   NaN  \n", "50%                                   NaN  \n", "75%                                   NaN  \n", "max                                   <PERSON>  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_step.describe()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["num_total: 194947\n", "num_students: 333\n", "num_problems: 300\n", "num_kcs: 98\n", "num_null_condition: 0\n", "\n", " ****************************** \n", "\n", "74004 27911 187943\n", "0.6483968011923079\n"]}], "source": ["num_total = len(df_step)\n", "num_students = len(df_step['Anon Student Id'].unique())\n", "num_problems = len(df_step['Problem Name'].unique())\n", "num_kcs = len(df_step['KC (F2011)'].unique())\n", "num_null_condition = df_step['Condition'].isnull().sum()  # 空值可不要\n", "print(\"num_total:\",num_total)\n", "print(\"num_students:\",num_students)\n", "print(\"num_problems:\",num_problems)\n", "print(\"num_kcs:\",num_kcs)\n", "print(\"num_null_condition:\",num_null_condition)\n", "\n", "n_incorrects = df_step['Incorrects'].sum()\n", "n_hints = df_step['Hints'].sum()\n", "n_corrects = df_step['Corrects'].sum()\n", "print(\"\\n\",\"*\"*30,\"\\n\")\n", "print(n_incorrects,n_hints,n_corrects)\n", "print(n_corrects / (n_incorrects + n_hints + n_corrects))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （1）Analysis for Null and Unique value of column attributes"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------num_unique_toal and num_nonull_toal----------------------\n", "<class 'pandas.core.series.Series'>\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_name</th>\n", "      <th>num_nonull</th>\n", "      <th>num_null</th>\n", "      <th>num_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>194947</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON>ple</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON> Student Id</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Problem Hierarchy</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Problem Name</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Problem View</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Step Name</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Step Start Time</td>\n", "      <td>194632</td>\n", "      <td>315</td>\n", "      <td>33098</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>First Transaction Time</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>34578</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Correct Transaction Time</td>\n", "      <td>182132</td>\n", "      <td>12815</td>\n", "      <td>33501</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Step End Time</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>34351</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Step Duration (sec)</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>2521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Correct Step Duration (sec)</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>2187</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Error Step Duration (sec)</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>2105</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>First Attempt</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Incorrects</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Hints</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Corrects</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Condition</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>KC (F2011)</td>\n", "      <td>113992</td>\n", "      <td>80955</td>\n", "      <td>98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Opportunity (F2011)</td>\n", "      <td>113992</td>\n", "      <td>80955</td>\n", "      <td>1206</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Predicted Error Rate (F2011)</td>\n", "      <td>113992</td>\n", "      <td>80955</td>\n", "      <td>7623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td><PERSON> (Single-KC)</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Opportunity (Single-KC)</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>1410</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>Predicted Error Rate (Single-KC)</td>\n", "      <td>194947</td>\n", "      <td>0</td>\n", "      <td>317</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td><PERSON> (Unique-step)</td>\n", "      <td>193043</td>\n", "      <td>1904</td>\n", "      <td>1179</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>Opportunity (Unique-step)</td>\n", "      <td>193043</td>\n", "      <td>1904</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Predicted Error Rate (Unique-step)</td>\n", "      <td>0</td>\n", "      <td>194947</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              col_name  num_nonull  num_null  num_unique\n", "0                                  Row      194947         0      194947\n", "1                               Sample      194947         0           1\n", "2                      Anon Student Id      194947         0         333\n", "3                    Problem Hierarchy      194947         0          27\n", "4                         Problem Name      194947         0         300\n", "5                         Problem View      194947         0          32\n", "6                            Step Name      194947         0         382\n", "7                      Step Start Time      194632       315       33098\n", "8               First Transaction Time      194947         0       34578\n", "9             Correct Transaction Time      182132     12815       33501\n", "10                       Step End Time      194947         0       34351\n", "11                 Step Duration (sec)      194947         0        2521\n", "12         Correct Step Duration (sec)      194947         0        2187\n", "13           Error Step Duration (sec)      194947         0        2105\n", "14                       First Attempt      194947         0           3\n", "15                          Incorrects      194947         0          32\n", "16                               Hints      194947         0          30\n", "17                            Corrects      194947         0          17\n", "18                           Condition      194947         0           1\n", "19                          KC (F2011)      113992     80955          98\n", "20                 Opportunity (F2011)      113992     80955        1206\n", "21        Predicted Error Rate (F2011)      113992     80955        7623\n", "22                      KC (Single-KC)      194947         0           1\n", "23             Opportunity (Single-KC)      194947         0        1410\n", "24    Predicted Error Rate (Single-KC)      194947         0         317\n", "25                    KC (Unique-step)      193043      1904        1179\n", "26           Opportunity (Unique-step)      193043      1904          25\n", "27  Predicted Error Rate (Unique-step)           0    194947           1"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["def work_col_analysis(df_work):\n", "    num_nonull_toal = df_work.notnull().sum()  # Not Null\n", "    dict_col_1 = {'col_name':num_nonull_toal.index,'num_nonull':num_nonull_toal.values}\n", "    df_work_col_1 = pd.DataFrame(dict_col_1)\n", "\n", "    num_null_toal = df_work.isnull().sum()  # Null\n", "    dict_col_2 = {'col_name':num_null_toal.index,'num_null':num_null_toal.values}\n", "    df_work_col_2 = pd.DataFrame(dict_col_2)\n", "\n", "    num_unique_toal = df_work.apply(lambda col: len(col.unique()))   # axis=0\n", "    print(type(num_unique_toal))\n", "    dict_col_3 = {'col_name':num_unique_toal.index,'num_unique':num_unique_toal.values}\n", "    df_work_col_3 = pd.DataFrame(dict_col_3)\n", "\n", "    # df_work_col = pd.concat([df_work_col_1, df_work_col_2], axis=1)\n", "    df_work_col = pd.merge(df_work_col_1, df_work_col_2, on=['col_name'])\n", "    df_work_col = pd.merge(df_work_col, df_work_col_3, on=['col_name'])\n", "    return df_work_col\n", "print(\"-------------------num_unique_toal and num_nonull_toal----------------------\")\n", "df_result = work_col_analysis(df_step)\n", "df_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （3）Data Cleaning \n", "### Data Cleaning Suggestions\n", "> - Redundant columns: Columns that are all NULL or Single value.\n", "> - rows that KC (F2011) == null（Do not know the knowledge source）\n", "> - rows that Step Start Time == null（This step is too short or more than 10mins, so the data is not reliable）\n", "> - Others\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df_step_clear = df_step.copy(deep=True) # deep copy"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the cols num before clear:  28\n", "the cols num after clear: 24\n", "drop:--- <PERSON><PERSON>\n", "drop:--- Condition\n", "drop:--- <PERSON> (Single-KC)\n", "drop:--- Predicted Error Rate (Unique-step)\n"]}], "source": ["# 直接清除所有”冗余列“\n", "cols = list(df_step.columns.values)\n", "drop_cols = []\n", "for col in cols:\n", "    if len(df_step_clear[col].unique().tolist()) == 1:\n", "        df_step_clear.drop(col,axis =1,inplace=True)\n", "        drop_cols.append(col)\n", "\n", "print(\"the cols num before clear: \",len(df_step.columns.to_list()))\n", "print(\"the cols num after clear:\",len(df_step_clear.columns.to_list()))\n", "for col in drop_cols:\n", "    print(\"drop:---\",col)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Others：'KC (F2011)','Step Start Time' with null value\n", "df_step_clear.dropna(axis=0, how='any', subset=['KC (F2011)','Step Start Time'],inplace = True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------num_unique_toal and num_nonull_toal----------------------\n", "<class 'pandas.core.series.Series'>\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_name</th>\n", "      <th>num_nonull</th>\n", "      <th>num_null</th>\n", "      <th>num_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>113817</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON> Student Id</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>331</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Problem Hierarchy</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>26</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Problem Name</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>154</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Problem View</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Step Name</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>240</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Step Start Time</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>18856</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>First Transaction Time</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>19745</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Correct Transaction Time</td>\n", "      <td>103454</td>\n", "      <td>10363</td>\n", "      <td>19146</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Step End Time</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>19623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Step Duration (sec)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>2382</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Correct Step Duration (sec)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>2093</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Error Step Duration (sec)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>1949</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>First Attempt</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Incorrects</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Hints</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Corrects</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>KC (F2011)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>Opportunity (F2011)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>1205</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Predicted Error Rate (F2011)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>7622</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Opportunity (Single-KC)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>1164</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>Predicted Error Rate (Single-KC)</td>\n", "      <td>113817</td>\n", "      <td>0</td>\n", "      <td>315</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td><PERSON> (Unique-step)</td>\n", "      <td>112869</td>\n", "      <td>948</td>\n", "      <td>625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>Opportunity (Unique-step)</td>\n", "      <td>112869</td>\n", "      <td>948</td>\n", "      <td>25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                            col_name  num_nonull  num_null  num_unique\n", "0                                Row      113817         0      113817\n", "1                    Anon Student Id      113817         0         331\n", "2                  Problem Hierarchy      113817         0          26\n", "3                       Problem Name      113817         0         154\n", "4                       Problem View      113817         0          32\n", "5                          Step Name      113817         0         240\n", "6                    Step Start Time      113817         0       18856\n", "7             First Transaction Time      113817         0       19745\n", "8           Correct Transaction Time      103454     10363       19146\n", "9                      Step End Time      113817         0       19623\n", "10               Step Duration (sec)      113817         0        2382\n", "11       Correct Step Duration (sec)      113817         0        2093\n", "12         Error Step Duration (sec)      113817         0        1949\n", "13                     First Attempt      113817         0           3\n", "14                        Incorrects      113817         0          25\n", "15                             Hints      113817         0          25\n", "16                          Corrects      113817         0          15\n", "17                        KC (F2011)      113817         0          97\n", "18               Opportunity (F2011)      113817         0        1205\n", "19      Predicted Error Rate (F2011)      113817         0        7622\n", "20           Opportunity (Single-KC)      113817         0        1164\n", "21  Predicted Error Rate (Single-KC)      113817         0         315\n", "22                  KC (Unique-step)      112869       948         625\n", "23         Opportunity (Unique-step)      112869       948          25"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# the remaining columns\n", "print(\"-------------------num_unique_toal and num_nonull_toal----------------------\")\n", "df_result = work_col_analysis(df_step_clear)\n", "df_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Outlier Analysis\n", "> - <p>It is found that there is a non-numeric type in duration that is '.' , which should represent 0</p>\n", "> - In addition, box diagrams can be used to analyze whether some outliers need to be removed"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['Row', 'Anon Student Id', 'Problem Hierarchy', 'Problem Name', 'Problem View', 'Step Name', 'Step Start Time', 'First Transaction Time', 'Correct Transaction Time', 'Step End Time', 'Step Duration (sec)', 'Correct Step Duration (sec)', 'Error Step Duration (sec)', 'First Attempt', 'Incorrects', 'Hints', 'Corrects', 'KC (F2011)', 'Opportunity (F2011)', 'Predicted Error Rate (F2011)', 'Opportunity (Single-KC)', 'Predicted Error Rate (Single-KC)', '<PERSON> (Unique-step)', 'Opportunity (Unique-step)']\n", "----------------------------------------------------------------------------------------------------\n", "['Row', 'Problem View', 'Incorrects', 'Hints', 'Corrects', 'Predicted Error Rate (F2011)', 'Opportunity (Single-KC)', 'Predicted Error Rate (Single-KC)', 'Opportunity (Unique-step)']\n", "----------------------------------------------------------------------------------------------------\n", "Row                                   int64\n", "Anon Student Id                      object\n", "Problem Hierarchy                    object\n", "Problem Name                         object\n", "Problem View                          int64\n", "Step Name                            object\n", "Step Start Time                      object\n", "First Transaction Time               object\n", "Correct Transaction Time             object\n", "Step End Time                        object\n", "Step Duration (sec)                  object\n", "Correct Step Duration (sec)          object\n", "Error Step Duration (sec)            object\n", "First Attempt                        object\n", "Incorrects                            int64\n", "Hints                                 int64\n", "Corrects                              int64\n", "KC (F2011)                           object\n", "Opportunity (F2011)                  object\n", "Predicted Error Rate (F2011)        float64\n", "Opportunity (Single-KC)               int64\n", "Predicted Error Rate (Single-KC)    float64\n", "KC (Unique-step)                     object\n", "Opportunity (Unique-step)           float64\n", "dtype: object\n"]}], "source": ["print(df_step_clear.columns.tolist())\n", "print(\"-\"*100)\n", "print(df_step_clear.describe().columns.tolist()) #有许多object类无法统计分析\n", "print(\"-\"*100)\n", "print(df_step_clear.dtypes)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step Duration (sec)            float64\n", "Correct Step Duration (sec)    float64\n", "Error Step Duration (sec)      float64\n", "dtype: object\n"]}], "source": ["# Change . to 0 in \"xxx-duration\"\n", "rectify_cols = ['Step Duration (sec)', 'Correct Step Duration (sec)', 'Error Step Duration (sec)']\n", "for col in rectify_cols:\n", "    df_step_clear[col] = df_step_clear[col].apply(lambda x: 0 if x=='.' else x)\n", "    df_step_clear[col] = df_step_clear[col].astype(float)\n", "print(df_step_clear[rectify_cols].dtypes)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Data Visualization"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.graph_objs as go\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["D:\\MySoftwares\\Anaconda\\envs\\data\\lib\\site-packages\\ipykernel_launcher.py:8: UserWarning:\n", "\n", "Matplotlib is currently using module://ipykernel.pylab.backend_inline, which is a non-GUI backend, so cannot show the figure.\n", "\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAX0AAAD4CAYAAAAAczaOAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAARFElEQVR4nO3dX4hdZ7nH8e/jdHDCsRVD0pJMEkekyo6DR+lQBXNhKBzjVb2xdIRjpRsCYqcKuUh0LmwvAu2NFwaPEEhoBc+Uglp7kR5PLVtkwD+dEaFJ51RDbe2YYFOHaqmMTuNzLrISZpJJMmuSWXt23u8Hhr33s9fa6xkYfvPyrr3eFZmJJKkM7+p2A5Kk5hj6klQQQ1+SCmLoS1JBDH1JKshN3W7gajZt2pRDQ0PdbkO6YG5ujtOnTzM/P8/AwABbtmxh48aN3W5LWmJ6evqNzNx8cX3dh/7Q0BBTU1PdbkMCYGJigvHxcY4dO8auXbuYnJyk3W6zb98+RkdHu92edEFEvLpsfb1/T39kZCQNfa0Xw8PDHDp0iN27d1+odTodxsbGOH78eBc7k5aKiOnMHLmkbuhLK9fX18f8/Dz9/f0XagsLCwwMDHD27NkudiYtdbnQ90SuVEOr1WJycnJJbXJyklar1aWOpHoMfamG8fFx2u02nU6HhYUFOp0O7Xab8fHxbrcmrci6P5ErrSfnT9aOjY0xMzNDq9Xi4MGDnsRVz3BOX5JuQM7pS5IMfUkqiaEvSQUx9CWpIIa+JBXE0Jekghj6klQQQ1+SCmLoS1JBDH1JKoihL0kFMfSlmiYmJhgeHqavr4/h4WEmJia63ZK0Yq6yKdVw/naJR44cWXK7RMCVNtUTXGVTqsHbJapXeLtE6TrwdonqFS6tLF0H3i5Rve6qoR8R2yOiExEzEXEiIr5a1TdGxLMR8fvq8X2L9vl6RJyMiJci4jOL6ndExAvVe9+OiFibX0taG94uUb1uJSdy3wH2ZeZvIuJmYDoingW+BDyXmY9ExAHgALA/InYC9wIfAbYCP42ID2XmWeC7wF7gl8AxYA/wzPX+paS14u0S1euuGvqZeRo4XT1/KyJmgEHgbuDT1WaPAz8D9lf1JzLzH8AfIuIkcGdEvALckpm/AIiI7wGfw9BXjxkdHTXk1bNqzelHxBDwceBXwG3VP4Tz/xhurTYbBF5btNtsVRusnl9cX+44eyNiKiKmzpw5U6dFSdIVrDj0I+I9wA+Ar2Xm36606TK1vEL90mLm4cwcycyRzZs3r7RFSdJVrCj0I6Kfc4H//cz8YVX+c0Rsqd7fArxe1WeB7Yt23wacqurblqlLkhqykm/vBHAEmMnMby1662ngvur5fcCPF9XvjYh3R8QHgNuBX1dTQG9FxCerz/zion0kSQ1Yybd3PgX8J/BCRPy2qn0DeAR4MiLawB+BzwNk5omIeBJ4kXPf/PlK9c0dgC8DjwEbOHcC15O4ktSgq470M3MyMyMzP5qZH6t+jmXmXzLzrsy8vXqcW7TPwcz8YGZ+ODOfWVSfyszh6r0Hcr1fDiwtwwXX1MtccE2qwQXX1Otce0eqwQXX1Ctce0e6DmZmZpidnV0yvTM7O8vMzEy3W5NWxNCXati6dSsPPvggb7/9NpnJ22+/zYMPPsjWrVu73Zq0Is7pSzX8/e9/58033+TNN98E4JVXXgHgXe9y/KTe4F+qVMPc3FyturTeGPqSVBBDX5IKYuhLUkEMfUkqiKEvSQUx9CWpIIa+JBXE0Jekghj6klQQQ1+SCmLoS1JBDH1JKoihL0kFMfQlqSCGviQVxNCXpIIY+pJUEENfkgpi6EtSQRoP/YjYExEvRcTJiDjQ9PElqWSNhn5E9AHfAT4L7ARGI2Jnkz1IUsmaHunfCZzMzJcz85/AE8DdDfcgScW6qeHjDQKvLXo9C3zi4o0iYi+wF2DHjh3NdKYby0PvXZOPzW/e0vgxeeiva/O5KlLToR/L1PKSQuZh4DDAyMjIJe9LV7VGQRmx3J/wOZn+qWr9a3p6ZxbYvuj1NuBUwz1IUrGiydFJRNwE/A64C/gT8Dzwhcw8cYV9zgCvNtOhtCJ3LFObbrwL6cren5mbLy42Or2Tme9ExAPAT4A+4OiVAr/a55KmpfUgIqYyc6TbfUh1NDrSl24khr56kVfkSlJBDH1p9Q53uwGpLqd3JKkgjvQlqSCGviQVxNCXaoqIoxHxekQc73YvUl2GvlTfY8CebjchrYahL9WUmT8H5rrdh7Qahr4kFcTQl6SCNL20cm2bNm3KoaGhbrchXTA3N8fAwADz8/Ns2LAht2zZwsaNG7vdlrTE9PT0G11fcG01hoaGmJqa6nYbEgATExOMj49z7Ngxdu3axeTkJO12m3379jE6Otrt9qQLImLZ1YnX/RW5IyMjaehrvRgeHubQoUPs3r37Qq3T6TA2Nsbx436DU+tHREwvtyCgoS/V0NfXx/z8PP39/RdqCwsLDAwMcPbs2S52Ji11udD3RK5UQ6vV4uGHH2Z4eJi+vj6Gh4d5+OGHabVa3W5NWhFDX6ph9+7dPProo9x///289dZb3H///Tz66KNLpnuk9czQl2rodDrs37+fo0ePcvPNN3P06FH2799Pp9PpdmvSijinL9XgnL56hXP60nXQarWYnJxcUpucnHROXz3D0JdqGB8fp91u0+l0WFhYoNPp0G63GR8f73Zr0oqs+4uzpPXk/AVYY2NjzMzM0Gq1OHjwoBdmqWc4py9JNyDn9CVJhr5U18TExJKLsyYmJrrdkrRizulLNZxfcO3IkSNLFlwDnNdXT3BOX6rBBdfUK1xwTboOvDhLvcITudJ10Gq1GBwcJCIu/AwODnpxlnqGc/pSDadPn2Zubo7+/n4WFhbo7+/nzJkzjvLVMxzpSzXMzc0B56Z0Fj+er0vrnaEvSQUx9CWpIIa+JBXkmkI/Il6JiBci4rcRMVXVNkbEsxHx++rxfYu2/3pEnIyIlyLiM9favCSpnusx0t+dmR9b9H3QA8BzmXk78Fz1mojYCdwLfATYA/xXRPRdh+NLklZoLaZ37gYer54/DnxuUf2JzPxHZv4BOAncuQbHlyRdxrWGfgL/GxHTEbG3qt2WmacBqsdbq/og8NqifWermiSpIdd6cdanMvNURNwKPBsR/3eFbWOZ2rJrQFT/QPYC7Nix4xpblCSdd00j/cw8VT2+DvyIc9M1f46ILQDV4+vV5rPA9kW7bwNOXeZzD2fmSGaObN68+VpalCQtsurQj4h/i4ibzz8H/gM4DjwN3Fdtdh/w4+r508C9EfHuiPgAcDvw69UeX5JU37VM79wG/Cgizn/Of2fm/0TE88CTEdEG/gh8HiAzT0TEk8CLwDvAVzLTBUskqUGrDv3MfBn492XqfwHuusw+B4GDqz2mJOnaeEWuJBXE0Jekghj6klQQQ1+SCmLoS1JBDH1JKoihL0kFMfQlqSCGviQVxNCXpIIY+pJUEENfkgpi6EtSQQx9SSqIoS9JBTH0Jakghr4kFcTQl6SCGPqSVBBDX5IKYuhLUkEMfUkqiKEvSQUx9CWpIIa+JBXE0JekgjQe+hGxJyJeioiTEXGg6eNLUskaDf2I6AO+A3wW2AmMRsTOJnuQpJLd1PDx7gROZubLABHxBHA38GLDfUiXiIhG9s/MazqOdC2aDv1B4LVFr2eBT1y8UUTsBfYC7Nixo5nOdGN56L21d8lv3rIGjSyjbm8P/XVt+lCRmg795YZClwx7MvMwcBhgZGTEYZHqW6OgvNJo3hG8ekHTJ3Jnge2LXm8DTjXcg7Rqlwt2A1+9Ipr8Y42Im4DfAXcBfwKeB76QmSeusM8Z4NVmOpRq2QS80e0mpMt4f2ZuvrjY6PROZr4TEQ8APwH6gKNXCvxqn0ualtaDiJjKzJFu9yHV0ehIX7qRGPrqRV6RK0kFMfSl1Tvc7QakupzekaSCONKXpIIY+pJUEENfqikijkbE6xFxvNu9SHUZ+lJ9jwF7ut2EtBqGvlRTZv4cmOt2H9JqGPqSVBBDX5IK0vTSyrVt2rQph4aGut2GdMHc3BwDAwPMz8+zYcOG3LJlCxs3bux2W9IS09PTb3R9wbXVGBoaYmpqqtttSABMTEwwPj7OsWPH2LVrF5OTk7Tbbfbt28fo6Gi325MuiIhlVyde91fkjoyMpKGv9WJ4eJhDhw6xe/fuC7VOp8PY2BjHj/sNTq0fETG93IKAhr5UQ19fH/Pz8/T391+oLSwsMDAwwNmzZ7vYmbTU5ULfE7lSDa1Wi8nJySW1yclJWq1WlzqS6jH0pRrGx8dpt9t0Oh0WFhbodDq0223Gx8e73Zq0Iuv+RK60npw/WTs2NsbMzAytVouDBw96Elc9wzl9SboBOacvSTL0Jakkhr4kFcTQl6SCGPqSVBBDX5IKYuhLUkEMfUkqiKEvSQUx9CWpIIa+VNPExATDw8P09fUxPDzMxMREt1uSVswF16Qazt8568iRI0vunAW46Jp6gguuSTV45yz1Cu+cJV0H3jlLvcJVNqXrwDtnqdddNfQjYntEdCJiJiJORMRXq/rGiHg2In5fPb5v0T5fj4iTEfFSRHxmUf2OiHiheu/bERFr82tJa8M7Z6nXreRE7jvAvsz8TUTcDExHxLPAl4DnMvORiDgAHAD2R8RO4F7gI8BW4KcR8aHMPAt8F9gL/BI4BuwBnrnev5S0VrxzlnrdVUM/M08Dp6vnb0XEDDAI3A18utrsceBnwP6q/kRm/gP4Q0ScBO6MiFeAWzLzFwAR8T3gcxj66jGjo6OGvHpWrTn9iBgCPg78Crit+odw/h/DrdVmg8Bri3abrWqD1fOL65Kkhqw49CPiPcAPgK9l5t+utOkytbxCfblj7Y2IqYiYOnPmzEpblCRdxYpCPyL6ORf438/MH1blP0fElur9LcDrVX0W2L5o923Aqaq+bZn6JTLzcGaOZObI5s2bV/q7SJKuYiXf3gngCDCTmd9a9NbTwH3V8/uAHy+q3xsR746IDwC3A7+upoDeiohPVp/5xUX7SJIasJJv73wK+E/ghYj4bVX7BvAI8GREtIE/Ap8HyMwTEfEk8CLnvvnzleqbOwBfBh4DNnDuBK4ncSWpQVcd6WfmZGZGZn40Mz9W/RzLzL9k5l2ZeXv1OLdon4OZ+cHM/HBmPrOoPpWZw9V7D+R6vxxYWoYLrqmXueCaVIMLrqnXufaOVIMLrqlXuOCadB244Jp6hQuuSddBq9XinnvuYWBggIhgYGCAe+65xwXX1DMMfamGwcFBnnrqKRYWFoBzo/ynnnqKwUEvLldvMPSlGp577jkA/vWvfy15PF+X1jtDX6rhcvP2zuerVxj6klQQQ1+SCmLoS1JBDH1JKoihL0kFMfQlqSCGviQVxNCXpIIY+pJUEENfkgpi6EtSQQx9SSqIoS9JBTH0Jakghr4kFcTQl6SCGPqSVBBDX5IKYuhLUkEaD/2I2BMRL0XEyYg40PTxJalkjYZ+RPQB3wE+C+wERiNiZ5M9SFLJmh7p3wmczMyXM/OfwBPA3Q33IEnFuqnh4w0Cry16PQt84uKNImIvsBdgx44dzXSmG8tD712Tj81v3tL4MXnor2vzuSpS06Efy9TykkLmYeAwwMjIyCXvS1e1RkEZsdyf8DmZ/qlq/Wt6emcW2L7o9TbgVMM9SFKxosnRSUTcBPwOuAv4E/A88IXMPHGFfc4ArzbTobQidyxTm268C+nK3p+Zmy8uNjq9k5nvRMQDwE+APuDolQK/2ueSpqX1ICKmMnOk231IdTQ60pduJIa+epFX5EpSQQx9afUOd7sBqS6ndySpII70Jakghr4kFcTQl2qKiKMR8XpEHO92L1Jdhr5U32PAnm43Ia2GoS/VlJk/B+a63Ye0Goa+JBXE0Jekghj6klQQQ1+SCmLoSzVFxATwC+DDETEbEe1u9yStlMswSFJBHOlLUkEMfUkqiKEvSQUx9CWpIIa+JBXE0Jekghj6klSQ/wfi4zFtWDl6swAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 432x288 with 3 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Outlier analysis for each column\n", "\n", "fig=plt.figure()\n", "box_cols = ['Step Duration (sec)', 'Correct Step Duration (sec)','Error Step Duration (sec)']\n", "for i, col in enumerate(box_cols):\n", "    ax=fig.add_subplot(3, 1, i+1)\n", "    ax.boxplot(df_step_clear[df_step_clear[col].notnull()][col].tolist())\n", "fig.show(\"svg\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# The distribution of continuous values\n", "def show_value_counts_histogram(colname, sort = True):\n", "    # create the bins\n", "    start = int(df_step_clear[colname].min()/10)*10\n", "    end = int(df_step_clear[colname].quantile(q=0.95)/10+1)*10\n", "    step = int((end - start)/20)\n", "    print(start, end, step)\n", "    counts, bins = np.histogram(df_step_clear[colname],bins=range(start, end, step))\n", "    bins = 0.5 * (bins[:-1] + bins[1:])\n", "\n", "    fig = px.bar(x=bins, y=counts, labels={'x': colname, 'y':'count'})\n", "    fig.show(\"svg\")\n", "\n", "\n", "# Box distribution of continuous values\n", "def show_value_counts_box(colname, sort = True):\n", "#     fig = px.box(df_step_clear, y=colname)\n", "#     fig.show(\"svg\")\n", "    plt.figure(figsize=(10,5))\n", "    plt.title('Box-plot for '+ colname,fontsize=20)#标题，并设定字号大小\n", "    plt.boxplot([df_step_clear[colname].tolist()])\n", "    plt.show(\"svg\")\n", "    \n", "\n", "# Histogram of discrete values\n", "def show_value_counts_bar(colname, sort = True):\n", "    ds = df_step_clear[colname].value_counts().reset_index()\n", "    ds.columns = [\n", "        colname,\n", "        'Count'\n", "    ]\n", "    if sort:\n", "        ds = ds.sort_values(by='Count', ascending=False)\n", "    # histogram\n", "    fig = px.bar(\n", "        ds,\n", "        x = colname,\n", "        y = 'Count',\n", "        title = colname + ' distribution'\n", "    )\n", "    fig.show(\"svg\")\n", "    \n", "\n", "# Pie of discrete values\n", "def show_value_counts_pie(colname, sort = True):\n", "    ds = df_step_clear[colname].value_counts().reset_index()\n", "    ds.columns = [\n", "        colname,\n", "        'percent'\n", "    ]\n", "    ds['percent'] /= len(df_step_clear)\n", "    if sort:\n", "        ds = ds.sort_values(by='percent', ascending=False)\n", "    fig = px.pie(\n", "        ds,\n", "        names = colname,\n", "        values = 'percent',\n", "        title = colname+ ' Percentage',\n", "    )\n", "    fig.update_traces(textposition='inside', textinfo='percent+label',showlegend=False)\n", "    fig.show(\"svg\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"scrolled": false}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-d0ee9c\"><g class=\"clips\"><clipPath id=\"clipd0ee9cxyplot\" class=\"plotclip\"><rect width=\"540\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd0ee9cx\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd0ee9cy\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd0ee9cxy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"270\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,311.19)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,252.37)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,193.56)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,134.74)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,370)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clipd0ee9cxyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M18,270V13.5H162V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M198,270V199.18H342V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M378,270V262.61H522V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(170,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">correct</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(350,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">incorrect</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(530,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">hint</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,311.19)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,252.37)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,193.56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">60k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,134.74)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">80k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-d0ee9c\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">First Attempt distribution</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">First Attempt</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,31.840625000000003,235)\" x=\"31.840625000000003\" y=\"235\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Count</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0 70 3\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-d7aea1\"><g class=\"clips\"><clipPath id=\"clipd7aea1xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"310\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd7aea1x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd7aea1y\"><rect x=\"0\" y=\"60\" width=\"700\" height=\"310\"/></clipPath><clipPath class=\"axesclip\" id=\"clipd7aea1xy\"><rect x=\"80\" y=\"60\" width=\"540\" height=\"310\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"60\" width=\"540\" height=\"310\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,334.26)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,298.52)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,262.78)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,227.04)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,191.3)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,155.56)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,119.82)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,84.08)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,370)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,60)\" clip-path=\"url('#clipd7aea1xyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M2.35,310V15.5H21.13V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M25.83,310V189.78H44.61V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M49.3,310V225.21H68.09V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M72.78,310V246.68H91.57V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M96.26,310V263.77H115.04V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M119.74,310V278.19H138.52V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M143.22,310V284.55H162V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M166.7,310V288.94H185.48V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M190.17,310V293.71H208.96V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M213.65,310V296.69H232.43V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M237.13,310V299.2H255.91V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M260.61,310V301.62H279.39V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M284.09,310V302.88H302.87V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M307.57,310V303.55H326.35V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M331.04,310V305.14H349.83V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M354.52,310V306.25H373.3V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M378,310V306.61H396.78V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M401.48,310V306.62H420.26V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M424.96,310V307.14H443.74V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M448.43,310V307.15H467.22V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M471.91,310V307.73H490.7V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M495.39,310V307.68H514.17V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M518.87,310V307.38H537.65V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(80,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(158.26,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(236.52,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(314.78,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">30</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(393.04,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(471.3,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">50</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(549.5699999999999,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">60</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,334.26)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,298.52)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,262.78)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">15k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,227.04)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,191.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">25k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,155.56)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">30k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,119.82)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">35k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,84.08)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-d7aea1\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"/><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Step Duration (sec)</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,31.840625000000003,215)\" x=\"31.840625000000003\" y=\"215\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">count</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAlwAAAFECAYAAADyceDbAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjMuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8QVMy6AAAACXBIWXMAAAsTAAALEwEAmpwYAAAml0lEQVR4nO3df5hdVX3v8fc3AyTB8rMEDAEELdKBacU6RaqxEvxBrrcW9NbbYCtQ5jZWMWprK2IeH+G23Gpr5VauULFQoFcHadFCW5RSOoijKB28VAKBghAkJkL4aRQSwuR7/9hr4ORw5mdm50zmvF/Pc559Zu1fa5/ZmfPJXmuvHZmJJEmS6jOn3RWQJEma7QxckiRJNTNwSZIk1czAJUmSVDMDlyRJUs0MXJIkSTUzcEk7QESsiYg1ba7DmyPiWxHxeERkRPxDO+ujncNMOHdHExH/MyI2RcTB7a5Ls4j4UERsiYifb3ddNDMYuNR25cu/+bW5/KG/LCK6213HmSAiDi2fzaVTWRe4GjgM+BvgHOCKaa3gxOvyyxHxhYh4oPyefxwR34+If4yID0fEi5qWz4i4sR11HU9EnN103m4tx/NARFwbEWdGxKJ213MsEXFjROx0AzKWkPWHwEWZ+WC769PCBcDDwKfaXRHNDLu0uwJSg3Ma3u8FHAOcAvy3iFicmbe1pVazwxuBecCHMvOL7apERPw2cBkQwL8BXwGGqYJgL/BrwJeBe9tVxyn6OnBjef8iYCHwWuC/AOdExNmZ+Yk21W17vaHdFRjFx4C5wJ+3uyKtZObTEfGXwCcj4jWZ+a1210ntZeDSjJGZZzeXRcT5wPuADwKn7dgazSoHlum6dlUgInYHPgsk8ObMvKHFMq8BHtnRdZsGNzafvxERwNuBi4A/jQh2xtCVmd9vdx2aRcRewG8BN8zQq1sj/i/wv4D3AgauDmeToma6fynTBc0zImJuRHwkIr4XEU+VppxvRMR/b1ru7aW559sRsWvTvJ6y7rqI2H8iFYqIS8v2XhoRfxARd5V+JGsj4ryI2HOiBzeJYzgbuL/8eGpTM9ZpY2z/uNJcNHL1cKBhveMaljs8Ii6PiB9GxDPl87g8Ig5vsc2RZrTjIuKdEfGdiPjJBPr59AB7AqtahS2AzPxWZj5R9nNaQ1PX65uO+eymOr06Iv4+In5U6v9gRHwuIg5s2sVzTWjls/+TiLi/NG1+PyI+HhG7jXMcE5KVq4DfKEUfj4iFDfU4bazfX6um1Il89mW7V0XEfRHxdDmnvlmuLjZu69Dy+b6+YX/ZvN8YpQ/XRM/dxn2VfzuHRsQVEfFI+XczFBG/Nt7n2eRkYHfgS61mRsSvR8QNEbG+/G7XRcTXI+K9LZbdNyL+NCJWl8/rybLum0fbeUT8ZlnmsXIMayKiPyJ6G5fLzHXAN4DfmMzfBc1OXuHSTPfGMh1qLCxfitdRfVncRXXlZHeqL7cvRcTRmflRgMz8ckR8FjgDOBf4cNnGyB/sucBvZ+bDk6zbecCvAldS9Y86gepK3OuiagLdNNbKkzkGquaqvYEPAP8B/EPDpm4bYzdrqMLWcWU/l5WykXlExC8D/wrsAVwD3An8PNUVhBMj4g2Zuc3nX3wIeBPwj8AAVTPwWB4t0wMj4kWZ+dNxlr+t1P3jwAPApQ3zbhx5ExG/A3we2Fzq/yBwOPA/gLdGxLGZ+YMW278S+GXg74EtwInA2UBvRPx6TtODZjNzICIGgcVUV7w+Ow2bHeuzv5Dqd3gTsB74WeAtwN9GxBGZ+bGy3BNUn+9pwEvYtkl/zVg7n+S52+glwC3AfcDfAvsCvwlcHRFvzMyB8Q8deP7vwmCLui0HPgf8iOrzeQTYH/hF4Heo+laNLPsSqnPpUKpg9DWqJuFfA74WEe/OzM83LB9UfSBPLdv9MrABOAhYAtxN098q4JtU//5+FfinCR6fZqPM9OWrrS+qJqak+rIbeX2a6g/gVqo/mns0rXNWWedaYJeG8v2pviwSeE1D+Vzgu2V7S0vZ35TlzplkfS8t6z0CvKShfA5wVZn3saZ11gBrtvMYDi1ll07hMz67rHtcU3kAq8u832qa95ul/C5gTott/RR45STqEFRftkkVps4AXgnsNoHz48ZR5r0ceIaqz9eipnnHU/UP+0pT+Y1lm/8J7NNQPg+4ucx71yQ/17PHWe6Py3KXNZSdVspOm+hxT+SzB17Womw34AaqYNn8Od1IdUFutLpP57mbwMebtnXCyLYmcS79CPgxEC3m3UoVvvdvMW+/Fse+FVjWVL53OUefBg5oKF9e6noLsFfTOl3Awhb7PLGs82cTPT5fs/PV9gr48tXwh7jV6w7gnS3Wuaf8ofz5FvP6yrqXNJUfDmykunPoD8syXwe6JlnfS2kRqsq8l1J9yd/fVN7qS2tSx0A9geu1pfxbo6z3jTL/V1ts67wp1OMQqisyjb/jZ4DvAGcCe45yftw4yvbOK/P/6yjzvwI8S0Ng5/nA9YJQRXUlIoGBSX6uZ4+z3O/RFCrYvsA1lc/+7WXdU5rKb2TygWuq5+6aVv/eqK5gPjLB49itbOs/R5l/K1Ug3Wec7byibOfvRpk/EpTe21B2eymbzH80Xl3WuWKyvzNfs+tlk6JmjMyMkfdRDQ1wFPAJ4AsRcVRmrizz9gB+DvhhZt7VYlP/VqavbNr+PRHxbuALVHc2PUIV5oYblyt9ag5t2uaNmXljU9nXWxzDfRHxIHBoROydpT9Ss6keQw1+qWl/reqxuNTjpqZ5t0x2Z1k17S2JaqiPN1HdmXhMw+u9EXFcZt4/wU3+Spm+vjSNNtuf6srDy6m+iBu94PdHFTCfZfo/95FzO6dpe6N+9hFxCFV4fQNVwJ3ftMh2DVOxnefubc3/3ooHef53OZ6fLdPHR5n/BeAvgDsi4ktUv+dvZuaGpuVG9rdXc5/AYqTfaDc89zepB3goM//fBOsK8FiZ7jeJdTQLGbg0I2XVv+eWiHg7sBb4cET8VVZ3JO1VFls/yuoj5Xu3mHc9VVPEnlT/s/1hi2VOo3QkbnJj088PjbL/H1H1VdmLqp9MK9tzDNNpe+rxo6nuNDNXUzVlAhDV4JCXUH0JngecNMFNjXz5/tE4y/1Mi7IX/P4yczgiHqUKatNppPN+85f+VLX87CPipVRhbB+q8PgvwJNUV10Ppep7NHc7970958wTo6zzLBO/ievpMp3XamZmfjoiHqG6M/D9VP0qMyK+DvxRPt8fceTceVN5jWbk3Nm7TFv9zRjLSOB9esylNOsZuDSjZeYTEXE31ZWYX6L6n/CTZfaLR1lt5E6wJxsLS4fXy6nC1iPA8oi4IjO3uXKTmcdNsHoHUHWSbTZSrydbzKNp3qSOoQbbU4/pulpDZt4VEe+i6ot1/CRWHanXXpn540nu9gBgm870EdFF9UU82W2NZ0mZfqehbGuZvuDvcETsPc72Rvvs/4Cq/r+TmZc2bfNkqsC1vdp67pa/Cc/wfGBqtczlwOXlc3wN8DbgdOC6iOjO6gaZkfp9IDM/M4FdP1Gmk71COFLPyd6Uo1nGYSG0M9inTOcAZOZG4PvAomgxbAHPf7l9t6n8j4ClVE0Ox1N1IP5iREz1Uv8LroKVKwwHU/V5eWK0Fad4DCNNMV1Tqm1rI00jx40yf6S8+bOsw8YyjabyrYx+zN8u09dNYX+trmK+jioATabJaEwRcTxVX7mnqfqUjRhpEmv1WJreFmUT8XNlelWLea2OF8p5VcLmuLbj3990uh1YON5QC5n5RGZem5m/S9X3cl+eP1cmde6Uq+6rgAMiYjJNziOP9rltEutoFjJwaUaLiJOoRiHfwrYDB15C9cX8541fFCU8faxhmZHyVwN/QnUF5T2ZeTvw+1T/W720XP2arA+U28pH9jGHqm/YHKo7IMczqWOg+oJOqn450+WbVFfpFkfEbzTOKD//KtXdfC+4/X6yIuKwiHh/VINWNs8LYGX5sbmv2KO0DiUA/4fq3DgvIl7eYru7RcRoX6gfi4h9GpadB/xp+XEiv78xReXtwN+Voo9nZmNT4BBVmHxnGaJkZL19gT+b4m7XlOlxTXU5gWqYjFZGhuuYzHk12XN3ut1I9e/smOYZEbE0Ilq13ow0Ez8FUJoWvwG8PSJOb7WTiPiF2HZ8vpErYZ9rPo8jYk40jLPW4NgyHRjlWNQhbFLUjNHUcfVFwJFUj0YB+GhmNva5+VSZdyLwHxFxLdU4QO+g+sP6Z5k5WLa7N9VzA5Pq9u+NAJn5VxHxBqqxg/6AqqPtZHwTuK10zH2S6vb2V1B1zp7IF+aEj6HU9ycR8R2qcb6+QBWEhoFrMvN7k6z7yDYzIk6l6tv2pYi4mmoYiCOo+lFtpLqrbevoW5mwvYC/pPqS/ibV1YKNVMd6PNUdng9TjTHV6AZgWUT8I9Vn+yxwU2beVJoiT6f6cr8jIr5G9bnsShUgXkfVb6rVA4RXl3Uax+F6GfDPVGNETcZxDefvfKo+W6+l+s/CZuDMzNzmETSZub78Ht9FdR79M1Vz91uoQudUOu5fQDXW1N9FxFVU/Y16qK7sXkk11EezG6jOuS+Xc/Bp4IHMHOszmNS5W4OrqM6TE6jGkGt0BbCpjH22hioYvo5qzLVbm5Z/J1Un/4sj4v1UTb5PUI2r9YtUn92v8Hxz4F9T3URyCnBP+feyger3fTzVeXj2yMbLf8LeANydmau2+6i1c2v3bZK+fNF6OIhnqTrfXg28aZT15gEfpfrifprqy3sQOLlpuZGxsX6/xTb2ohqE8RngmAnW99KyvZdS/dG/C9hE9eX2v2k9tMEamm6tn8wxNCz/c1Tjkj1KdXVk1GEFmtY7mxbDQjTMP4IqZKynCh/rqR5LcsRktzVGHeZShbgLqJqbHir7epLqi/BcYEGL9fYHvliWH6bFMAzAL5TfywNUAeex8pl+Dji+adkbyzbmUl31vL+scx/VIKtzJ3FMI5/FyGtr+R0+QDVG1Zk0jXvV4jP5c6obQ0bGEzuL6j/DYw0LMepnT9Vn6d+oroiOnE8n8fyQF82fXRfV42fuK7+PbfY7Hecu4wxpwjhDU4yyznepHlXV1VT+e1RNt/dRXc16jKqJ+MM0jedXlt+jHMetwE/KsdxPFbyXAy9qsc5vUd39+CTVv/37qboq/FLTcm8ux/3ByRybr9n5isxp6/cqdYSIuJSq8/FhmbmmvbXRZEX12JrXZ8MwJNr5lJsAvgi8PTO/Mt7y7VCuMr6eajDaum+A0QxnHy5J0s7oCqomwLOn2AezVhFxNNXdkWcbtgQGLknSTiir5pnlVM2HL3hI+QywkOoGgr9qd0U0M9hpXpK0U8rqZpEp3TBSt8z8KvDVdtdDM4d9uCRJkmpmk6IkSVLNxm1SLIMB3kR1+/IuwN9n5sfLmDO/y/PPBvtoZl5b1jmL6onxw8D7M/O6Uv4qqlu351PdMv2BHOcS23777ZeHHnropA9MkiRpR7v11lsfycwFzeUT6cO1mWocm59ExK7AYESMtEufl5mfalw4Io4ElgFHUXVk/NeIeHlWT4i/kKqT47epAtdSxmnjPvTQQxkaGhprEUmSpBkhIh5oVT5uk2JWflJ+3LW8xroqdSJwRWZuzsz7qQbyO6Y88mDPzLy5XNW6nGowPkmSpFltQn24IqIrIm6jerzB9Zk58sT790XE9yLikoZnki0CHmxYfW0pW1TeN5dLkiTNahMKXJk5nJlHUz1f6piI6KFqHnwZcDTVY0BGnkPXagC6HKP8BSJieUQMRcTQhg0bWi0iSZK005jUXYqZ+QTVM6+WZuZDJYhtBT7P809tXwsc3LDaQVTPu1pb3jeXt9rPRZnZm5m9Cxa8oN+ZJEnSTmXcwBURCyJi7/J+PvBG4K7SJ2vE26geYApwDbAsIuZGxGHA4cAtmbke2BgRx5bHMJxC9WBiSZKkWW0idykuBC6LiC6qgHZlZv5TRPxteVZUUj1N/t0AmXlHRFwJ3Ak8C5xR7lAEeA/PDwvhKLySJKkjzPiR5nt7e9NhISRJ0s4gIm7NzN7mckeal9RR+vv76enpoauri56eHvr7+9tdJUkdwIdXS+oY/f39rFy5kosvvpjFixczODhIX18fACeffHKbaydpNrNJUVLH6Onp4fzzz2fJkiXPlQ0MDLBixQpWrVo1xpqSNDGjNSkauCR1jK6uLjZt2sSuu+76XNmWLVuYN28ew8PDY6wpSRNjHy5JHa+7u5vBwcFtygYHB+nu7m5TjSR1CgOXpI6xcuVK+vr6GBgYYMuWLQwMDNDX18fKlSvbXTVJs5yd5iV1jJGO8StWrGD16tV0d3dz7rnn2mFeUu3swyVJkjRN7MMlSZLUJgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmBi5JkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSarZuIErIuZFxC0R8R8RcUdEnFPK942I6yPinjLdp2GdsyLi3oi4OyJOaCh/VUTcXuZ9JiKinsOSJEmaOSZyhWszcHxmvgI4GlgaEccCHwFuyMzDgRvKz0TEkcAy4ChgKXBBRHSVbV0ILAcOL6+l03cokiRJM9O4gSsrPyk/7lpeCZwIXFbKLwNOKu9PBK7IzM2ZeT9wL3BMRCwE9szMmzMzgcsb1pEkSZq1JtSHKyK6IuI24GHg+sz8DnBAZq4HKNP9y+KLgAcbVl9byhaV983lkiRJs9qEAldmDmfm0cBBVFeresZYvFW/rByj/IUbiFgeEUMRMbRhw4aJVFGSJGnGmtRdipn5BHAjVd+rh0ozIWX6cFlsLXBww2oHAetK+UEtylvt56LM7M3M3gULFkymipIkSTPORO5SXBARe5f384E3AncB1wCnlsVOBa4u768BlkXE3Ig4jKpz/C2l2XFjRBxb7k48pWEdSZKkWWuXCSyzELis3Gk4B7gyM/8pIm4GroyIPuAHwDsAMvOOiLgSuBN4FjgjM4fLtt4DXArMB75aXpIkSbNaVDcMzly9vb05NDTU7mpIkiSNKyJuzcze5nJHmpckSaqZgUuSJKlmBi5JkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmBi5JkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmBi5JkqSajRu4IuLgiBiIiNURcUdEfKCUnx0RP4yI28rrLQ3rnBUR90bE3RFxQkP5qyLi9jLvMxER9RyWJEnSzLHLBJZ5FvhQZn43IvYAbo2I68u88zLzU40LR8SRwDLgKOBA4F8j4uWZOQxcCCwHvg1cCywFvjo9hyJJkjQzjXuFKzPXZ+Z3y/uNwGpg0RirnAhckZmbM/N+4F7gmIhYCOyZmTdnZgKXAydt7wFIkiTNdJPqwxURhwKvBL5Tit4XEd+LiEsiYp9Stgh4sGG1taVsUXnfXC5JkjSrTThwRcTPAFcBH8zMH1M1D74MOBpYD/zFyKItVs8xylvta3lEDEXE0IYNGyZaRUmSpBlpQoErInalCltfyMwvA2TmQ5k5nJlbgc8Dx5TF1wIHN6x+ELCulB/UovwFMvOizOzNzN4FCxZM5ngkSZJmnIncpRjAxcDqzPx0Q/nChsXeBqwq768BlkXE3Ig4DDgcuCUz1wMbI+LYss1TgKun6TgkSZJmrIncpfha4F3A7RFxWyn7KHByRBxN1Sy4Bng3QGbeERFXAndS3eF4RrlDEeA9wKXAfKq7E71DUZIkzXpR3TA4c/X29ubQ0FC7qyFJkjSuiLg1M3ubyx1pXpIkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmBi5JkqSaGbgkdZT+/n56enro6uqip6eH/v7+dldJUgeYyMCnkjQr9Pf3s3LlSi6++GIWL17M4OAgfX19AJx88sltrp2k2cyBTyV1jJ6eHs4//3yWLFnyXNnAwAArVqxg1apVY6wpSRMz2sCnBi5JHaOrq4tNmzax6667Ple2ZcsW5s2bx/Dw8BhrStLEONK8pI7X3d3N4ODgNmWDg4N0d3e3qUaSOoWBS1LHWLlyJX19fQwMDLBlyxYGBgbo6+tj5cqV7a6apFnOTvOSOsZIx/gVK1awevVquru7Offcc+0wL6l29uGSJEmaJvbhkiRJahMDlyRJUs0MXJIkSTUzcEmSJNXMwCVJklQzA5ckSVLNDFySJEk1M3BJkiTVzMAlSZJUMwOXJElSzQxckiRJNTNwSZIk1czAJUmSVDMDlyRJUs3GDVwRcXBEDETE6oi4IyI+UMr3jYjrI+KeMt2nYZ2zIuLeiLg7Ik5oKH9VRNxe5n0mIqKew5IkSZo5JnKF61ngQ5nZDRwLnBERRwIfAW7IzMOBG8rPlHnLgKOApcAFEdFVtnUhsBw4vLyWTuOxSJIkzUjjBq7MXJ+Z3y3vNwKrgUXAicBlZbHLgJPK+xOBKzJzc2beD9wLHBMRC4E9M/PmzEzg8oZ1JEmSZq1J9eGKiEOBVwLfAQ7IzPVQhTJg/7LYIuDBhtXWlrJF5X1zeav9LI+IoYgY2rBhw2SqKEmSNONMOHBFxM8AVwEfzMwfj7Voi7Ico/yFhZkXZWZvZvYuWLBgolWUJEmakSYUuCJiV6qw9YXM/HIpfqg0E1KmD5fytcDBDasfBKwr5Qe1KJckSZrVJnKXYgAXA6sz89MNs64BTi3vTwWubihfFhFzI+Iwqs7xt5Rmx40RcWzZ5ikN60iSJM1au0xgmdcC7wJuj4jbStlHgU8AV0ZEH/AD4B0AmXlHRFwJ3El1h+MZmTlc1nsPcCkwH/hqeUmSJM1qUd0wOHP19vbm0NBQu6shSZI0roi4NTN7m8sdaV6SJKlmBi5JkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmBi5JkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUtSR+nv76enp4euri56enro7+9vd5UkdYBd2l0BSdpR+vv7WblyJRdffDGLFy9mcHCQvr4+AE4++eQ2107SbBaZ2e46jKm3tzeHhobaXQ1Js0BPTw/nn38+S5Ysea5sYGCAFStWsGrVqjbWTNJsERG3ZmZvc/m4TYoRcUlEPBwRqxrKzo6IH0bEbeX1loZ5Z0XEvRFxd0Sc0FD+qoi4vcz7TETEdByYJE3U6tWrWbx48TZlixcvZvXq1W2qkaROMZE+XJcCS1uUn5eZR5fXtQARcSSwDDiqrHNBRHSV5S8ElgOHl1erbUpSbbq7uxkcHNymbHBwkO7u7jbVSFKnGDdwZeZNwGMT3N6JwBWZuTkz7wfuBY6JiIXAnpl5c1ZtmJcDJ02xzpI0JStXrqSvr4+BgQG2bNnCwMAAfX19rFy5st1VkzTLbU+n+fdFxCnAEPChzHwcWAR8u2GZtaVsS3nfXC5JO8xIx/gVK1awevVquru7Offcc+0wL6l2Uw1cFwJ/DGSZ/gVwOtCqX1aOUd5SRCynan7kkEMOmWIVJemFTj75ZAOWpB1uSuNwZeZDmTmcmVuBzwPHlFlrgYMbFj0IWFfKD2pRPtr2L8rM3szsXbBgwVSqKEmSNGNMKXCVPlkj3gaM3MF4DbAsIuZGxGFUneNvycz1wMaIOLbcnXgKcPV21FuSJGmnMW6TYkT0A8cB+0XEWuDjwHERcTRVs+Aa4N0AmXlHRFwJ3Ak8C5yRmcNlU++huuNxPvDV8pIkSZr1HPhUkiRpmkx54FNJkiRtHwOXJElSzQxckjpKf38/PT09dHV10dPTQ39/f7urJKkDbM/Ap5K0U+nv72flypVcfPHFLF68mMHBQfr6+gAcm0tSrew0L6lj9PT0cP7557NkyZLnygYGBlixYgWrVq0aY01JmpjROs0buCR1jK6uLjZt2sSuu+76XNmWLVuYN28ew8PDY6wpSRPjXYqSOl53dzfnnHPONn24zjnnHLq7u9tdNUmznIFLUsdYsmQJn/zkJzn99NPZuHEjp59+Op/85Ce3aWKUpDoYuCR1jIGBAc4880wuueQS9thjDy655BLOPPNMBgYG2l01SbOcgUtSx1i9ejVHHHHENmVHHHEEq1evblONJHUKA5ekjnHggQeyYsUKfvrTnwLw05/+lBUrVnDggQe2uWaSZjvH4ZLUMZ566ik2btzIvHnz2Lp1K08//TQbN26kq6ur3VWTNMt5hUtSx3jssceYO3cujz32WMufJakuBi5JHWX+/Plcd911PPPMM1x33XXMnz+/3VWS1AFsUpTUUTZv3szpp5/OD37wAw455BA2b97c7ipJ6gBe4ZLUUZ566imefvrp5/pwPfXUU+2ukqQOYOCS1DF22WUXdt99d+bPn8+cOXOYP38+u+++O7vs4sV+SfUycEnqGMPDwwwPD7NmzRq2bt3KmjVrniuTpDoZuCR1jH322YdNmzY9NwzEyMOs99lnnzbXTNJsZ+CS1DGeeOIJIoIFCxYwZ84cFixYQETwxBNPtLtqkmY5A5ekjrF161bmz5/Po48+ytatW3n00UeZP38+W7dubXfVJM1yBi5JHWXr1q0sWrSIOXPmsGjRIsOWpB3CW3MkdZRNmzaxZs0agOemklQ3r3BJkiTVzMAlqeMccMAB20wlqW4GLkkdZbfddtvm4dW77bZbm2skqRPYh0tSR3nmmWeee79ly5Y21kRSJ/EKlyRJUs3GDVwRcUlEPBwRqxrK9o2I6yPinjLdp2HeWRFxb0TcHREnNJS/KiJuL/M+ExEx/YcjSZI080zkCtelwNKmso8AN2Tm4cAN5Wci4khgGXBUWeeCiOgq61wILAcOL6/mbUrSDtH4aB9J2hHGDVyZeRPwWFPxicBl5f1lwEkN5Vdk5ubMvB+4FzgmIhYCe2bmzZmZwOUN60jSDrXffvttM5Wkuk21D9cBmbkeoEz3L+WLgAcblltbyhaV983lLUXE8ogYioihDRs2TLGKktTaM888w5w5c7bpQC9JdZruTvOt+mXlGOUtZeZFmdmbmb0LFiyYtspJEsDjjz/O1q1befzxx9tdFUkdYqqB66HSTEiZPlzK1wIHNyx3ELCulB/UolySJGnWm2rgugY4tbw/Fbi6oXxZRMyNiMOoOsffUpodN0bEseXuxFMa1pEkSZrVJjIsRD9wM3BERKyNiD7gE8CbIuIe4E3lZzLzDuBK4E7ga8AZmTlcNvUe4K+pOtJ/H/jqNB+LJE3Ii1/8YubMmcOLX/zidldFUoeI6qbBmau3tzeHhobaXQ1Js0BEcOCBB7J+/Xoyk4hg4cKFrFu3jpn+t1DSziEibs3M3uZyR5qX1FHWrVvHW9/6VjZs2MBb3/pW1q2zO6mk+vksRUkd55prrsE7oCXtSF7hkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JLUcfbYYw/mzJnDHnvs0e6qSOoQ3qUoqeNs3Lhxm6kk1c0rXJIkSTUzcEmSJNXMwCVJklQzA5ckSVLNDFySJEk1M3BJkiTVzMAlSZJUMwOXJElSzQxckiRJNTNwSZIk1czAJUmSVDMDlyRJUs0MXJIkSTUzcEmSJNXMwCVJklQzA5ckSVLNDFySJEk1M3BJkiTVzMAlSZJUs+0KXBGxJiJuj4jbImKolO0bEddHxD1luk/D8mdFxL0RcXdEnLC9lZckSdoZTMcVriWZeXRm9pafPwLckJmHAzeUn4mII4FlwFHAUuCCiOiahv1LkiTNaHU0KZ4IXFbeXwac1FB+RWZuzsz7gXuBY2rYvyRJ0oyyvYErgX+JiFsjYnkpOyAz1wOU6f6lfBHwYMO6a0uZJEnSrLbLdq7/2sxcFxH7A9dHxF1jLBstyrLlglV4Ww5wyCGHbGcVJUmS2mu7rnBl5royfRj4ClUT4UMRsRCgTB8ui68FDm5Y/SBg3SjbvSgzezOzd8GCBdtTRUmSpLabcuCKiBdFxB4j74E3A6uAa4BTy2KnAleX99cAyyJibkQcBhwO3DLV/UuSJO0stqdJ8QDgKxExsp0vZubXIuLfgSsjog/4AfAOgMy8IyKuBO4EngXOyMzh7aq9JEnSTmDKgSsz7wNe0aL8UeANo6xzLnDuVPcpSZK0M3KkeUmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmBi5JkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmBi5JkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqpmBS5IkqWYGLkmSpJoZuCRJkmpm4JIkSaqZgUuSJKlmOzxwRcTSiLg7Iu6NiI/s6P1LkiTtaLvsyJ1FRBfwWeBNwFrg3yPimsy8c0fWQ9LOJSJ2mn1k5rRsR9LssqOvcB0D3JuZ92XmM8AVwIk7uA6SarTvvvsSEdP62plM97Hvu+++7T4kSdNgh17hAhYBDzb8vBZ4dfNCEbEcWA5wyCGH7JiaSZoWj71/GNiz3dWYRYbbXQFJ02BHB65W/1V9wfX3zLwIuAigt7fX6/PSzuTsJ9tdgzG1umJmM6Ckuu3owLUWOLjh54OAdTu4DpI6mOFKUjvs6D5c/w4cHhGHRcRuwDLgmh1cB0mSpB1qh17hysxnI+J9wHVAF3BJZt6xI+sgSZK0o+3oJkUy81rg2h29X0mSpHZxpHlJkqSaGbgkSZJqZuCSJEmqmYFLkiSpZgYuSZKkmhm4JEmSambgkiRJqlnM9MdcRMQG4IF210PSrLMf8Ei7KyFp1nlJZi5oLpzxgUuS6hARQ5nZ2+56SOoMNilKkiTVzMAlSZJUMwOXpE51UbsrIKlz2IdLkiSpZl7hkiRJqpmBS1JHiYhLIuLhiFjV7rpI6hwGLkmd5lJgabsrIamzGLgkdZTMvAl4rN31kNRZDFySJEk1M3BJkiTVzMAlSZJUMwOXJElSzQxckjpKRPQDNwNHRMTaiOhrd50kzX6ONC9JklQzr3BJkiTVzMAlSZJUMwOXJElSzQxckiRJNTNwSZIk1czAJUmSVDMDlyRJUs0MXJIkSTX7/xcQCiwDzfk4AAAAAElFTkSuQmCC\n", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["# Bar\n", "show_value_counts_bar('First Attempt')\n", "show_value_counts_histogram('Step Duration (sec)')\n", "show_value_counts_box('Step Duration (sec)')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-a28518\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M350,235l0,-135a135,135 0 0 1 100.60531728299974,44.980168105095004Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(378.4596190649416,169.81290361829647)scale(0.2085173306821047)rotate(-65.9108041856665)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Statically Equivalent Loads</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">13.4%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-94.93032441141571,-95.9855901015531a135,135 0 0 1 94.93032441141571,-39.0144098984469Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(323.53360256880006,169.17981408358335)scale(0.18015997519205948)rotate(67.65834629273309)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Representing Interactions Between Bodies</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">12.4%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-132.66467331828326,-25.00168901000858a135,135 0 0 1 37.73434890686755,-70.98390109154452Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(285.2348437852489,199.72735272403904)scale(0.24845090012254845)rotate(27.994675663565204)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Couples</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">9.62%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-129.92891530172872,36.653471438817405a135,135 0 0 1 -2.7357580165545414,-61.65516044882598Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(278.8020858805333,237.82153255361578)scale(0.1124412939680198)rotate(-2.5406573710429257)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Applications of Static Equivalency to Distributed Forces, section1 Simplifying 3D loadings to 2D or 1D loading</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">7.34%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-106.61167863960355,82.81877792895703a135,135 0 0 1 -23.31723666212517,-46.16530649013963Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(281.67087391239306,268.78169273775137)scale(0.21722344559782972)rotate(-26.797490708769374)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Force</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">6.14%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-68.09773234797738,116.56628521602305a135,135 0 0 1 -38.51394629162617,-33.747507287066014Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(300.40913129303107,290.79441336836226)scale(0.1758702624940732)rotate(-48.77382113392548)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">6.07%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-27.824554011431427,132.1014541708945a135,135 0 0 1 -40.27317833654595,-15.535168954871452Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(322.14777107357605,305.737723783814)scale(0.17587526443091672)rotate(-68.90613880176079)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">5.11%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l14.937850638420592,134.17101258581988a135,135 0 0 1 -42.762404649852016,-2.069558414925382Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(346.04446262340116,308.70686809125436)scale(0.1293052120581764)rotate(-87.22923640580927)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces, section1 Applying Force Equilibrium</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">5.07%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l50.9587177932692,125.01283566444667a135,135 0 0 1 -36.02086715484861,9.158176921373212Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(368.6193685787403,306.79451791989555)scale(0.1181958598695223)rotate(75.73499565091333)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces, section1 Applying Force and Moment Equilibrium</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">4.4%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l81.09997652159149,107.92494525454858a135,135 0 0 1 -30.141258728322285,17.087890409898094Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(388.51953671437894,301.96530129547955)scale(0.16096686322338435)rotate(60.449932786842055)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Equilibrium of a Single Subsystem</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">4.1%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l103.48104067294388,86.69875559224904a135,135 0 0 1 -22.38106415135239,21.226189662299532Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(406.38824360698914,293.57242388859817)scale(0.20272366907238437)rotate(46.51704051240148)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Multiple Body Equilibrium - Trusses, module Method of Joints</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">3.64%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l119.69343882698846,62.43781467804563a135,135 0 0 1 -16.21239815404458,24.26094091420341Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(414.9634233882467,277.89382648616834)scale(0.14357517354027777)rotate(33.752866443501375)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces, section1 Combining Moments</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">3.45%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l128.97617611012964,39.876634712683874a135,135 0 0 1 -9.282737283141188,22.56117996536176Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(424.77184016852146,265.27019032315366)scale(0.15242979287321118)rotate(22.3645852552782)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.88%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l133.7237926459521,18.518835826865683a135,135 0 0 1 -4.747616535822459,21.35779888581819Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(426.18836531438546,251.60223381965667)scale(0.10856584454398271)rotate(12.53248635968265)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Applications of Static Equivalency to Distributed Forces, section1 Center of Gravity and Centroid</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.58%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.9601318032167,-3.2806742688627866a135,135 0 0 1 -1.2363391572646094,21.79951009572847Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(433.8031992307326,239.2423591051205)scale(0.16988108640014898)rotate(3.246000158148661)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.58%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l133.1709417732339,-22.147240623381297a135,135 0 0 1 1.7891900299828194,18.86656635451851Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(431.0735179323677,226.9335436966536)scale(0.12541681964339316)rotate(-5.417380531906417)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces, section1 Combining Concurrent Forces</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.24%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l130.33247561502395,-35.19155864776645a135,135 0 0 1 2.8384661582099397,13.044318024385152Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(440.05977147240156,214.9227726863979)scale(0.15636265848729958)rotate(-12.276285616384143)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Multiple Body Equilibrium - Trusses, module Method of Sections</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.57%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l126.32501332096433,-47.612929015743035a135,135 0 0 1 4.007462294059621,12.421370367976586Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(434.40886763433485,207.35290708498744)scale(0.13151227150522565)rotate(-17.881072247555267)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Choosing a Solvable Subsystem</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.54%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l121.16308819999733,-59.535754449235526a135,135 0 0 1 5.161925120966998,11.922825433492491Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 253, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(430.8164308447701,199.5948281222755)scale(0.12728894082168146)rotate(-23.409947547378692)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Drawing FBDs of a Single Subsystem</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.53%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l116.46182828886704,-68.27622244686982a135,135 0 0 1 4.701259911130293,8.740467997634298Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 233, 183); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(432.5200301143848,190.19303662989475)scale(0.12379075161623722)rotate(-28.274598697909823)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Multiple Body Equilibrium - Frames, module Drawing FBDs of Multiple Subsystems</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.17%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l111.20725467770197,-76.53722301631237a135,135 0 0 1 5.254573611165071,8.261000569442544Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 25, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(430.46762402938674,183.35456027062952)scale(0.13005592543040437)rotate(-32.45921083845121)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Multiple Body Equilibrium - Frames, module Solving Multiple Subsystems</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.15%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l106.60024774722861,-82.83349069204726a135,135 0 0 1 4.607006930473361,6.296267675734896Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(181, 39, 15); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(425.49285511072907,179.40190687168803)scale(0.09674608243894574)rotate(-36.19309944911572)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Pin Connections</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.92%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l104.16933850909881,-85.87053577669575a135,135 0 0 1 2.4309092381298,3.037045084648483Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 102, 75); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(431.8772251756433,169.1990073978773)scale(0.06893360305403122)rotate(-38.67445109254328)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Other Connections</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.459%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l102.38406113314605,-87.99149973653259a135,135 0 0 1 1.7852773759527594,2.1209639598368426Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(118, 8, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(440.9050703607189,158.218374724064)scale(0.06736794566636527)rotate(-40.088299638894)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Moments of Inertia, module Second Moment of Area</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.327%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l101.41617776646355,-89.10532468512228a135,135 0 0 1 0.9678833666825,1.113824948589695Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(243, 105, 0); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(440.0184163027831,156.63295707494237)scale(0.03610720996824198)rotate(-40.98974669864782)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Fixed Connections</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.174%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l100.6053172829998,-90.01983189490494a135,135 0 0 1 0.8108604834637418,0.9145072097826557Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 136, 158); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(444.8763931381125,150.73919295134075)scale(0.034253977415719755)rotate(-41.56224465589497)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">sequence Statics, unit Moments of Inertia, module Mass Moment of Inertia</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.144%</tspan></text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-a28518\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Hierarchy Percentage</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-5ae365\"><g class=\"clips\"/><g class=\"gradients\"/></defs><g class=\"bglayer\"/><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"/><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"><g class=\"trace\" stroke-linejoin=\"round\" style=\"opacity: 1;\"><g class=\"slice\"><path class=\"surface\" d=\"M350,235l0,-135a135,135 0 0 1 48.5860743806104,9.046066451730866Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(366.28579952393864,133.99037928852704)scale(0.8260766778087297)rotate(-79.4530694008804)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m9_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">5.86%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-42.61040422224465,-128.09899863783838a135,135 0 0 1 42.61040422224465,-6.901001362161622Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(335.61992509855764,131.81495037811922)scale(0.7671180126462112)rotate(80.80049553230185)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m2_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">5.11%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-75.58132597243292,-111.85912195368259a135,135 0 0 1 32.970921750188275,-16.23987668415579Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(304.5511799738734,137.99416065827117)scale(0.6971989741834179)rotate(63.77737947758237)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_03</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">4.35%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-101.56360847227496,-88.93724435853878a135,135 0 0 1 25.982282499842043,-22.921877595143812Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(281.0544319864205,153.90965903295537)scale(0.64822820025395)rotate(48.580879833416816)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m11_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">4.1%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-120.51518783002912,-60.836580297488794a135,135 0 0 1 18.951579357754156,-28.100664061049983Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(261.30484169082655,172.77631086836607)scale(0.6649501772755558)rotate(33.996415298241914)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">4.01%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-131.17873242603628,-31.892634872308328a135,135 0 0 1 10.663544596007156,-28.943945425180466Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(248.9262711644795,195.83043854396135)scale(0.6042685506740396)rotate(20.224834602915166)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m17_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">3.64%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-134.84673597631559,-6.431002762698616a135,135 0 0 1 3.668003550279309,-25.461632109609713Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(238.63131904775275,217.27148939647427)scale(0.5558401290449758)rotate(8.197633042515577)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m3_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">3.04%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-133.80229358964726,17.942860144074984a135,135 0 0 1 -1.0444423866683223,-24.3738629067736Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(236.47300608101716,238.2552668370061)scale(0.535997794002492)rotate(-2.453675637207084)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m5_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.88%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-128.4892380279381,41.418784518619944a135,135 0 0 1 -5.313055561709177,-23.47592437454496Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(238.69064114571086,258.55811895504957)scale(0.5310173402721887)rotate(-12.752312923377076)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m7_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.84%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-120.13164180799168,61.59049144564757a135,135 0 0 1 -8.357596219946402,-20.17170692702763Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(243.01474581073072,277.71640932023035)scale(0.4957995440286946)rotate(-22.505337515485394)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m4_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.58%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-109.76637569477023,78.58971158386161a135,135 0 0 1 -10.365266113221452,-16.999220138214042Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(249.8246449853268,294.4522318912421)scale(0.46379526008163374)rotate(-31.372729908537394)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m6_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.35%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-97.7824892384975,93.07837986516053a135,135 0 0 1 -11.983886456272728,-14.488668281298914Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(258.7354787342012,308.75806506626066)scale(0.44404530936315184)rotate(-39.59487598513408)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_07</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">2.22%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-88.11154497573203,102.28076868106511a135,135 0 0 1 -9.670944262765474,-9.202388815904584Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(266.3082392770453,321.4979572015515)scale(0.33435572230735305)rotate(-46.42215134821686)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m18_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.57%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-77.82278790092045,110.31143949440761a135,135 0 0 1 -10.288757074811585,-8.030670813342496Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(275.0102467286979,329.47408857223326)scale(0.3284610013899644)rotate(-52.02693797938798)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m12_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.54%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-66.85841848709553,117.28150697106686a135,135 0 0 1 -10.964369413824912,-6.970067476659253Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(284.4364002253196,336.3056729323375)scale(0.3272752688365642)rotate(-57.55581327921141)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m10_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.53%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-55.27480901588603,123.16531771670677a135,135 0 0 1 -11.583609471209506,-5.883810745639906Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(293.98703210880063,343.0633372457172)scale(0.33373984196154166)rotate(-63.07203669047681)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m8_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.53%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-44.1735419416003,127.56840593318425a135,135 0 0 1 -11.10126707428573,-4.403088216477485Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(303.51454878195784,349.6202546322851)scale(0.31719311732826516)rotate(-68.36527056590842)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_05</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.41%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-34.01918242167919,130.64338952798383a135,135 0 0 1 -10.154359519921108,-3.0749835947995763Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(313.23313535465803,353.47301688333806)scale(0.28405912406989525)rotate(-73.1524288990222)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor1</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.25%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-24.32942560916777,132.78960444826987a135,135 0 0 1 -9.689756812511419,-2.1462149202860417Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 253, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(322.52297047912504,355.40548836343294)scale(0.2629721683492546)rotate(-77.51100450723527)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m14_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.17%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-14.641541905538835,134.2036707792613a135,135 0 0 1 -9.687883703628936,-1.4140663309914316Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 233, 183); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(331.3722308536286,357.2196902799091)scale(0.260009904428894)rotate(-81.69561664777666)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m15_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.15%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l-5.100075397690644,134.90362942092355a135,135 0 0 1 -9.541466507848192,-0.6999586416622492Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 25, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(340.09869448788254,359.3591746907951)scale(0.2587579987881577)rotate(-85.80431745697047)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_01</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">1.13%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l3.1931316594006645,134.96223142126a135,135 0 0 1 -8.293207057091308,-0.05860200033643537Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(181, 39, 15); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(348.4234121984561,360.93114696293264)scale(0.22890259244142383)rotate(-89.59513956614569)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.978%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l11.437159428561253,134.51465118791214a135,135 0 0 1 -8.244027769160589,0.4475802333478498Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 102, 75); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(357.5649163178501,361.54883826872066)scale(0.2311250261491032)rotate(86.89237987295394)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_07</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.973%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l19.54255247207991,133.57802455073215a135,135 0 0 1 -8.105393043518658,0.9366266371799838Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(118, 8, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(365.23452475481156,360.86264889021396)scale(0.22859317292084871)rotate(83.40836606130893)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_16</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.962%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l27.554671236709314,132.1580118382418a135,135 0 0 1 -8.012118764629403,1.4200127124903474Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(243, 105, 0); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(372.5937997347694,358.642527097379)scale(0.22327058701030403)rotate(79.94965602677985)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor13</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.959%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l35.38758005846838,130.2793889209091a135,135 0 0 1 -7.832908821759066,1.8786229173327058Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 136, 158); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(380.24898664099817,358.2156675858083)scale(0.2260190210324885)rotate(76.51308679722706)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_04</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.95%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l43.00974242786098,127.96547212544895a135,135 0 0 1 -7.622162369392598,2.3139167954601447Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 0, 73); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(387.30923015714575,355.615952912128)scale(0.22102114700469525)rotate(73.11289174727858)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_05</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.939%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l50.23323522717145,125.30611349256532a135,135 0 0 1 -7.2234927993104705,2.659358632883638Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(132, 216, 42); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(394.1739746733642,353.1398661733431)scale(0.21280218475783602)rotate(69.78860802867757)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor11</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.908%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l57.171920754225525,122.29624473904568a135,135 0 0 1 -6.938685527054076,3.0098687535196404Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 49, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(400.9789167020637,350.9298923885646)scale(0.21123328953074913)rotate(66.54972455784286)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_13</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.892%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l63.56984481040098,119.0960739520054a135,135 0 0 1 -6.397924056175455,3.2001707870402782Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(233, 164, 1); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(407.2050691092213,348.0281768702364)scale(0.19962889424012903)rotate(63.42628957009936)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor12</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.843%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l69.78288959365234,115.5653422093325a135,135 0 0 1 -6.2130447832513624,3.5307317426728986Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(413.07656470817227,344.7857826283041)scale(0.1993311743288092)rotate(60.39141780226146)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor14</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.843%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l75.76955936182154,111.73170487428983a135,135 0 0 1 -5.986669768169193,3.833637335042667Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(419.0399083784664,341.7011004452829)scale(0.2000209841150444)rotate(57.366034950842106)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_11</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.838%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l81.4986014282704,107.62424431900054a135,135 0 0 1 -5.729042066448869,4.107460555289293Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(422.60540990272966,335.3053016125612)scale(0.1872222216120066)rotate(54.36121141832939)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m8_assess_part2</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.831%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l86.8457672975035,103.35769300109146a135,135 0 0 1 -5.347165869233095,4.266551317909077Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(430.27740415511335,334.6696033083559)scale(0.19545262060206203)rotate(51.413321384327446)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_06</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.807%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l91.91537252055339,98.87651032681067a135,135 0 0 1 -5.069605223049891,4.481182674280788Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(434.8241795031297,330.09517002483375)scale(0.19145442324989978)rotate(48.52552782097575)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_23</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.798%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l96.61885303651714,94.28572128327758a135,135 0 0 1 -4.703480515963747,4.590789043533093Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(439.49485268085107,325.8908228973122)scale(0.186468288004831)rotate(45.69466775613478)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_24</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.775%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l100.63761242432271,89.98372611495819a135,135 0 0 1 -4.018759387805574,4.301995168319394Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(444.1152474712839,322.22494133320384)scale(0.16902909254444)rotate(43.05042304752362)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_08</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.694%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l104.46027348514377,85.51638008831388a135,135 0 0 1 -3.822661060821062,4.467346026644307Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(448.3006951403135,318.441521105171)scale(0.17053779972581898)rotate(40.55325654339862)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.693%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l108.04905713994175,80.93454918123412a135,135 0 0 1 -3.588783654797979,4.5818309070797625Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(451.3348722984885,313.73477746363227)scale(0.16721021628360372)rotate(38.07032341390118)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_04</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.686%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l111.30223546586646,76.39903389638391a135,135 0 0 1 -3.253178325924708,4.535515284850206Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(455.2876616488099,309.9190161984876)scale(0.1626298368612766)rotate(35.65064972719358)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.658%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l114.34930956009603,71.75817307547155a135,135 0 0 1 -3.047074094229572,4.640860820912366Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(457.88720487448063,305.25987984864264)scale(0.16054814239685744)rotate(33.28790953899676)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_03c</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.655%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l117.12524881899543,67.13178151284536a135,135 0 0 1 -2.775939258899399,4.626391562626182Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(460.70869872614736,300.8812495412852)scale(0.15619537998252397)rotate(30.96470650254355)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_21</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.636%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l119.58641173971156,62.642558434503414a135,135 0 0 1 -2.4611629207161343,4.489223078341951Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(463.9143898583768,296.93822943374056)scale(0.15022900049392582)rotate(28.73322965813543)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_02</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.604%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l121.86597270275162,58.0834287659759a135,135 0 0 1 -2.279560963040055,4.559129668527511Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(465.65673944157703,292.3310291545138)scale(0.14824907958516773)rotate(26.565012256517036)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_06</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.601%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l123.9629458842608,53.462024350896286a135,135 0 0 1 -2.0969731815091848,4.621404415079617Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(468.03922414956486,288.07237074611004)scale(0.1481931376986586)rotate(24.406283771317078)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor2</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.598%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l125.88204848398227,48.77201932949197a135,135 0 0 1 -1.9191025997214695,4.690005021404318Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(470.2211661257956,283.7109008787924)scale(0.1488178095631094)rotate(22.253881230396132)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_03</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.597%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l127.58485741352104,44.12600320413695a135,135 0 0 1 -1.7028089295387616,4.646016125355018Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(471.54956844186756,279.0878753279296)scale(0.14434177439426452)rotate(20.128363952660834)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.583%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l129.07702274583465,39.54898480455952a135,135 0 0 1 -1.4921653323136184,4.577018399577433Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(473.1959286503637,274.71929194052433)scale(0.1407504050040497)rotate(18.05661720129683)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_20</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.568%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l130.37028322143118,35.05123753541123a135,135 0 0 1 -1.2932604755965258,4.4977472691482845Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 253, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(474.66055635569745,270.4161241319829)scale(0.13716533177002865)rotate(16.041803948443544)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_11b</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.552%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l131.48155818503204,30.62025240320582a135,135 0 0 1 -1.111274963600863,4.430985132205411Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 233, 183); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(475.90415743064267,266.1614736751913)scale(0.13412332844987532)rotate(14.079179735891785)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_19</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.539%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l132.43360980063048,26.19807235607892a135,135 0 0 1 -0.9520516155984353,4.422180047126901Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 25, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(476.92806628408255,261.91848374545725)scale(0.13291239740714925)rotate(12.149766730804629)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_21</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.533%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l133.2165047253263,21.871508150265115a135,135 0 0 1 -0.7828949246958246,4.326564205813806Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(181, 39, 15); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(478.3541631742563,257.8280133587798)scale(0.13046966703752694)rotate(10.256727905321611)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_09</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.518%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l133.85712339543255,17.529133358496146a135,135 0 0 1 -0.6406186701062495,4.342374791768968Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 102, 75); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(478.58762465471045,253.57800573750998)scale(0.12932512268036336)rotate(8.392155829093952)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_08a</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.517%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.34601730269287,13.272062194871937a135,135 0 0 1 -0.48889390726031934,4.257071163624209Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(118, 8, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(479.2018581059011,249.4561405357947)scale(0.12642259308128537)rotate(6.5513060439124615)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_12</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.505%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.6980650797703,9.02392729170445a135,135 0 0 1 -0.3520477770774164,4.248134903167488Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(243, 105, 0); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(479.618682532809,245.36291968872442)scale(0.1258114391698046)rotate(4.737341521916619)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_18</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.503%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.91476106445677,4.796587007502095a135,135 0 0 1 -0.21669598468648132,4.227340284202355Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 136, 158); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(479.91268159238706,241.28392170709395)scale(0.1249958343708157)rotate(2.9344474024089777)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_02</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.499%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.9977428724893,0.7806531452957963a135,135 0 0 1 -0.0829818080325424,4.015933862206298Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 0, 73); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(480.2787442319102,237.3347192317772)scale(0.11905753341441766)rotate(1.183742323202921)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_17</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.474%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.9634986020988,-3.139115334178152a135,135 0 0 1 0.03424427039050215,3.9197684794739485Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(132, 216, 42); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(480.3971068608054,233.51165525137407)scale(0.11638109313976881)rotate(-0.5005403410739291)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_03</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.462%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.8235543092851,-6.899942275936137a135,135 0 0 1 0.13994429281370913,3.760826941757985Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 49, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(480.47421787737477,229.80856993899465)scale(0.11203857147704185)rotate(-2.131052478979541)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_09</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.444%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.5829405583661,-10.60340090081018a135,135 0 0 1 0.24061375091901027,3.7034586248740435Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(233, 164, 1); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(480.3381728433725,226.19947047740996)scale(0.11058580952783942)rotate(-3.717283006932348)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_25</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.438%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l134.2477037159573,-14.23214836180915a135,135 0 0 1 0.3352368424087899,3.6287474609989694Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(480.12435743872743,222.65110914693767)scale(0.10871409361887947)rotate(-5.278209757769218)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_15</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.43%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l133.81759770601747,-17.828363474824986a135,135 0 0 1 0.43010600993983417,3.5962151130158357Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(479.76971986101665,219.15301046170788)scale(0.10808921614946287)rotate(-6.820158675769164)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_01</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.427%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l133.3041518434315,-21.330801703249637a135,135 0 0 1 0.5134458625859679,3.5024382284246514Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(479.3946336393885,215.71040087238356)scale(0.10579382764086635)rotate(-8.339966788792708)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_22</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.417%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l132.72092166609139,-24.70135526855233a135,135 0 0 1 0.5832301773401127,3.370553565302693Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(479.3672854787733,212.3008874720072)scale(0.1030673910942526)rotate(-9.817074777933215)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_03</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.403%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l132.0602212294046,-28.019599726632773a135,135 0 0 1 0.6607004366867955,3.318244458080443Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(478.41686022365826,209.12058249311215)scale(0.10139343819059664)rotate(-11.260971559609061)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_01</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.399%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l131.3200299117978,-31.305746181244878a135,135 0 0 1 0.7401913176067865,3.286146454612105Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(477.88445356855647,205.8834410682084)scale(0.10116486048833627)rotate(-12.693797938796763)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor4</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.397%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l130.55895026917517,-34.34181859789962a135,135 0 0 1 0.761079642622633,3.03607241665474Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(477.09237753650183,202.85023319597624)scale(0.0938866254708908)rotate(-14.072853791613113)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.369%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l129.7276877373419,-37.35943032652814a135,135 0 0 1 0.8312625318332607,3.0176117286285233Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(476.4925886621431,199.86186210313426)scale(0.09421767956133521)rotate(-15.40130209019776)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_05</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.369%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l128.8444996179223,-40.300061019894464a135,135 0 0 1 0.8831881194196001,2.940630693366323Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(475.8762360252519,196.90397929650922)scale(0.09270592105166188)rotate(-16.717098500224438)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor9</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.362%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l127.90181951479762,-43.19866392383143a135,135 0 0 1 0.9426801031246868,2.8986029039369683Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(475.1479866521111,194.00815844867225)scale(0.09236035222357103)rotate(-18.015498563483845)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_06</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.359%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l126.92189465666269,-45.998181015807766a135,135 0 0 1 0.979924858134936,2.799517091976334Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(473.9962106923742,191.3125662990175)scale(0.08954039076062598)rotate(-19.291757821766907)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_11</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.35%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l125.90492022957285,-48.71294552770236a135,135 0 0 1 1.0169744270898349,2.714764511894593Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(473.4028736477205,188.4900518817724)scale(0.08807527619570182)rotate(-20.536387358655077)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_11</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.342%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l124.84690654047144,-51.36389711922948a135,135 0 0 1 1.058013689101415,2.650951591527118Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(472.4281072497422,185.85776505127504)scale(0.08677862708388825)rotate(-21.757294604497133)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_12</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.337%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l123.76584740214287,-53.91674152644505a135,135 0 0 1 1.0810591383285697,2.5528444072155736Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(470.91080114426046,183.5253095828539)scale(0.08358240524364295)rotate(-22.951316587153485)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m20_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.327%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l122.64817057149622,-56.41299722107636a135,135 0 0 1 1.1176768306466442,2.4962556946313086Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(470.12664516951975,180.9418341575873)scale(0.08290360094570942)rotate(-24.120034792693957)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_12</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.322%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l121.51588852136716,-58.812318750944236a135,135 0 0 1 1.1322820501290636,2.3993215298678763Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(469.1116687886464,178.5219484451327)scale(0.08053554881822117)rotate(-25.26344922111855)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_15</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.313%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l120.34005266563402,-61.18228276578464a135,135 0 0 1 1.1758358557331405,2.3699640148404058Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(467.98833565777073,176.19218543413956)scale(0.08031993015390568)rotate(-26.38788581670616)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_13</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.312%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l119.12851970158313,-63.50902135688692a135,135 0 0 1 1.211532964050889,2.326738591102277Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(467.08593789443387,173.76264895860302)scale(0.08003185024149323)rotate(-27.50599646801487)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_14</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.309%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l117.89742818829001,-65.76622557656019a135,135 0 0 1 1.2310915132931228,2.2572042196732696Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 253, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(465.6993343414154,171.62986111139085)scale(0.07816060679229692)rotate(-28.608292258626136)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_17</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.303%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l116.74725705116637,-67.78700444059231a135,135 0 0 1 1.1501711371236354,2.020778864032124Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 233, 183); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(464.7879019681595,169.42085346893597)scale(0.07099414039241463)rotate(-29.647328606447672)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_19</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.274%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l115.60096104562115,-69.72386826136932a135,135 0 0 1 1.1462960055452243,1.9368638207770061Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 25, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(463.73763905486504,167.44666841174296)scale(0.06881006416890526)rotate(-30.618361053270178)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_17</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.265%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l114.46206669354954,-71.57817606115297a135,135 0 0 1 1.1388943520716026,1.8543077997836548Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(181, 39, 15); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(462.6919636141757,165.55135551341053)scale(0.06662017070593804)rotate(-31.557763778697847)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_18</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.257%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l113.32989166853427,-73.35758757209976a135,135 0 0 1 1.1321750250152718,1.7794115109467867Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 102, 75); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(461.64460387614565,163.73472663724087)scale(0.06464427369418288)rotate(-32.46711826880039)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_08</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.249%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l112.24464525547273,-75.00759702505528a135,135 0 0 1 1.0852464130615402,1.6500094529555156Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(118, 8, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(460.6913059792369,161.9780875427282)scale(0.06067821945091796)rotate(-33.33377263501984)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_02</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.233%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l111.14383882604884,-76.62928350839046a135,135 0 0 1 1.1008064294238977,1.621686483335182Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(243, 105, 0); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(459.63055186802717,160.36399027957)scale(0.06023636854531516)rotate(-34.1687972798444)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_07</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.231%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l110.03687732683424,-78.21052121140245a135,135 0 0 1 1.1069614992145915,1.5812377030119933Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 136, 158); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(458.5739589484894,158.77434727761693)scale(0.05935195754725096)rotate(-34.99433300825052)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_27</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.228%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l108.92063175080936,-79.75773303576636a135,135 0 0 1 1.1162455760248804,1.547211824363913Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 0, 73); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(457.50189453587234,157.22498878549533)scale(0.058688028016115085)rotate(-35.80879833416844)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_01</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.225%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l107.80058009364676,-81.26521353859381a135,135 0 0 1 1.1200516571626054,1.5074805028274483Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(132, 216, 42); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(456.33613775579784,155.7771335716571)scale(0.057677184613084145)rotate(-36.612193257598165)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor15</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.221%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l106.6962041168445,-82.70985447367596a135,135 0 0 1 1.1043759768022596,1.4446409350821483Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 49, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(455.39386098100397,154.21861950716155)scale(0.05602697111080266)rotate(-37.39661034819096)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_02</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.214%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l105.57246982633376,-84.1394890331992a135,135 0 0 1 1.1237342905107397,1.4296345595232367Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(233, 164, 1); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(454.29622029297036,152.80635183049648)scale(0.05602697111080266)rotate(-38.168375550225846)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_01</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.214%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l104.4390263470457,-85.54232739223951a135,135 0 0 1 1.1334434792880614,1.4028383590403166Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(453.2771632792042,151.34109453590676)scale(0.05564969120494853)rotate(-38.9369777801212)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor5</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.213%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l103.30133873122912,-86.9127920178374a135,135 0 0 1 1.1376876158165743,1.37046462559789Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(452.17801557658333,149.96281612088222)scale(0.05498113084799778)rotate(-39.697672579667824)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor3</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.21%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l102.15054161683122,-88.26248833671093a135,135 0 0 1 1.1507971143979034,1.3496963188735265Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(451.14179991147574,148.54672751742558)scale(0.054870614522003446)rotate(-40.452041434935495)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_05</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.209%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l101.0068380210165,-89.56907207845873a135,135 0 0 1 1.1437035958147135,1.3065837417478008Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(449.9434362562748,147.30178313609696)scale(0.053634089778285134)rotate(-41.196921373784676)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor6</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.205%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l99.88152733088111,-90.82224671329405a135,135 0 0 1 1.125310690135393,1.253174634835318Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(448.88633802812853,145.9932559690649)scale(0.052078033379631725)rotate(-41.92282347979693)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor7</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.199%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l98.75083356102631,-92.05038224256583a135,135 0 0 1 1.1306937698548012,1.228135529271782Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(447.86449370825625,144.689246671411)scale(0.05173049707131557)rotate(-42.63449221118151)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_07</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.197%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l97.60503979834293,-93.26444234521479a135,135 0 0 1 1.1457937626833825,1.2140601026489577Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(446.5043132241001,143.7098031173)scale(0.05146575033403785)rotate(-43.34299797042661)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor16</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.197%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l96.4443212461885,-94.46424137927534a135,135 0 0 1 1.1607185521544352,1.1997990340605469Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(445.5878072001517,142.30983089462345)scale(0.05172304321499706)rotate(-44.05150372967171)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_04</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.197%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l95.28469488413982,-95.63381682666636a135,135 0 0 1 1.1596263620486695,1.1695754473910256Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(444.39948794390307,141.18824620365766)scale(0.050958853124107764)rotate(-44.75526503070756)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor8</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.194%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l94.11088618588954,-96.78915797395153a135,135 0 0 1 1.1738086982502836,1.1553411472851707Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(443.11014398498935,140.18425646693734)scale(0.05080582344191579)rotate(-45.45428187353423)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor19</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.194%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l92.93928706892679,-97.91470226232433a135,135 0 0 1 1.1715991169627529,1.1255442883727937Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(441.97512288254836,139.04434329830903)scale(0.050137623593364815)rotate(-46.14855425815165)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor18</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.192%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l91.75969424245157,-99.02100036119509a135,135 0 0 1 1.179592826475215,1.1062980988707665Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(440.89585796784917,137.86276684146625)scale(0.050011214780679956)rotate(-46.836500698490056)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_7_08b</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.191%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l90.58903813700951,-100.0930875206247a135,135 0 0 1 1.1706561054420632,1.0720871594296142Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(439.88834056383865,136.62842811693972)scale(0.049255780254218236)rotate(-47.5165397084798)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_08</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.187%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l89.42819086712821,-101.13159090132267a135,135 0 0 1 1.1608472698813017,1.0385033806979607Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(438.6350683465125,135.7060719220977)scale(0.04821643760069637)rotate(-48.18392682991157)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_19</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.184%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l88.27235643188403,-102.14201432301215a135,135 0 0 1 1.1558344352441736,1.010423421689481Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(437.4990542240864,134.69219117578388)scale(0.047536966702948096)rotate(-48.84024354885514)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_26</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.181%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l87.15064768204086,-103.10074979650142a135,135 0 0 1 1.1217087498431795,0.9587354734892699Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(436.43691820736166,133.6586383359895)scale(0.045746472383059684)rotate(-49.47916392103156)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_03</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.174%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l86.03001391397756,-104.03766964883839a135,135 0 0 1 1.1206337680632998,0.9369198523369704Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(435.34213788931976,132.7118744055456)scale(0.045296600387295154)rotate(-50.10226943251047)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_09</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.172%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l84.96303088955067,-104.91083538920685a135,135 0 0 1 1.0669830244268894,0.8731657403684636Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(434.3354633156413,131.7416707854558)scale(0.04282417835688938)rotate(-50.704815625082745)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_6_11a</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.163%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l83.89302520134541,-105.76842781551792a135,135 0 0 1 1.0700056882052564,0.8575924263110721Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 253, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(433.27953553224074,130.88901410244347)scale(0.042592263428021124)rotate(-51.2883839848181)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_07</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.162%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l82.8614405950527,-106.57852345811773a135,135 0 0 1 1.0315846062927108,0.8100956425998049Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 233, 183); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(432.2796916536859,130.02606286138908)scale(0.040779661072713164)rotate(-51.85771896992577)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_24</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.155%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l81.82203395174248,-107.37855819482724a135,135 0 0 1 1.0394066433102154,0.800034736709506Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 25, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(431.2559023084967,129.23160454540917)scale(0.040779661072713164)rotate(-52.41440206647553)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_14</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.155%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l80.78684562109528,-108.15953760345556a135,135 0 0 1 1.0351883306472018,0.7809794086283262Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(181, 39, 15); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(430.2514581680584,128.42577711112858)scale(0.040331942599925)rotate(-52.96792219088576)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_03</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.153%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l79.75021673983656,-108.9261352015626a135,135 0 0 1 1.0366288812587214,0.7665975981070403Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 102, 75); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(429.23288666262147,127.65525729266915)scale(0.04010557414410469)rotate(-53.5166978570868)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_05</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.152%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l78.71842341625832,-109.67410731279591a135,135 0 0 1 1.031793323578242,0.7479721112333095Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(118, 8, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(428.2128514632291,126.90629218214731)scale(0.03964808327525637)rotate(-54.06072906507853)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_04</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.15%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l77.71008856313703,-110.39086074267833a135,135 0 0 1 1.0083348531212835,0.7167534298824165Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(243, 105, 0); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(427.2413755186498,126.13671856530317)scale(0.03851927835599021)rotate(-54.593689870582125)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_07_18</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.146%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l76.70749361645372,-111.0898754301296a135,135 0 0 1 1.0025949466833168,0.6990146874512675Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 136, 158); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(426.1843413425619,125.52975352386744)scale(0.037984063318678735)rotate(-55.11558027359746)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m21_assess</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.144%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l75.7047819216062,-111.77560554164782a135,135 0 0 1 1.0027116948475197,0.6857301115182253Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 0, 73); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(425.27143434354383,124.73294147662853)scale(0.03783851633483337)rotate(-55.632726218403604)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_02</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.143%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l74.72077006837122,-112.4357884322852a135,135 0 0 1 0.9840118532349749,0.6601828906373726Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(132, 216, 42); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(424.3106618341763,124.04006104175919)scale(0.03692997244605608)rotate(-56.14196473286103)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_09</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.14%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l73.75597126839949,-113.07102503406887a135,135 0 0 1 0.9647987999717316,0.6352366017836744Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 49, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(423.36819147418265,123.3717192900759)scale(0.036020442875057346)rotate(-56.63855135876048)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_12</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.136%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l72.79832513501069,-113.68994615856441a135,135 0 0 1 0.9576461333888062,0.618921124495543Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(233, 164, 1); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(422.427438706992,122.73768911242988)scale(0.035565307959929696)rotate(-57.1256490682415)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_19</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.134%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l71.85441434411158,-114.2888583338933a135,135 0 0 1 0.9439107908991105,0.5989121753288913Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(421.5021823091563,122.1142268484918)scale(0.03488214236309236)rotate(-57.6048393473738)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_13</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.132%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l70.9119183498527,-114.87601941198966a135,135 0 0 1 0.9424959942588771,0.5871610780963579Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(420.57343865790057,121.52070218862674)scale(0.03465429687097796)rotate(-58.07770368222714)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_12</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.131%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l69.9773714752278,-115.44768287591559a135,135 0 0 1 0.9345468746248997,0.5716634639259297Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(419.65448947637327,120.93327136831297)scale(0.03419842029674259)rotate(-58.545823558871234)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_20</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.129%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l69.03821635752222,-116.01174372524513a135,135 0 0 1 0.9391551177055817,0.564060849329536Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(418.72655135064883,120.37178521674625)scale(0.03419842029674259)rotate(-59.010780463375795)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.129%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l68.10094980084142,-116.56440552854579a135,135 0 0 1 0.9372665566807967,0.5526618033006656Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 161, 90); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(417.8024236214276,119.81231999401842)scale(0.033970389164490654)rotate(-59.474155881810645)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_23</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.128%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l67.16572422060514,-117.10578760215746a135,135 0 0 1 0.9352255802362777,0.5413820736116719Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(25, 211, 243); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(416.880132083451,119.2639952912199)scale(0.03374229610203975)rotate(-59.93436832810602)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_03</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.127%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l66.25217032688869,-117.62503954080921a135,135 0 0 1 0.913553893716454,0.5192519386517489Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 102, 146); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(415.9845857561227,118.70969291090351)scale(0.03282930404807282)rotate(-60.38667334405267)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_11</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.124%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l65.35416652855953,-118.12634302879823a135,135 0 0 1 0.8980037983291567,0.5013034879890199Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(182, 232, 128); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(415.0961105170916,118.19307135214527)scale(0.03214090523288642)rotate(-60.827907957511115)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_06</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.121%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l64.46546615765111,-118.61367405605789a135,135 0 0 1 0.888700370908424,0.48733102725965693Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 151, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(414.22658532426556,117.67823123759256)scale(0.031686667535348205)rotate(-61.26123514062084)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_05_15</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.119%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l63.704583635992314,-119.02405649180699a135,135 0 0 1 0.7608825216587931,0.4103824357490993Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(254, 203, 82); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(413.50692654644945,117.08170901581926)scale(0.0271005444157099)rotate(-61.65976963019625)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_08</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.102%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l62.94768169527026,-119.4260832866545a135,135 0 0 1 0.7569019407220523,0.40202679484751513Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(198, 202, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(412.75864465217074,116.67149942436282)scale(0.026870581716326183)rotate(-62.025092912307)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_12</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.101%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l62.194857424428804,-119.8198635867817a135,135 0 0 1 0.7528242708414581,0.39378030012719023Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(247, 167, 153); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(412.0142691602473,116.26945966414681)scale(0.026640556302459133)rotate(-62.387253222278275)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_09</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.1%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l61.44620585698034,-120.20550647030107a135,135 0 0 1 0.7486515674484622,0.38564288351938103Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(51, 255, 201); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(411.27389597215523,115.87548311376176)scale(0.026410468148593637)rotate(-62.74625056011007)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0993%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l60.71513303387396,-120.57641817817856a135,135 0 0 1 0.7310728231063806,0.3709117078774824Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(224, 198, 253); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(410.55366200150814,115.47724118251338)scale(0.025719826991651627)rotate(-63.09892195366291)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_18</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0966%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l59.98849782642206,-120.93957221906052a135,135 0 0 1 0.7266352074518991,0.363154040881966Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 219, 192); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(409.8304043720009,115.1142589792818)scale(0.025487599067173003)rotate(-63.44526740293668)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_16</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0958%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l59.266386689750554,-121.29507578109245a135,135 0 0 1 0.7221111366715078,0.35550356203192734Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(122, 230, 248); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(409.12017375089374,114.7414939883308)scale(0.025259085385309733)rotate(-63.78844988007103)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_22</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0949%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l58.54216886545947,-121.64626777886787a135,135 0 0 1 0.7242178242910811,0.3511919977754161Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 204, 219); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(408.39794669678963,114.39981242651137)scale(0.02525723081612884)rotate(-64.13005087113561)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_14</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0949%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l57.822604551528336,-121.98994385963778a135,135 0 0 1 0.7195643139311372,0.3436760807699102Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(232, 248, 214); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(407.6899054628376,114.03879587082959)scale(0.02502862025487541)rotate(-64.47007037613048)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_15</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.094%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l57.10777577397686,-122.32621119837393a135,135 0 0 1 0.7148287775514746,0.33626733873614967Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 253, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(406.9822447663599,113.69378595008946)scale(0.024798092205456305)rotate(-64.80692690898587)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_13</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0931%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l56.40453370714129,-122.65206307796034a135,135 0 0 1 0.7032420668355712,0.32585187958640915Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 233, 183); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(406.2870457577153,113.34961545572462)scale(0.024336847247154292)rotate(-65.13903898363208)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_15</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0914%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l55.69943244520112,-122.97387212445771a135,135 0 0 1 0.7051012619401718,0.3218090464973784Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 25, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(405.5838530490388,113.03689978921348)scale(0.024335125625456925)rotate(-65.46798808613875)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_14</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0914%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l55.01971878978214,-123.2794814407219a135,135 0 0 1 0.6797136554189791,0.30560931626418153Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(181, 39, 15); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(404.9174678527335,112.68533788695865)scale(0.023413601174364574)rotate(-65.79061124436646)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_09_20</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0879%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l54.38607987574711,-123.56032662529205a135,135 0 0 1 0.633638914035032,0.28084518457015406Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 102, 75); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(404.29201465576637,112.3457731556819)scale(0.021794109409666574)rotate(-66.09583805582696)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_06</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0817%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l53.75100746015967,-123.83791502208788a135,135 0 0 1 0.6350724155874374,0.277588396795835Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(118, 8, 239); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(403.6649191794211,112.06104113213362)scale(0.02179549025784657)rotate(-66.38999446479926)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_08</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0817%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l53.114518282179844,-124.11223931446884a135,135 0 0 1 0.6364891779798256,0.27432429238095324Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(243, 105, 0); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(403.0330463546839,111.78714696757206)scale(0.02179549025784657)rotate(-66.68415087377156)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_07</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0817%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l52.47662911831049,-124.3832922718292a135,135 0 0 1 0.6378891638693531,0.27105295736036794Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(8, 136, 158); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(402.3997756914842,111.51650043211893)scale(0.02179549025784657)rotate(-66.97830728274386)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_05</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0817%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l51.83735678195514,-124.65106674978874a135,135 0 0 1 0.6392723363553543,0.26777447795953435Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 0, 73); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(401.76512388149155,111.24910865944207)scale(0.02179549025784657)rotate(-67.2724636917161)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_10</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0817%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l51.21050955702288,-124.90990237331094a135,135 0 0 1 0.6268472249322556,0.25883562352220224Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(132, 216, 42); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(401.14420042477957,110.97146809907842)scale(0.021332603729185072)rotate(-67.56345712854892)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_09</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.08%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l50.60309829696707,-125.15720691493354a135,135 0 0 1 0.6074112600558124,0.24730454162259718Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(255, 49, 255); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(400.54007335874087,110.70300395258238)scale(0.020636560833933056)rotate(-67.84654313503302)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_03_04</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0773%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l50.174431514143286,-125.32967095717017a135,135 0 0 1 0.42866678282378246,0.17246404223662637Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(233, 164, 1); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(400.1336958326159,110.27326165917808)scale(0.014592177025889103)rotate(-68.08376604549454)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_08_25</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0545%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l49.76596079542781,-125.49242664841549a135,135 0 0 1 0.4084707187154777,0.16275569124532296Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(399.7290132302444,110.08166131720029)scale(0.013891826264529368)rotate(-68.2751258599335)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_19</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0518%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l49.363898695353605,-125.65112616126791a135,135 0 0 1 0.40206210007420395,0.15869951285242223Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(399.31882299220854,109.94018447812527)scale(0.01365126931025912)rotate(-68.46015973009355)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">_m1_tutor17</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.051%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l48.97522056585223,-125.80313100446318a135,135 0 0 1 0.3886781295013719,0.15200484319527163Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(398.9435334617229,109.742172221414)scale(0.013190898022771002)rotate(-68.6404491420443)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_17</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0492%</tspan></text></g></g><g class=\"slice\"><path class=\"surface\" d=\"M350,235l48.58607438060941,-125.9539335482695a135,135 0 0 1 0.3891461852428222,0.1508025438063214Z\" style=\"pointer-events: all; stroke-width: 0; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(68, 68, 68); stroke-opacity: 1;\"/><g class=\"slicetext\"><text data-notex=\"1\" class=\"slicetext\" transform=\"translate(398.55607319718007,109.5914650304901)scale(0.013190898022771002)rotate(-68.81757558185558)\" text-anchor=\"middle\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\"><tspan class=\"line\" dy=\"0em\" x=\"0\" y=\"0\">tutor_04_18</tspan><tspan class=\"line\" dy=\"1.3em\" x=\"0\" y=\"0\">0.0492%</tspan></text></g></g></g></g><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-5ae365\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Name Percentage</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Pie\n", "# show_value_counts_pie('<PERSON> (F2011)')\n", "show_value_counts_pie('Problem Hierarchy')\n", "show_value_counts_pie('Problem Name')\n", "# show_value_counts_pie('Step Name')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-b12f0b\"><g class=\"clips\"><clipPath id=\"clipb12f0bxyplot\" class=\"plotclip\"><rect width=\"182.25\" height=\"101.25\"/></clipPath><clipPath id=\"clipb12f0bx2y2plot\" class=\"plotclip\"><rect width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath id=\"clipb12f0bx3y3plot\" class=\"plotclip\"><rect width=\"182.25\" height=\"101.25\"/></clipPath><clipPath id=\"clipb12f0bx4y4plot\" class=\"plotclip\"><rect width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx\"><rect x=\"80\" y=\"0\" width=\"182.25\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0by\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bxy\"><rect x=\"80\" y=\"100\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0by2\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bxy2\"><rect x=\"80\" y=\"100\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0by3\"><rect x=\"0\" y=\"268.75\" width=\"700\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bxy3\"><rect x=\"80\" y=\"268.75\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0by4\"><rect x=\"0\" y=\"268.75\" width=\"700\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bxy4\"><rect x=\"80\" y=\"268.75\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx2\"><rect x=\"302.75\" y=\"0\" width=\"182.24999999999997\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx2y\"><rect x=\"302.75\" y=\"100\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx2y2\"><rect x=\"302.75\" y=\"100\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx2y3\"><rect x=\"302.75\" y=\"268.75\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx2y4\"><rect x=\"302.75\" y=\"268.75\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx3\"><rect x=\"80\" y=\"0\" width=\"182.25\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx3y\"><rect x=\"80\" y=\"100\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx3y2\"><rect x=\"80\" y=\"100\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx3y3\"><rect x=\"80\" y=\"268.75\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx3y4\"><rect x=\"80\" y=\"268.75\" width=\"182.25\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx4\"><rect x=\"302.75\" y=\"0\" width=\"182.24999999999997\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx4y\"><rect x=\"302.75\" y=\"100\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx4y2\"><rect x=\"302.75\" y=\"100\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx4y3\"><rect x=\"302.75\" y=\"268.75\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb12f0bx4y4\"><rect x=\"302.75\" y=\"268.75\" width=\"182.24999999999997\" height=\"101.25\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"182.25\" height=\"101.25\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"302.75\" y=\"100\" width=\"182.24999999999997\" height=\"101.25\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"80\" y=\"268.75\" width=\"182.25\" height=\"101.25\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/><rect class=\"bg\" x=\"302.75\" y=\"268.75\" width=\"182.24999999999997\" height=\"101.25\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,175.04000000000002)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,148.82999999999998)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,122.62)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,201.25)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clipb12f0bxyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.36,101.25V5.06H3.28V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(1.804164859002169,21.724902386117126)scale(0.20268980477223414)rotate(90 0.078125 -4.796875)\">resolve_into_components</text></g><g class=\"point\"><path d=\"M4.01,101.25V28.24H6.93V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(5.47,41.01579175704988)scale(0.20268980477223417)rotate(90 0 -4.796875)\">identify_interaction</text></g><g class=\"point\"><path d=\"M7.65,101.25V28.77H10.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(9.10524945770065,50.91227765726679)scale(0.20268980477223408)rotate(90 0.0234375 -4.796875)\">couple_represents_net_zero_force</text></g><g class=\"point\"><path d=\"M11.3,101.25V40.72H14.22V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(12.760000000000002,59.351626898047705)scale(0.2026898047722341)rotate(90 0 -4.796875)\">represent_interaction_spring</text></g><g class=\"point\"><path d=\"M14.94,101.25V44.61H17.86V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(16.4,52.917114967462034)scale(0.20268980477223422)rotate(90 0 -4.796875)\">simple_step</text></g><g class=\"point\"><path d=\"M18.59,101.25V46.15H21.51V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(20.043906215439062,74.63539593010395)scale(0.19500110595001108)rotate(90 0.03125 -4.796875)\">replace_general_loads_with_force_and_couple</text></g><g class=\"point\"><path d=\"M22.23,101.25V50.26H25.15V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(23.689999999999998,67.77049891540128)scale(0.202689804772234)rotate(90 0 -4.796875)\">represent_interaction_cord</text></g><g class=\"point\"><path d=\"M25.88,101.25V53.34H28.8V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(27.34,77.42524945770064)scale(0.2026898047722342)rotate(90 0 -4.796875)\">represent_interaction_pin_connection</text></g><g class=\"point\"><path d=\"M29.52,101.25V61.07H32.44V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(30.97070951971989,81.97490784171237)scale(0.169883067979124)rotate(90 0.0546875 -4.796875)\">represent_interaction_contacting_body</text></g><g class=\"point\"><path d=\"M33.17,101.25V62.34H36.09V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(34.62450268437412,82.63883794857304)scale(0.17591410002825655)rotate(90 0.03125 -4.796875)\">statics_problem_force_and_moment</text></g><g class=\"point\"><path d=\"M36.81,101.25V63.81H39.73V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(38.25416485900217,80.23737527114963)scale(0.20268980477223378)rotate(90 0.078125 -4.796875)\">couple_related_to_forces</text></g><g class=\"point\"><path d=\"M40.46,101.25V66.14H43.38V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(41.92,78.4565726681128)scale(0.2026898047722343)rotate(90 0 -4.796875)\">find_moment_arm</text></g><g class=\"point\"><path d=\"M44.1,101.25V66.94H47.02V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(45.555249457700654,81.23121475054229)scale(0.20268980477223428)rotate(90 0.0234375 -4.796875)\">find_symmetry_plane</text></g><g class=\"point\"><path d=\"M47.75,101.25V69.6H50.67V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(49.2079046210884,85.8538542172397)scale(0.08940283356137178)rotate(90 0.0234375 -4.796875)\">replace_forces_in_opposite_sense_with_force_and_couple</text></g><g class=\"point\"><path d=\"M51.39,101.25V70.36H54.31V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(52.90697393228953,86.58237765390606)scale(0.16205918517911305)rotate(90 -0.3515625 -4.796875)\">judge_equilibrium_qualitatively</text></g><g class=\"point\"><path d=\"M55.04,101.25V72.47H57.96V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(56.5,87.07151632672604)scale(0.04409460882888059)rotate(90 0 -4.796875)\">anticipate_solved_variables~~determine_joint_is_solvable~~recognize_variable_solvable_from_subsystem</text></g><g class=\"point\"><path d=\"M58.68,101.25V73.19H61.6V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(60.13549887712544,88.14122981499304)scale(0.19204790931451182)rotate(90 0.0234375 -4.796875)\">rotation_sense_of_force</text></g><g class=\"point\"><path d=\"M62.33,101.25V74.7H65.25V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(63.79,88.42418163782652)scale(0.09364047172930673)rotate(90 0 -4.796875)\">moving_force_perpendicular_to_line_of_action</text></g><g class=\"point\"><path d=\"M65.97,101.25V79.56H68.89V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(67.43,91.00166935483871)scale(0.12438709677419353)rotate(90 0 -4.796875)\">find_linear_force_per_length</text></g><g class=\"point\"><path d=\"M69.62,101.25V80.75H72.54V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(71.08000000000001,91.40813878080415)scale(0.08508430609597925)rotate(90 0 -4.796875)\">represent_interaction_roller_connection</text></g><g class=\"point\"><path d=\"M73.26,101.25V81.33H76.18V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(74.71476554744525,91.64710598540145)scale(0.07444554744525549)rotate(90 0.0703125 -4.796875)\">recognize_equivalence_of_translated_forces</text></g><g class=\"point\"><path d=\"M76.91,101.25V81.92H79.83V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(78.37,92.10683520928596)scale(0.10878649314104819)rotate(90 0 -4.796875)\">moment_sign_sense_relation</text></g><g class=\"point\"><path d=\"M80.55,101.25V82.17H83.47V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(82.00689655172413,92.18637931034483)scale(0.0993103448275862)rotate(90 0.03125 -4.796875)\">moving_force_to_general_point</text></g><g class=\"point\"><path d=\"M84.2,101.25V82.25H87.12V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(85.66,92.09707842437224)scale(0.0723551112697846)rotate(90 0 -4.796875)\">identify_interaction~~body_draw_force_on</text></g><g class=\"point\"><path d=\"M87.84,101.25V83.79H90.76V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(89.29913845850193,92.69632882660613)scale(0.03675910391789202)rotate(90 0.0234375 -4.796875)\">find_moment_arm~~moment_sign_sense_relation~~rotation_sense_of_force</text></g><g class=\"point\"><path d=\"M91.49,101.25V84.03H94.41V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(92.92598996095927,93.13140546569994)scale(0.10244283324037924)rotate(90 0.234375 -4.796875)\">identify_two-force_member</text></g><g class=\"point\"><path d=\"M95.13,101.25V85.26H98.05V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(96.59,93.42242598908595)scale(0.03490313778990449)rotate(90 0 -4.796875)\">force_at_joint_implied_by_previous_analysis~~sense_if_assuming_tension</text></g><g class=\"point\"><path d=\"M98.78,101.25V85.42H101.7V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(100.23891965057555,93.55611151553757)scale(0.04609490877655944)rotate(90 0.0234375 -4.796875)\">moment_sign_sense_relation~~rotation_sense_of_force</text></g><g class=\"point\"><path d=\"M102.42,101.25V85.62H105.34V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(103.87147578534031,93.95838678010472)scale(0.10910994764397902)rotate(90 0.078125 -4.796875)\">equivalence_of_couples</text></g><g class=\"point\"><path d=\"M106.07,101.25V85.63H108.99V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(107.52776512333314,93.71444285469009)scale(0.057212842671550405)rotate(90 0.0390625 -4.796875)\">possible_interaction_for_nonuniform_contact</text></g><g class=\"point\"><path d=\"M109.71,101.25V86.18H112.63V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(111.16999999999999,93.97715378513146)scale(0.05465095194922935)rotate(90 0 -4.796875)\">represent_interaction_pin_in_slot_connection</text></g><g class=\"point\"><path d=\"M113.36,101.25V86.4H116.28V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(114.82,94.14641497461929)scale(0.06700507614213196)rotate(90 0 -4.796875)\">recognize_equivalence_from_motion</text></g><g class=\"point\"><path d=\"M117,101.25V86.55H119.92V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(118.46000000000001,94.52783806343906)scale(0.1308848080133556)rotate(90 0 -4.796875)\">interpret_equation</text></g><g class=\"point\"><path d=\"M120.65,101.25V86.57H123.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(122.11,94.00437055029734)scale(0.01967333947566799)rotate(90 0 -4.796875)\">determine_subsystem_is_solvable~~identify_external_load_points_on_section~~identify_internal_load_points_on_section</text></g><g class=\"point\"><path d=\"M124.29,101.25V86.73H127.21V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(125.74144204322201,94.57384282907663)scale(0.12171316306483297)rotate(90 0.0703125 -4.796875)\">gravitational_forces</text></g><g class=\"point\"><path d=\"M127.94,101.25V86.97H130.86V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(129.39845085701887,94.42705793013668)scale(0.06609676719461922)rotate(90 0.0234375 -4.796875)\">identify_forces_in_symmetry_plane</text></g><g class=\"point\"><path d=\"M131.58,101.25V87.25H134.5V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(133.03905673089884,94.44305574271212)scale(0.040246148317836766)rotate(90 0.0234375 -4.796875)\">rotation_sense_of_force~~motion_dependence_on_force</text></g><g class=\"point\"><path d=\"M135.23,101.25V87.32H138.15V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(136.6882078222832,94.65179903936873)scale(0.07646624924950686)rotate(90 0.0234375 -4.796875)\">motion_dependence_on_force</text></g><g class=\"point\"><path d=\"M138.87,101.25V87.53H141.79V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(140.32999999999998,94.91026185770751)scale(0.1084584980237154)rotate(90 0 -4.796875)\">body_draw_force_on</text></g><g class=\"point\"><path d=\"M142.52,101.25V88.18H145.44V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(143.977587868597,94.8795609646065)scale(0.034305868843046365)rotate(90 0.0703125 -4.796875)\">couple_represents_net_zero_force~~couple_related_to_forces</text></g><g class=\"point\"><path d=\"M146.16,101.25V88.41H149.08V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(147.61822651933701,95.10222928176796)scale(0.05675138121546963)rotate(90 0.03125 -4.796875)\">moment_about_point_due_to_couple</text></g><g class=\"point\"><path d=\"M149.81,101.25V89.19H152.73V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(151.26999999999998,95.42466666666667)scale(0.04266666666666667)rotate(90 0 -4.796875)\">represent_interaction_rigid_sliding_connection</text></g><g class=\"point\"><path d=\"M153.45,101.25V89.93H156.37V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(154.91,95.81740740740742)scale(0.04740740740740738)rotate(90 0 -4.796875)\">represent_interaction_fixed_connection</text></g><g class=\"point\"><path d=\"M157.1,101.25V91.16H160.02V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(158.55606719675708,96.44647411911444)scale(0.050339881509198645)rotate(90 0.078125 -4.796875)\">recognize_knowns_vs_unknowns</text></g><g class=\"point\"><path d=\"M160.74,101.25V91.76H163.66V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(162.19637632583792,96.7522151039457)scale(0.05153669919389051)rotate(90 0.0703125 -4.796875)\">find_angle_given_components</text></g><g class=\"point\"><path d=\"M164.39,101.25V92.26H167.31V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(165.84917447199265,96.92395806550353)scale(0.03522252831343738)rotate(90 0.0234375 -4.796875)\">recognize_conditions_for_full_equivalence</text></g><g class=\"point\"><path d=\"M168.03,101.25V92.44H170.95V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(169.49,96.9049306448039)scale(0.012493684910259255)rotate(90 0 -4.796875)\">sense_if_assuming_tension~~identify_external_load_points_on_section~~identify_internal_load_points_on_section</text></g><g class=\"point\"><path d=\"M171.68,101.25V92.59H174.6V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(173.14,97.1016741834085)scale(0.03787344540112066)rotate(90 0 -4.796875)\">find_net_force_for_linear_distribution</text></g><g class=\"point\"><path d=\"M175.32,101.25V92.71H178.24V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(176.76660629363553,97.24528179702519)scale(0.055303045633916866)rotate(90 0.2421875 -4.796875)\">statics_problem_collinear</text></g><g class=\"point\"><path d=\"M178.97,101.25V92.76H181.89V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(255, 255, 255); fill-opacity: 1; white-space: pre;\" transform=\"translate(180.43,97.24263949671773)scale(0.049540481400437604)rotate(90 0 -4.796875)\">centroid_of_composite_area</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"214.25\" transform=\"translate(81.82,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"214.25\" transform=\"translate(154.72,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"214.25\" transform=\"translate(227.62,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,201.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,175.04000000000002)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,148.82999999999998)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,122.62)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">6000</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x2y2\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x2\"/><g class=\"y2\"><path class=\"y2grid crisp\" transform=\"translate(0,177.38)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,153.51)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,129.65)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y2grid crisp\" transform=\"translate(0,105.78)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y2zl zl crisp\" transform=\"translate(0,201.25)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(302.75,100)\" clip-path=\"url('#clipb12f0bx2y2plot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.67,101.25V5.06H6.07V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(3.37,53.842807565099925)scale(0.14338659337587925)rotate(90 0 -4.796875)\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Choosing a Solvable Subsystem</text></g><g class=\"point\"><path d=\"M7.42,101.25V7.34H12.82V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(10.120000000000001,54.95229720486982)scale(0.13702612739957137)rotate(90 0 -4.796875)\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Equilibrium of a Single Subsystem</text></g><g class=\"point\"><path d=\"M14.18,101.25V13.25H19.58V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(16.866405487239696,58.084703083482665)scale(0.1740097633318915)rotate(90 0.078125 -4.796875)\">sequence Statics, unit Multiple Body Equilibrium - Trusses, module Method of Joints</text></g><g class=\"point\"><path d=\"M20.92,101.25V28.34H26.33V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(23.61623560838787,65.39292627220516)scale(0.12464912515026044)rotate(90 0.0703125 -4.796875)\">sequence Statics, unit Complex Interactions Between Bodies, module Statically Equivalent Loads</text></g><g class=\"point\"><path d=\"M27.67,101.25V33.81H33.07V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(30.36224471021159,68.00617479300828)scale(0.09926770929162833)rotate(90 0.078125 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Representing Interactions Between Bodies</text></g><g class=\"point\"><path d=\"M34.42,101.25V36.01H39.82V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(37.120000000000005,69.73302235929067)scale(0.2299460292983809)rotate(90 0 -4.796875)\">sequence Statics, unit Friction, module Friction</text></g><g class=\"point\"><path d=\"M41.17,101.25V36.6H46.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(43.861267289820624,69.46118840501404)scale(0.11177869029608818)rotate(90 0.078125 -4.796875)\">sequence Statics, unit Multiple Body Equilibrium - Frames, module Solving Multiple Subsystems</text></g><g class=\"point\"><path d=\"M47.92,101.25V48.81H53.32V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(50.61130001990843,75.56417877762294)scale(0.11135974517220784)rotate(90 0.078125 -4.796875)\">sequence Statics, unit Complex Interactions Between Bodies, module Couples</text></g><g class=\"point\"><path d=\"M54.67,101.25V61.3H60.08V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(57.375,81.45190249531227)scale(0.03687869609115823)rotate(90 0 -4.796875)\">sequence Statics, unit Complex Interactions Between Bodies, module Applications of Static Equivalency to Distributed Forces, section1 Simplifying 3D loadings to 2D or 1D loading</text></g><g class=\"point\"><path d=\"M61.42,101.25V62.73H66.82V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(64.11767081872053,82.34752932639981)scale(0.07453380094328214)rotate(90 0.03125 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Force</text></g><g class=\"point\"><path d=\"M68.17,101.25V68.25H73.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(70.86606805833573,84.99142121818701)scale(0.050328853302830996)rotate(90 0.078125 -4.796875)\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections</text></g><g class=\"point\"><path d=\"M74.92,101.25V73.48H80.32V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(77.61695244482381,87.57291098646508)scale(0.0433430069503719)rotate(90 0.0703125 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams</text></g><g class=\"point\"><path d=\"M81.67,101.25V73.63H87.07V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(84.37,87.58653912622701)scale(0.030548873219964058)rotate(90 0 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces, section1 Applying Force Equilibrium</text></g><g class=\"point\"><path d=\"M88.42,101.25V77.32H93.82V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(91.12,89.40172985254861)scale(0.024334562094826498)rotate(90 0 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces, section1 Applying Force and Moment Equilibrium</text></g><g class=\"point\"><path d=\"M95.17,101.25V82.51H100.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(97.86807455203025,91.99822250534277)scale(0.024645734012822614)rotate(90 0.078125 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces, section1 Combining Moments</text></g><g class=\"point\"><path d=\"M101.92,101.25V83.57H107.32V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(104.61803976642769,92.54373149037869)scale(0.027878877473082524)rotate(90 0.0703125 -4.796875)\">sequence Statics, unit Multiple Body Equilibrium - Frames, module Drawing FBDs of Multiple Subsystems</text></g><g class=\"point\"><path d=\"M108.67,101.25V83.91H114.07V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(111.36766370250606,92.73938740680859)scale(0.0332273421359921)rotate(90 0.0703125 -4.796875)\">sequence Statics, unit Multiple Body Equilibrium - Trusses, module Method of Sections</text></g><g class=\"point\"><path d=\"M115.43,101.25V85.6H120.83V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(118.12838374681569,93.53526438390746)scale(0.022986711954650824)rotate(90 0.0703125 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces</text></g><g class=\"point\"><path d=\"M122.18,101.25V87.18H127.58V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(124.87827747095791,94.33251475909351)scale(0.024498190820796028)rotate(90 0.0703125 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces</text></g><g class=\"point\"><path d=\"M128.92,101.25V87.18H134.32V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(131.62,94.28224825632084)scale(0.014019180470793367)rotate(90 0 -4.796875)\">sequence Statics, unit Complex Interactions Between Bodies, module Applications of Static Equivalency to Distributed Forces, section1 Center of Gravity and Centroid</text></g><g class=\"point\"><path d=\"M135.67,101.25V88.63H141.07V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(138.36879067806356,95.01425236689792)scale(0.015479320786538393)rotate(90 0.078125 -4.796875)\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces, section1 Combining Concurrent Forces</text></g><g class=\"point\"><path d=\"M142.42,101.25V92.92H147.82V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(145.12,97.14192271735743)scale(0.011866625116858832)rotate(90 0 -4.796875)\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Drawing FBDs of a Single Subsystem</text></g><g class=\"point\"><path d=\"M149.17,101.25V94.13H154.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(151.87,97.76522852422907)scale(0.01568281938325992)rotate(90 0 -4.796875)\">sequence Statics, unit Moments of Inertia, module Second Moment of Area</text></g><g class=\"point\"><path d=\"M155.92,101.25V95.29H161.32V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(158.61948198457466,95.27404512489927)scale(0.006630597444457242)\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Pin Connections</text></g><g class=\"point\"><path d=\"M162.67,101.25V97.25H168.08V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(165.375,97.22122141623488)scale(0.011959930915371385)\">sequence Statics, unit Moments of Inertia, module Mass Moment of Inertia</text></g><g class=\"point\"><path d=\"M169.43,101.25V98.76H174.83V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(172.1294919081671,98.74435077154686)scale(0.006503575461046299)\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Other Connections</text></g><g class=\"point\"><path d=\"M176.18,101.25V100.3H181.58V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-outside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\" transform=\"translate(178.879490527587,100.28430824967921)scale(0.006521246886557483)\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Fixed Connections</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"214.25\" transform=\"translate(306.12,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"214.25\" transform=\"translate(373.62,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10</text></g><g class=\"x2tick\"><text text-anchor=\"middle\" x=\"0\" y=\"214.25\" transform=\"translate(441.12,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20</text></g></g><g class=\"yaxislayer-above\"><g class=\"y2tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,201.25)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,177.38)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5k</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,153.51)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,129.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">15k</text></g><g class=\"y2tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,105.78)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20k</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x3y3\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x3\"/><g class=\"y3\"><path class=\"y3grid crisp\" transform=\"translate(0,341.15999999999997)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y3grid crisp\" transform=\"translate(0,312.32)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y3grid crisp\" transform=\"translate(0,283.47)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y3zl zl crisp\" transform=\"translate(0,370)\" d=\"M80,0h182.25\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,268.75)\" clip-path=\"url('#clipb12f0bx3y3plot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.36,101.25V5.06H3.28V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(1.8865075921908891,13.62047722342733)scale(0.2026898047722342)rotate(90 -0.328125 -4.796875)\">_m9_assess</text></g><g class=\"point\"><path d=\"M4.01,101.25V17.36H6.93V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(5.5365075921908895,25.920477223427326)scale(0.2026898047722342)rotate(90 -0.328125 -4.796875)\">_m2_assess</text></g><g class=\"point\"><path d=\"M7.65,101.25V23.9H10.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(9.11,32.49848156182212)scale(0.2026898047722342)rotate(90 0 -4.796875)\">tutor_17_09</text></g><g class=\"point\"><path d=\"M11.3,101.25V29.69H14.22V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(12.760000000000002,38.288481561822124)scale(0.2026898047722342)rotate(90 0 -4.796875)\">tutor_05_03</text></g><g class=\"point\"><path d=\"M14.94,101.25V31.25H17.86V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(16.4,39.848481561822126)scale(0.2026898047722342)rotate(90 0 -4.796875)\">tutor_12_10</text></g><g class=\"point\"><path d=\"M18.59,101.25V34.02H21.51V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(20.114924078091107,43.35481561822127)scale(0.2026898047722343)rotate(90 -0.3203125 -4.796875)\">_m11_assess</text></g><g class=\"point\"><path d=\"M22.23,101.25V35.49H25.15V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(23.756507592190886,44.05047722342732)scale(0.20268980477223408)rotate(90 -0.328125 -4.796875)\">_m1_assess</text></g><g class=\"point\"><path d=\"M25.88,101.25V35.81H28.8V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(27.406507592190888,44.370477223427336)scale(0.2026898047722343)rotate(90 -0.328125 -4.796875)\">_m3_assess</text></g><g class=\"point\"><path d=\"M29.52,101.25V41.43H32.44V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(31.044924078091103,50.76481561822125)scale(0.20268980477223408)rotate(90 -0.3203125 -4.796875)\">_m17_assess</text></g><g class=\"point\"><path d=\"M33.17,101.25V53.98H36.09V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(34.69650759219089,62.54047722342733)scale(0.2026898047722343)rotate(90 -0.328125 -4.796875)\">_m5_assess</text></g><g class=\"point\"><path d=\"M36.81,101.25V54.57H39.73V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(38.269999999999996,63.168481561822105)scale(0.20268980477223383)rotate(90 0 -4.796875)\">tutor_17_08</text></g><g class=\"point\"><path d=\"M40.46,101.25V54.61H43.38V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(41.98650759219089,63.17047722342733)scale(0.2026898047722343)rotate(90 -0.328125 -4.796875)\">_m7_assess</text></g><g class=\"point\"><path d=\"M44.1,101.25V55.61H47.02V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(45.52199566160521,64.24648590021692)scale(0.2026898047722343)rotate(90 0.1875 -4.796875)\">tutor_15_14</text></g><g class=\"point\"><path d=\"M47.75,101.25V58.74H50.67V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(49.27650759219089,67.30047722342734)scale(0.2026898047722343)rotate(90 -0.328125 -4.796875)\">_m4_assess</text></g><g class=\"point\"><path d=\"M51.39,101.25V62.6H54.31V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(52.91650759219089,71.16047722342734)scale(0.2026898047722343)rotate(90 -0.328125 -4.796875)\">_m6_assess</text></g><g class=\"point\"><path d=\"M55.04,101.25V64.75H57.96V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(56.5,73.34848156182213)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_05_07</text></g><g class=\"point\"><path d=\"M58.68,101.25V74.83H61.6V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(60.2049240780911,84.16481561822125)scale(0.2026898047722343)rotate(90 -0.3203125 -4.796875)\">_m22_assess</text></g><g class=\"point\"><path d=\"M62.33,101.25V75.41H65.25V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(63.8549240780911,84.74481561822125)scale(0.2026898047722343)rotate(90 -0.3203125 -4.796875)\">_m18_assess</text></g><g class=\"point\"><path d=\"M65.97,101.25V75.98H68.89V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(67.49492407809112,85.31481561822126)scale(0.2026898047722343)rotate(90 -0.3203125 -4.796875)\">_m12_assess</text></g><g class=\"point\"><path d=\"M69.62,101.25V76.1H72.54V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(71.1465075921909,84.66047722342734)scale(0.2026898047722343)rotate(90 -0.328125 -4.796875)\">_m8_assess</text></g><g class=\"point\"><path d=\"M73.26,101.25V76.1H76.18V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(74.78492407809111,85.43481561822125)scale(0.2026898047722343)rotate(90 -0.3203125 -4.796875)\">_m10_assess</text></g><g class=\"point\"><path d=\"M76.91,101.25V77.3H79.83V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(78.37,85.89848156182212)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_12_17</text></g><g class=\"point\"><path d=\"M80.55,101.25V77.96H83.47V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(82.00999999999999,85.78572668112798)scale(0.20268980477223433)rotate(90 0 -4.796875)\">tutor_6_05</text></g><g class=\"point\"><path d=\"M84.2,101.25V78.18H87.12V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(85.66,86.77848156182213)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_15_11</text></g><g class=\"point\"><path d=\"M87.84,101.25V79.47H90.76V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(89.30000000000001,88.06848156182213)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_12_01</text></g><g class=\"point\"><path d=\"M91.49,101.25V80.71H94.41V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(93.03075921908892,89.02503253796095)scale(0.20268980477223433)rotate(90 -0.3984375 -4.796875)\">_m1_tutor1</text></g><g class=\"point\"><path d=\"M95.13,101.25V81.03H98.05V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(96.59,89.62848156182213)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_17_03</text></g><g class=\"point\"><path d=\"M98.78,101.25V82.01H101.7V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(100.24000000000001,90.60848156182213)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_11_03</text></g><g class=\"point\"><path d=\"M102.42,101.25V82.04H105.34V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(103.9449240780911,91.37481561822126)scale(0.2026898047722343)rotate(90 -0.3203125 -4.796875)\">_m14_assess</text></g><g class=\"point\"><path d=\"M106.07,101.25V82.08H108.99V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(107.53,90.67848156182212)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_12_05</text></g><g class=\"point\"><path d=\"M109.71,101.25V82.3H112.63V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(111.2349240780911,91.63481561822125)scale(0.2026898047722343)rotate(90 -0.3203125 -4.796875)\">_m15_assess</text></g><g class=\"point\"><path d=\"M113.36,101.25V82.72H116.28V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(114.82,91.31848156182213)scale(0.2026898047722343)rotate(90 0 -4.796875)\">tutor_08_01</text></g><g class=\"point\"><path d=\"M117,101.25V83.15H119.92V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(118.42199566160521,91.78648590021693)scale(0.2026898047722343)rotate(90 0.1875 -4.796875)\">tutor_12_06</text></g><g class=\"point\"><path d=\"M120.65,101.25V83.35H123.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(122.0719956616052,91.98648590021688)scale(0.20268980477223333)rotate(90 0.1875 -4.796875)\">tutor_11_04</text></g><g class=\"point\"><path d=\"M124.29,101.25V83.5H127.21V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(125.75,92.09848156182208)scale(0.20268980477223333)rotate(90 0 -4.796875)\">tutor_12_11</text></g><g class=\"point\"><path d=\"M127.94,101.25V83.74H130.86V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(129.4,92.33848156182216)scale(0.2026898047722353)rotate(90 0 -4.796875)\">tutor_13_02</text></g><g class=\"point\"><path d=\"M131.58,101.25V83.84H134.5V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(133.04000000000002,92.43848156182209)scale(0.20268980477223333)rotate(90 0 -4.796875)\">tutor_12_18</text></g><g class=\"point\"><path d=\"M135.23,101.25V83.87H138.15V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(136.69,92.46848156182217)scale(0.2026898047722353)rotate(90 0 -4.796875)\">tutor_15_08</text></g><g class=\"point\"><path d=\"M138.87,101.25V84.23H141.79V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(140.32999999999998,92.82848156182209)scale(0.20268980477223333)rotate(90 0 -4.796875)\">tutor_11_27</text></g><g class=\"point\"><path d=\"M142.52,101.25V84.33H145.44V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(143.98000000000002,92.92848156182208)scale(0.20268980477223333)rotate(90 0 -4.796875)\">tutor_11_10</text></g><g class=\"point\"><path d=\"M146.16,101.25V84.44H149.08V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(147.62,93.03848156182217)scale(0.2026898047722353)rotate(90 0 -4.796875)\">tutor_04_05</text></g><g class=\"point\"><path d=\"M149.81,101.25V84.52H152.73V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(151.2319956616052,93.15648590021688)scale(0.20268980477223333)rotate(90 0.1875 -4.796875)\">tutor_22_06</text></g><g class=\"point\"><path d=\"M153.45,101.25V85.06H156.37V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(154.8719956616052,93.69648590021697)scale(0.2026898047722353)rotate(90 0.1875 -4.796875)\">tutor_15_04</text></g><g class=\"point\"><path d=\"M157.1,101.25V85.19H160.02V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(158.56,93.78848156182217)scale(0.2026898047722353)rotate(90 0 -4.796875)\">tutor_04_10</text></g><g class=\"point\"><path d=\"M160.74,101.25V85.19H163.66V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(162.2,93.78848156182208)scale(0.20268980477223333)rotate(90 0 -4.796875)\">tutor_22_07</text></g><g class=\"point\"><path d=\"M164.39,101.25V85.24H167.31V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(165.85,93.06572668112801)scale(0.20268980477223533)rotate(90 0 -4.796875)\">tutor_6_07</text></g><g class=\"point\"><path d=\"M168.03,101.25V85.24H170.95V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(169.45199566160522,93.87648590021688)scale(0.20268980477223333)rotate(90 0.1875 -4.796875)\">tutor_17_06</text></g><g class=\"point\"><path d=\"M171.68,101.25V85.34H174.6V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(173.10357917570497,93.20214750542296)scale(0.20268980477223336)rotate(90 0.1796875 -4.796875)\">tutor_7_16</text></g><g class=\"point\"><path d=\"M175.32,101.25V85.37H178.24V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(176.78,93.96848156182217)scale(0.2026898047722353)rotate(90 0 -4.796875)\">tutor_22_21</text></g><g class=\"point\"><path d=\"M178.97,101.25V85.5H181.89V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(180.50836585365855,94.31846341463415)scale(0.1966829268292683)rotate(90 -0.3984375 -4.796875)\">_m1_tutor13</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(81.82,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(154.72,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20</text></g><g class=\"x3tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(227.62,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40</text></g></g><g class=\"yaxislayer-above\"><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,341.15999999999997)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2000</text></g><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,312.32)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4000</text></g><g class=\"y3tick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,283.47)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">6000</text></g></g><g class=\"overaxes-above\"/></g><g class=\"subplot x4y4\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x4\"/><g class=\"y4\"><path class=\"y4grid crisp\" transform=\"translate(0,323.65)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"y4grid crisp\" transform=\"translate(0,277.3)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"y4zl zl crisp\" transform=\"translate(0,370)\" d=\"M302.75,0h182.24999999999997\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(302.75,268.75)\" clip-path=\"url('#clipb12f0bx4y4plot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.36,101.25V5.06H3.28V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(1.8104989154013014,20.49292841648589)scale(0.20268980477223414)rotate(90 0.046875 -4.796875)\">q1_A UpdateComboBox</text></g><g class=\"point\"><path d=\"M4.01,101.25V25.74H6.93V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(5.47,42.06603036876355)scale(0.20268980477223414)rotate(90 0 -4.796875)\">q1_A UpdateRadioButton</text></g><g class=\"point\"><path d=\"M7.65,101.25V56.87H10.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(9.098915401301518,72.30451193058568)scale(0.20268980477223414)rotate(90 0.0546875 -4.796875)\">q2_B UpdateComboBox</text></g><g class=\"point\"><path d=\"M11.3,101.25V59.27H14.22V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(12.760000000000002,75.87156182212581)scale(0.20268980477223414)rotate(90 0 -4.796875)\">q1_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M14.94,101.25V63.49H17.86V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(16.4,79.81919739696312)scale(0.20268980477223414)rotate(90 0 -4.796875)\">q2_B UpdateRadioButton</text></g><g class=\"point\"><path d=\"M18.59,101.25V66.08H21.51V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(20.05,82.68156182212581)scale(0.20268980477223428)rotate(90 0 -4.796875)\">q2_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M22.23,101.25V66.57H25.15V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(23.689999999999998,83.17156182212578)scale(0.20268980477223403)rotate(90 0 -4.796875)\">q3_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M25.88,101.25V69.78H28.8V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(27.34,86.12503253796095)scale(0.20268980477223428)rotate(90 0 -4.796875)\">q3_C UpdateRadioButton</text></g><g class=\"point\"><path d=\"M29.52,101.25V70.32H32.44V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(30.97559443547621,86.68667220586838)scale(0.1879707530149084)rotate(90 0.0234375 -4.796875)\">q1_i1 UpdateHotspotSingle</text></g><g class=\"point\"><path d=\"M33.17,101.25V75.91H36.09V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(34.590545454545456,89.36145454545454)scale(0.16290909090909092)rotate(90 0.2421875 -4.796875)\">q1_A UpdateShortAnswer</text></g><g class=\"point\"><path d=\"M36.81,101.25V76.15H39.73V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(38.26190844616376,89.52803567590801)scale(0.17261981517300662)rotate(90 0.046875 -4.796875)\">q1_i1 UpdateComboBox</text></g><g class=\"point\"><path d=\"M40.46,101.25V76.62H43.38V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(41.92,89.70172176029203)scale(0.15983776110322448)rotate(90 0 -4.796875)\">q1_A UpdateNumberField</text></g><g class=\"point\"><path d=\"M44.1,101.25V76.83H47.02V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(45.56,89.81624145785877)scale(0.1618223234624146)rotate(90 0 -4.796875)\">q5_E UpdateRadioButton</text></g><g class=\"point\"><path d=\"M47.75,101.25V77.21H50.67V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(49.20078725500931,90.0380893463265)scale(0.1684616226869594)rotate(90 0.0546875 -4.796875)\">q1_B UpdateComboBox</text></g><g class=\"point\"><path d=\"M51.39,101.25V77.58H54.31V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(52.85,90.15124012158054)scale(0.15348328267477204)rotate(90 0 -4.796875)\">q4_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M55.04,101.25V78.19H57.96V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(56.5,90.44505325686194)scale(0.15115116755428104)rotate(90 0 -4.796875)\">q4_D UpdateRadioButton</text></g><g class=\"point\"><path d=\"M58.68,101.25V80.38H61.6V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(60.13315138919274,91.51584117260992)scale(0.14610369722161456)rotate(90 0.046875 -4.796875)\">q3_C UpdateComboBox</text></g><g class=\"point\"><path d=\"M62.33,101.25V81.22H65.25V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(63.79,91.87460994383191)scale(0.13333888079883505)rotate(90 0 -4.796875)\">q6_F UpdateRadioButton</text></g><g class=\"point\"><path d=\"M65.97,101.25V81.82H68.89V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(67.42361695137977,92.18819864213754)scale(0.13617170389837938)rotate(90 0.046875 -4.796875)\">q2_A UpdateComboBox</text></g><g class=\"point\"><path d=\"M69.62,101.25V82.5H72.54V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(71.07732931345552,92.42160051277182)scale(0.11394929256480867)rotate(90 0.0234375 -4.796875)\">q3_i1 UpdateHotspotSingle</text></g><g class=\"point\"><path d=\"M73.26,101.25V83.32H76.18V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(74.71678846498298,92.77797062511195)scale(0.10276912054451016)rotate(90 0.03125 -4.796875)\">q1_i1 UpdateHotspotMultiple</text></g><g class=\"point\"><path d=\"M76.91,101.25V83.34H79.83V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(78.36412273025597,92.89644060380661)scale(0.12538175453948805)rotate(90 0.046875 -4.796875)\">q1_C UpdateComboBox</text></g><g class=\"point\"><path d=\"M80.55,101.25V84.89H83.47V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(82.00999999999999,93.584180999181)scale(0.10719082719082719)rotate(90 0 -4.796875)\">q7_G UpdateRadioButton</text></g><g class=\"point\"><path d=\"M84.2,101.25V86.59H87.12V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(85.66,94.38417285478548)scale(0.09676567656765675)rotate(90 0 -4.796875)\">q2_A UpdateRadioButton</text></g><g class=\"point\"><path d=\"M87.84,101.25V86.6H90.76V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(89.29791330357992,94.35207720064571)scale(0.0890323805906372)rotate(90 0.0234375 -4.796875)\">q2_i1 UpdateHotspotSingle</text></g><g class=\"point\"><path d=\"M91.49,101.25V87.18H94.41V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(92.947995916817,94.62516902478397)scale(0.08550754914063238)rotate(90 0.0234375 -4.796875)\">q4_i1 UpdateHotspotSingle</text></g><g class=\"point\"><path d=\"M95.13,101.25V87.44H98.05V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(96.59,94.77481244931062)scale(0.08960259529602596)rotate(90 0 -4.796875)\">q2_B UpdateNumberField</text></g><g class=\"point\"><path d=\"M98.78,101.25V87.44H101.7V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(100.24000000000001,94.7745511651469)scale(0.08954812563323203)rotate(90 0 -4.796875)\">q6_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M102.42,101.25V87.73H105.34V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(103.87564152159896,94.93601762303891)scale(0.09298087255534061)rotate(90 0.046875 -4.796875)\">q1_i2 UpdateComboBox</text></g><g class=\"point\"><path d=\"M106.07,101.25V87.73H108.99V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(107.52564152159897,94.93601762303891)scale(0.09298087255534061)rotate(90 0.046875 -4.796875)\">q1_i3 UpdateComboBox</text></g><g class=\"point\"><path d=\"M109.71,101.25V87.81H112.63V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(111.16635377102548,94.90313076505697)scale(0.07778621812262614)rotate(90 0.046875 -4.796875)\">q1_input1 UpdateComboBox</text></g><g class=\"point\"><path d=\"M113.36,101.25V88.22H116.28V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(114.81572413038722,95.1725639903741)scale(0.09121855173922556)rotate(90 0.046875 -4.796875)\">q2_C UpdateComboBox</text></g><g class=\"point\"><path d=\"M117,101.25V89.15H119.92V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(118.46000000000001,95.57636271529888)scale(0.07845997973657544)rotate(90 0 -4.796875)\">q5_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M120.65,101.25V90.28H123.57V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(122.10646357188911,96.12689447668171)scale(0.07544379969911884)rotate(90 0.046875 -4.796875)\">q2_i2 UpdateComboBox</text></g><g class=\"point\"><path d=\"M124.29,101.25V90.4H127.21V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(125.74845456271959,96.14129949672396)scale(0.06593865729750258)rotate(90 0.0234375 -4.796875)\">q2_i2 UpdateHotspotSingle</text></g><g class=\"point\"><path d=\"M127.94,101.25V90.95H130.86V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(129.4,96.42612417491749)scale(0.06798679867986797)rotate(90 0 -4.796875)\">q3_A UpdateRadioButton</text></g><g class=\"point\"><path d=\"M131.58,101.25V91.22H134.5V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(133.0372788931091,96.51345993850606)scale(0.05805028034002533)rotate(90 0.046875 -4.796875)\">q2_input2 UpdateComboBox</text></g><g class=\"point\"><path d=\"M135.23,101.25V92.04H138.15V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(136.69,96.93147112462006)scale(0.059720364741641295)rotate(90 0 -4.796875)\">q7_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M138.87,101.25V92.16H141.79V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(140.32999999999998,96.98306197688322)scale(0.05796731765643685)rotate(90 0 -4.796875)\">q2_i1 UpdateNumberField</text></g><g class=\"point\"><path d=\"M142.52,101.25V92.29H145.44V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(143.98000000000002,97.05369636963697)scale(0.0591419141914191)rotate(90 0 -4.796875)\">q4_A UpdateRadioButton</text></g><g class=\"point\"><path d=\"M146.16,101.25V92.51H149.08V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(147.6171287779238,97.17382172579939)scale(0.06125273762593076)rotate(90 0.046875 -4.796875)\">q3_A UpdateComboBox</text></g><g class=\"point\"><path d=\"M149.81,101.25V92.57H152.73V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(151.26999999999998,97.18330871794872)scale(0.0569764102564103)rotate(90 0 -4.796875)\">q8_H UpdateRadioButton</text></g><g class=\"point\"><path d=\"M153.45,101.25V92.7H156.37V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(154.9087821669357,97.22424983382395)scale(0.051960877409552735)rotate(90 0.0234375 -4.796875)\">q3_i3 UpdateHotspotSingle</text></g><g class=\"point\"><path d=\"M157.1,101.25V93.08H160.02V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(158.55736621534493,97.43452396303461)scale(0.05618740597464003)rotate(90 0.046875 -4.796875)\">q1_i6 UpdateComboBox</text></g><g class=\"point\"><path d=\"M160.74,101.25V93.08H163.66V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(162.1973662153449,97.43452396303461)scale(0.05618740597464003)rotate(90 0.046875 -4.796875)\">q1_i4 UpdateComboBox</text></g><g class=\"point\"><path d=\"M164.39,101.25V93.08H167.31V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(165.84736621534492,97.43452396303461)scale(0.05618740597464003)rotate(90 0.046875 -4.796875)\">q1_i5 UpdateComboBox</text></g><g class=\"point\"><path d=\"M168.03,101.25V93.11H170.95V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(169.49,97.43318946301925)scale(0.052782168186423506)rotate(90 0 -4.796875)\">q8_i1 UpdateRadioButton</text></g><g class=\"point\"><path d=\"M171.68,101.25V93.5H174.6V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(173.13747227658186,97.63367036312242)scale(0.053924766253533374)rotate(90 0.046875 -4.796875)\">q2_D UpdateComboBox</text></g><g class=\"point\"><path d=\"M175.32,101.25V93.63H178.24V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(176.78,97.67309485850936)scale(0.04859306496612199)rotate(90 0 -4.796875)\">q3_i1 UpdateNumberField</text></g><g class=\"point\"><path d=\"M178.97,101.25V94.02H181.89V101.25Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/><text class=\"bartext bartext-inside\" text-anchor=\"middle\" data-notex=\"1\" x=\"0\" y=\"0\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(68, 68, 68); fill-opacity: 1; white-space: pre;\" transform=\"translate(180.42722927844082,97.87803186247673)scale(0.05066462279645245)rotate(90 0.0546875 -4.796875)\">q3_B UpdateComboBox</text></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"x4tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(304.57,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"x4tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(377.47,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20</text></g><g class=\"x4tick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(450.37,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">40</text></g></g><g class=\"yaxislayer-above\"><g class=\"y4tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"y4tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,323.65)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5k</text></g><g class=\"y4tick\"><text text-anchor=\"end\" x=\"301.75\" y=\"4.199999999999999\" transform=\"translate(0,277.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-b12f0b\"><g class=\"clips\"/><clipPath id=\"legendb12f0b\"><rect width=\"195\" height=\"86\" x=\"0\" y=\"0\"/></clipPath></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"legend\" pointer-events=\"all\" transform=\"translate(493.1,100)\"><rect class=\"bg\" shape-rendering=\"crispEdges\" width=\"195\" height=\"86\" x=\"0\" y=\"0\" style=\"stroke: rgb(68, 68, 68); stroke-opacity: 1; fill: rgb(255, 255, 255); fill-opacity: 1; stroke-width: 0px;\"/><g class=\"scrollbox\" transform=\"\" clip-path=\"url('#legendb12f0b')\"><g class=\"groups\"><g class=\"traces\" transform=\"translate(0,14.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: KC (F2011)</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"189.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,33.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: Problem Hierarchy</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(239, 85, 59); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"189.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,52.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: Problem Name</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(0, 204, 150); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"189.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g><g class=\"traces\" transform=\"translate(0,71.5)\" style=\"opacity: 1;\"><text class=\"legendtext\" text-anchor=\"start\" x=\"40\" y=\"4.680000000000001\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Type: Step Name</text><g class=\"layers\" style=\"opacity: 1;\"><g class=\"legendfill\"/><g class=\"legendlines\"/><g class=\"legendsymbols\"><g class=\"legendpoints\"><path class=\"legendundefined\" d=\"M6,6H-6V-6H6Z\" transform=\"translate(20,0)\" style=\"stroke-width: 0.5px; fill: rgb(171, 99, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g><rect class=\"legendtoggle\" x=\"0\" y=\"-9.5\" width=\"189.515625\" height=\"19\" style=\"fill: rgb(0, 0, 0); fill-opacity: 0;\"/></g></g></g><rect class=\"scrollbar\" rx=\"20\" ry=\"3\" width=\"0\" height=\"0\" x=\"0\" y=\"0\" style=\"fill: rgb(128, 139, 164); fill-opacity: 1;\"/></g><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Bar of top 50 distributions for each type</text></g><g class=\"g-xtitle\"/><g class=\"g-x2title\"/><g class=\"g-x3title\"/><g class=\"g-x4title\"/><g class=\"g-ytitle\"/><g class=\"g-y2title\"/><g class=\"g-y3title\"/><g class=\"g-y4title\"/><g class=\"annotation\" data-index=\"0\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,171.125,88.5)\"><g class=\"cursor-pointer\" transform=\"translate(123,77)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"95\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"48.15625\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">KC (F2011)</text></g></g></g><g class=\"annotation\" data-index=\"1\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,393.875,88.5)\"><g class=\"cursor-pointer\" transform=\"translate(318,77)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"151\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"75.984375\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Problem Hierarchy</text></g></g></g><g class=\"annotation\" data-index=\"2\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,171.125,257.25)\"><g class=\"cursor-pointer\" transform=\"translate(110,246)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"121\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"60.96875\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Problem Name</text></g></g></g><g class=\"annotation\" data-index=\"3\" style=\"opacity: 1;\"><g class=\"annotation-text-g\" transform=\"rotate(0,393.875,257.25)\"><g class=\"cursor-pointer\" transform=\"translate(347,246)\"><rect class=\"bg\" x=\"0.5\" y=\"0.5\" width=\"92\" height=\"22\" style=\"stroke-width: 1px; stroke: rgb(0, 0, 0); stroke-opacity: 0; fill: rgb(0, 0, 0); fill-opacity: 0;\"/><text class=\"annotation-text\" text-anchor=\"middle\" x=\"46.53125\" y=\"18\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 16px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">Step Name</text></g></g></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# four column labels are individually distributed as follows \n", "\n", "topnum_max = 50 # show top 50 for each type\n", "fig = make_subplots(rows=2, cols=2,   # 2*2\n", "      start_cell=\"top-left\",  \n", "      subplot_titles=('<PERSON> (F2011)','Problem Hierarchy','Problem Name','Step Name'),   \n", "      column_widths=[0.5, 0.5]) \n", "traces = [\n", "    go.Bar(\n", "        x = df_step[colname].value_counts().reset_index().index.tolist()[:topnum_max],\n", "        y = df_step[colname].value_counts().reset_index()[colname].tolist()[:topnum_max],\n", "        name = 'Type: ' + str(colname),\n", "        text = df_step[colname].value_counts().reset_index()['index'].tolist()[:topnum_max], \n", "        textposition = 'auto',\n", "    ) for colname in ['KC (F2011)','Problem Hierarchy','Problem Name','Step Name']\n", "]\n", "for i in range(len(traces)):\n", "    fig.append_trace(\n", "        traces[i],\n", "        (i //2) + 1, # pos_row\n", "        (i % 2) + 1  # pos_col\n", "    )\n", "    \n", "fig.update_layout(\n", "    title_text = 'Bar of top 50 distributions for each type ',\n", ")\n", "\n", "fig.show(\"svg\")\n"]}], "metadata": {"celltoolbar": "原始单元格格式", "kernelspec": {"display_name": "Data", "language": "python", "name": "data"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 4}