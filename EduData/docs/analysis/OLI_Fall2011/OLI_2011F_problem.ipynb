{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#  OLI data in fall, 2011（problem）"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["%matplotlib inline\n", "import pandas as pd\n", "import numpy as np\n", "# global configuration: show every rows and cols\n", "pd.set_option('display.max_rows', None)\n", "pd.set_option('max_colwidth',None)\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 1. Data Description\n", "## 1.1 Column Description"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Field</th>\n", "      <th>Annotation</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>A row counter.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON>ple</td>\n", "      <td>The sample that includes this problem. If you select more than one sample to export,                    problems that occur in more than one sample will be duplicated in the export.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Anon Student ID</td>\n", "      <td>The student that worked on the problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Problem Hierarchy</td>\n", "      <td>The location in the curriculum hierarchy where this problem occurs.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Problem Name</td>\n", "      <td>The name of the problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Problem View</td>\n", "      <td>The number of times the student encountered the problem so far. This counter                        increases with each instance of the same problem. See \"Problem View\" in the \"By                        Student-Step\" table above.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Problem Start Time</td>\n", "      <td>If the problem start time is not given in the original log data, then it is set                    to the time of the last transaction of the prior problem. If there is no prior problem                    for the session, the time of the earliest transaction is used. Earliest transaction time                    is equivalent to the minimum transaction time for the earliest step of the problem.                    For more detail on how problem start time is determined, see Determining                    Problem Start Time.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Problem End Time</td>\n", "      <td>Derived from the maximum transaction time of the latest step of the problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Latency (sec)</td>\n", "      <td>The amount of time the student spent on this problem. Specifically, the                        difference between the problem start time and the last transaction on this                        problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Steps Missing Start Times</td>\n", "      <td>The number of steps (from the student-step table) with \"Step Start Time\"                        values of \"null\".</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Hints</td>\n", "      <td>Total number of hints the student requested for this problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Incorrects</td>\n", "      <td>Total number of incorrect attempts the student made on this problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Corrects</td>\n", "      <td>Total number of correct attempts the student made for this problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Avg Corrects</td>\n", "      <td>The total number of correct attempts / total number of steps in the problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Steps</td>\n", "      <td>Total number of steps the student took while working on the problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Avg Assistance Score</td>\n", "      <td>Calculated as (total hints requested + total incorrect attempts) / total steps.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Correct First Attempts</td>\n", "      <td>Total number of correct first attempts made by the student for this problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Condition</td>\n", "      <td>The name and type of the condition the student is assigned to. In the case of a student                    assigned to multiple conditions (factors in a factorial design), condition names are                    separated by a comma and space. This differs from the transaction format, which optionally                    has \"Condition Name\" and \"Condition Type\" columns.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>KCs</td>\n", "      <td>Total number of KCs practiced by the student for this problem.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Steps without <PERSON>s</td>\n", "      <td>Total number of steps in this problem (performed by the student) without an assigned KC.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>KC List</td>\n", "      <td>Comma-delimited list of KCs practiced by the student for this problem.</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Field  \\\n", "0                         Row   \n", "1                      Sample   \n", "2             Anon Student ID   \n", "3           Problem Hierarchy   \n", "4                Problem Name   \n", "5                Problem View   \n", "6          Problem Start Time   \n", "7            Problem End Time   \n", "8               Latency (sec)   \n", "9   Steps Missing Start Times   \n", "10                      Hints   \n", "11                 Incorrects   \n", "12                   Corrects   \n", "13               Avg Corrects   \n", "14                      Steps   \n", "15       Avg Assistance Score   \n", "16     Correct First Attempts   \n", "17                  Condition   \n", "18                        KCs   \n", "19          Steps without <PERSON>s   \n", "20                    KC List   \n", "\n", "                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         Annotation  \n", "0                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    A row counter.  \n", "1                                                                                                                                                                                                                                                                                                                                         The sample that includes this problem. If you select more than one sample to export,                    problems that occur in more than one sample will be duplicated in the export.                      \n", "2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           The student that worked on the problem.  \n", "3                                                                                                                                                                                                                                                                                                                                                                                                                                                                               The location in the curriculum hierarchy where this problem occurs.  \n", "4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          The name of the problem.  \n", "5                                                                                                                                                                                                                                                                                                             The number of times the student encountered the problem so far. This counter                        increases with each instance of the same problem. See \"Problem View\" in the \"By                        Student-Step\" table above.  \n", "6   If the problem start time is not given in the original log data, then it is set                    to the time of the last transaction of the prior problem. If there is no prior problem                    for the session, the time of the earliest transaction is used. Earliest transaction time                    is equivalent to the minimum transaction time for the earliest step of the problem.                    For more detail on how problem start time is determined, see Determining                    Problem Start Time.  \n", "7                                                                                                                                                                                                                                                                                                                                                                                                                                                                      Derived from the maximum transaction time of the latest step of the problem.  \n", "8                                                                                                                                                                                                                                                                                                                                         The amount of time the student spent on this problem. Specifically, the                        difference between the problem start time and the last transaction on this                        problem.  \n", "9                                                                                                                                                                                                                                                                                                                                                                                                                                The number of steps (from the student-step table) with \"Step Start Time\"                        values of \"null\".   \n", "10                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    Total number of hints the student requested for this problem.  \n", "11                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Total number of incorrect attempts the student made on this problem.  \n", "12                                                                                                                                                                                                                                                                                                                                                                                                                                                                              Total number of correct attempts the student made for this problem.  \n", "13                                                                                                                                                                                                                                                                                                                                                                                                                                                                     The total number of correct attempts / total number of steps in the problem.  \n", "14                                                                                                                                                                                                                                                                                                                                                                                                                                                                             Total number of steps the student took while working on the problem.  \n", "15                                                                                                                                                                                                                                                                                                                                                                                                                                              Calculated as (total hints requested + total incorrect attempts) / total steps.                      \n", "16                                                                                                                                                                                                                                                                                                                                                                                                                                                                     Total number of correct first attempts made by the student for this problem.  \n", "17                                                                                                                                                              The name and type of the condition the student is assigned to. In the case of a student                    assigned to multiple conditions (factors in a factorial design), condition names are                    separated by a comma and space. This differs from the transaction format, which optionally                    has \"Condition Name\" and \"Condition Type\" columns.  \n", "18                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   Total number of KCs practiced by the student for this problem.  \n", "19                                                                                                                                                                                                                                                                                                                                                                                                                                                         Total number of steps in this problem (performed by the student) without an assigned KC.  \n", "20                                                                                                                                                                                                                                                                                                                                                                                                                                                                           Comma-delimited list of KCs practiced by the student for this problem.  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# help_table3: the description for data by problems\n", "df3 = pd.read_csv('OLI_data/help_table3.csv',sep=',',encoding=\"gbk\")\n", "df3 = df3.loc[:, ['Field', 'Annotation']]\n", "df3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.2 Summarization of Data\n", "\n", "**This table organizes the data as student-problem**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON> Student Id</th>\n", "      <th>Problem Hierarchy</th>\n", "      <th>Problem Name</th>\n", "      <th>Problem View</th>\n", "      <th>Problem Start Time</th>\n", "      <th>Problem End Time</th>\n", "      <th>Latency (sec)</th>\n", "      <th>Steps Missing Start Times</th>\n", "      <th>Hints</th>\n", "      <th>Incorrects</th>\n", "      <th>Corrects</th>\n", "      <th>Avg Corrects</th>\n", "      <th>Steps</th>\n", "      <th>Avg Assistance Score</th>\n", "      <th>Correct First Attempts</th>\n", "      <th>Condition</th>\n", "      <th>KCs (F2011)</th>\n", "      <th>Steps without <PERSON>s (F2011)</th>\n", "      <th>KC List (F2011)</th>\n", "      <th><PERSON><PERSON> (Single-KC)</th>\n", "      <th>Steps without <PERSON><PERSON> (Single-KC)</th>\n", "      <th><PERSON> (Single-KC)</th>\n", "      <th><PERSON>s (Unique-step)</th>\n", "      <th>Steps without <PERSON>s (Unique-step)</th>\n", "      <th><PERSON> List (Unique-step)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>All Data</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "      <td>0.571</td>\n", "      <td>21</td>\n", "      <td>0.429</td>\n", "      <td>12</td>\n", "      <td>NaN</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>gravitational_forces, identify_interaction, represent_interaction_cord, represent_interaction_spring, simple_step</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Single-KC</td>\n", "      <td>0</td>\n", "      <td>21</td>\n", "      <td>.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>All Data</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Force</td>\n", "      <td>tutor_03_01</td>\n", "      <td>1</td>\n", "      <td>2011/9/21 17:49</td>\n", "      <td>2011/9/21 17:49</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1.000</td>\n", "      <td>3</td>\n", "      <td>0.000</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>distinguish_rotation_translation</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>Single-KC</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td><PERSON>523, <PERSON>680, KC768</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Row    Sample                       Anon Student Id  \\\n", "0    1  All Data  Stu_00b2b35fd027e7891e8a1a527125dd65   \n", "1    2  All Data  Stu_00b2b35fd027e7891e8a1a527125dd65   \n", "\n", "                                                                                         Problem Hierarchy  \\\n", "0  sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams   \n", "1                    sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Force   \n", "\n", "  Problem Name  Problem View Problem Start Time Problem End Time  \\\n", "0   _m2_assess             1    2011/9/21 17:35  2011/9/21 17:35   \n", "1  tutor_03_01             1    2011/9/21 17:49  2011/9/21 17:49   \n", "\n", "   Latency (sec)  Steps Missing Start Times  Hints  Incorrects  Corrects  \\\n", "0              0                          0      0           9        12   \n", "1              9                          0      0           0         3   \n", "\n", "   Avg Corrects  Steps  Avg Assistance Score  Correct First Attempts  \\\n", "0         0.571     21                 0.429                      12   \n", "1         1.000      3                 0.000                       3   \n", "\n", "   Condition  KCs (F2011)  Steps without KCs (F2011)  \\\n", "0        NaN            5                          0   \n", "1        NaN            1                          0   \n", "\n", "                                                                                                     KC List (F2011)  \\\n", "0  gravitational_forces, identify_interaction, represent_interaction_cord, represent_interaction_spring, simple_step   \n", "1                                                                                   distinguish_rotation_translation   \n", "\n", "   KCs (Single-KC)  Steps without <PERSON><PERSON> (Single-KC) KC List (Single-KC)  \\\n", "0                1                              0           Single-KC   \n", "1                1                              0           Single-KC   \n", "\n", "   KCs (Unique-step)  Steps without KC<PERSON> (Unique-step) KC List (Unique-step)  \n", "0                  0                               21                     .  \n", "1                  3                                0   <PERSON><PERSON><PERSON>, <PERSON>680, <PERSON>768  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df_problem =  pd.read_csv('OLI_data/AllData_problem_2011F.csv',low_memory=False) # sep=\"\\t\"\n", "df_problem.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 2. Data Analysis"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th>Problem View</th>\n", "      <th>Latency (sec)</th>\n", "      <th>Steps Missing Start Times</th>\n", "      <th>Hints</th>\n", "      <th>Incorrects</th>\n", "      <th>Corrects</th>\n", "      <th>Avg Corrects</th>\n", "      <th>Steps</th>\n", "      <th>Avg Assistance Score</th>\n", "      <th>Correct First Attempts</th>\n", "      <th>Condition</th>\n", "      <th>KCs (F2011)</th>\n", "      <th>Steps without <PERSON>s (F2011)</th>\n", "      <th><PERSON><PERSON> (Single-KC)</th>\n", "      <th>Steps without <PERSON><PERSON> (Single-KC)</th>\n", "      <th><PERSON>s (Unique-step)</th>\n", "      <th>Steps without <PERSON>s (Unique-step)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>0.0</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.0</td>\n", "      <td>45002.0</td>\n", "      <td>45002.000000</td>\n", "      <td>45002.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>22501.500000</td>\n", "      <td>1.221146</td>\n", "      <td>85.639883</td>\n", "      <td>0.007000</td>\n", "      <td>0.620217</td>\n", "      <td>1.644460</td>\n", "      <td>4.176325</td>\n", "      <td>0.959571</td>\n", "      <td>4.331963</td>\n", "      <td>0.928014</td>\n", "      <td>3.219479</td>\n", "      <td>NaN</td>\n", "      <td>1.223923</td>\n", "      <td>1.798920</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>4.289654</td>\n", "      <td>0.042309</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>12991.102744</td>\n", "      <td>1.140622</td>\n", "      <td>301.895374</td>\n", "      <td>0.106748</td>\n", "      <td>1.956302</td>\n", "      <td>3.378211</td>\n", "      <td>5.125742</td>\n", "      <td>0.358850</td>\n", "      <td>5.079484</td>\n", "      <td>2.221907</td>\n", "      <td>4.603916</td>\n", "      <td>NaN</td>\n", "      <td>1.733856</td>\n", "      <td>3.830471</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>5.084490</td>\n", "      <td>0.557118</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>11251.250000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>NaN</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>22501.500000</td>\n", "      <td>1.000000</td>\n", "      <td>20.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>3.000000</td>\n", "      <td>1.000000</td>\n", "      <td>3.000000</td>\n", "      <td>0.250000</td>\n", "      <td>2.000000</td>\n", "      <td>NaN</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>3.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>33751.750000</td>\n", "      <td>1.000000</td>\n", "      <td>73.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>5.000000</td>\n", "      <td>1.000000</td>\n", "      <td>5.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>NaN</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>5.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>45002.000000</td>\n", "      <td>32.000000</td>\n", "      <td>20426.000000</td>\n", "      <td>8.000000</td>\n", "      <td>50.000000</td>\n", "      <td>413.000000</td>\n", "      <td>232.000000</td>\n", "      <td>19.333000</td>\n", "      <td>32.000000</td>\n", "      <td>210.500000</td>\n", "      <td>32.000000</td>\n", "      <td>NaN</td>\n", "      <td>9.000000</td>\n", "      <td>32.000000</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "      <td>32.000000</td>\n", "      <td>29.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                Row  Problem View  Latency (sec)  Steps Missing Start Times  \\\n", "count  45002.000000  45002.000000   45002.000000               45002.000000   \n", "mean   22501.500000      1.221146      85.639883                   0.007000   \n", "std    12991.102744      1.140622     301.895374                   0.106748   \n", "min        1.000000      1.000000       0.000000                   0.000000   \n", "25%    11251.250000      1.000000       0.000000                   0.000000   \n", "50%    22501.500000      1.000000      20.000000                   0.000000   \n", "75%    33751.750000      1.000000      73.000000                   0.000000   \n", "max    45002.000000     32.000000   20426.000000                   8.000000   \n", "\n", "              Hints    Incorrects      Corrects  Avg Corrects         Steps  \\\n", "count  45002.000000  45002.000000  45002.000000  45002.000000  45002.000000   \n", "mean       0.620217      1.644460      4.176325      0.959571      4.331963   \n", "std        1.956302      3.378211      5.125742      0.358850      5.079484   \n", "min        0.000000      0.000000      0.000000      0.000000      1.000000   \n", "25%        0.000000      0.000000      1.000000      1.000000      1.000000   \n", "50%        0.000000      1.000000      3.000000      1.000000      3.000000   \n", "75%        0.000000      2.000000      5.000000      1.000000      5.000000   \n", "max       50.000000    413.000000    232.000000     19.333000     32.000000   \n", "\n", "       Avg Assistance Score  Correct First Attempts  Condition   KCs (F2011)  \\\n", "count          45002.000000            45002.000000        0.0  45002.000000   \n", "mean               0.928014                3.219479        NaN      1.223923   \n", "std                2.221907                4.603916        NaN      1.733856   \n", "min                0.000000                0.000000        NaN      0.000000   \n", "25%                0.000000                1.000000        NaN      0.000000   \n", "50%                0.250000                2.000000        NaN      1.000000   \n", "75%                1.000000                4.000000        NaN      2.000000   \n", "max              210.500000               32.000000        NaN      9.000000   \n", "\n", "       Steps without KCs (F2011)  KCs (Single-KC)  \\\n", "count               45002.000000          45002.0   \n", "mean                    1.798920              1.0   \n", "std                     3.830471              0.0   \n", "min                     0.000000              1.0   \n", "25%                     0.000000              1.0   \n", "50%                     0.000000              1.0   \n", "75%                     2.000000              1.0   \n", "max                    32.000000              1.0   \n", "\n", "       Steps without <PERSON>s (Single-KC)  KCs (Unique-step)  \\\n", "count                        45002.0       45002.000000   \n", "mean                             0.0           4.289654   \n", "std                              0.0           5.084490   \n", "min                              0.0           0.000000   \n", "25%                              0.0           1.000000   \n", "50%                              0.0           3.000000   \n", "75%                              0.0           5.000000   \n", "max                              0.0          32.000000   \n", "\n", "       Steps without <PERSON>s (Unique-step)  \n", "count                     45002.000000  \n", "mean                          0.042309  \n", "std                           0.557118  \n", "min                           0.000000  \n", "25%                           0.000000  \n", "50%                           0.000000  \n", "75%                           0.000000  \n", "max                          29.000000  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_problem.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （1）Analysis for Null and Unique value of column attributes"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------num_unique_toal and num_nonull_toal----------------------\n", "<class 'pandas.core.series.Series'>\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_name</th>\n", "      <th>num_nonull</th>\n", "      <th>num_null</th>\n", "      <th>num_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>45002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON>ple</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON><PERSON> Student Id</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Problem Hierarchy</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Problem Name</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Problem View</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Problem Start Time</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>25983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Problem End Time</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>25884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Latency (sec)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Steps Missing Start Times</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Hints</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Incorrects</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Corrects</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Avg Corrects</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Steps</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Avg Assistance Score</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>Correct First Attempts</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Condition</td>\n", "      <td>0</td>\n", "      <td>45002</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>KCs (F2011)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>Steps without <PERSON>s (F2011)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>KC List (F2011)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td><PERSON><PERSON> (Single-KC)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>Steps without <PERSON><PERSON> (Single-KC)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td><PERSON> List (Single-KC)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>KCs (Unique-step)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>Steps without KCs (Unique-step)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>KC List (Unique-step)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1470</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           col_name  num_nonull  num_null  num_unique\n", "0                               Row       45002         0       45002\n", "1                            Sample       45002         0           1\n", "2                   Anon Student Id       45002         0         333\n", "3                 Problem Hierarchy       45002         0          27\n", "4                      Problem Name       45002         0         300\n", "5                      Problem View       45002         0          32\n", "6                Problem Start Time       45002         0       25983\n", "7                  Problem End Time       45002         0       25884\n", "8                     Latency (sec)       45002         0        1290\n", "9         Steps Missing Start Times       45002         0           8\n", "10                            Hints       45002         0          35\n", "11                       Incorrects       45002         0          37\n", "12                         Corrects       45002         0          51\n", "13                     Avg Corrects       45002         0         195\n", "14                            Steps       45002         0          31\n", "15             Avg Assistance Score       45002         0         335\n", "16           Correct First Attempts       45002         0          33\n", "17                        Condition           0     45002           1\n", "18                      KCs (F2011)       45002         0          10\n", "19        Steps without <PERSON><PERSON> (F2011)       45002         0          31\n", "20                  KC List (F2011)       45002         0         170\n", "21                  KCs (Single-KC)       45002         0           1\n", "22    Steps without <PERSON>s (Single-KC)       45002         0           1\n", "23              KC List (Single-KC)       45002         0           1\n", "24                KCs (Unique-step)       45002         0          32\n", "25  Steps without KCs (Unique-step)       45002         0          16\n", "26            KC List (Unique-step)       45002         0        1470"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["def work_col_analysis(df_work):\n", "    num_nonull_toal = df_work.notnull().sum()  # Not Null\n", "    dict_col_1 = {'col_name':num_nonull_toal.index,'num_nonull':num_nonull_toal.values}\n", "    df_work_col_1 = pd.DataFrame(dict_col_1)\n", "\n", "    num_null_toal = df_work.isnull().sum()  # Null\n", "    dict_col_2 = {'col_name':num_null_toal.index,'num_null':num_null_toal.values}\n", "    df_work_col_2 = pd.DataFrame(dict_col_2)\n", "\n", "    num_unique_toal = df_work.apply(lambda col: len(col.unique()))   # axis=0\n", "    print(type(num_unique_toal))\n", "    dict_col_3 = {'col_name':num_unique_toal.index,'num_unique':num_unique_toal.values}\n", "    df_work_col_3 = pd.DataFrame(dict_col_3)\n", "\n", "    df_work_col = pd.merge(df_work_col_1, df_work_col_2, on=['col_name'])\n", "    df_work_col = pd.merge(df_work_col, df_work_col_3, on=['col_name'])\n", "    return df_work_col\n", "print(\"-------------------num_unique_toal and num_nonull_toal----------------------\")\n", "df_result = work_col_analysis(df_problem)\n", "df_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （2）Analysis for Discrete value of column attributes\n", "> Columns with a small number of discrete values may represent very informative, so identify these columns first and analyze them one by one"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Steps Missing Start Times  :  [0, 1, 2, 5, 7, 6, 3, 8]\n", "--------------------------------------------------------------------------------\n", "KCs (F2011)  :  [5, 1, 4, 2, 3, 9, 0, 8, 6, 7]\n", "--------------------------------------------------------------------------------\n", "Steps without KCs (Unique-step)  :  [21, 0, 17, 15, 9, 2, 5, 1, 4, 3, 12, 10, 8, 11, 14, 29]\n", "--------------------------------------------------------------------------------\n"]}], "source": ["discrete_cols = []\n", "series = []\n", "cols = list(df_problem.columns.values)\n", "\n", "for col in cols:\n", "    if len(df_problem[col].unique().tolist()) <= 20 and len(df_problem[col].unique().tolist()) >= 2:\n", "        discrete_cols.append(col)\n", "        series.append(df_problem[col].unique().tolist())\n", "\n", "for a,b in zip(discrete_cols,series):\n", "    print(a,\" : \",b)\n", "    print(\"-\"*80)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （3）Data Cleaning \n", "> **Data Cleaning Suggestions**\n", "> - Redundant columns: Columns that are all NULL or Single value.\n", "> - Others"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["df_problem_clear = df_problem.copy(deep=True) # deep copy"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the cols num before clear:  27\n", "the cols num after clear: 22\n", "drop:--- <PERSON><PERSON>\n", "drop:--- Condition\n", "drop:--- <PERSON><PERSON> (Single-KC)\n", "drop:--- Steps without <PERSON><PERSON> (Single-KC)\n", "drop:--- <PERSON> (Single-KC)\n"]}], "source": ["# Clear all redundant columns directly.\n", "cols = list(df_problem.columns.values)\n", "drop_cols = []\n", "for col in cols:\n", "    if len(df_problem_clear[col].unique().tolist()) == 1:\n", "        df_problem_clear.drop(col,axis =1,inplace=True)\n", "        drop_cols.append(col)\n", "\n", "print(\"the cols num before clear: \",len(df_problem.columns.to_list()))\n", "print(\"the cols num after clear:\",len(df_problem_clear.columns.to_list()))\n", "for col in drop_cols:\n", "    print(\"drop:---\",col)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Row</th>\n", "      <th><PERSON><PERSON> Student Id</th>\n", "      <th>Problem Hierarchy</th>\n", "      <th>Problem Name</th>\n", "      <th>Problem View</th>\n", "      <th>Problem Start Time</th>\n", "      <th>Problem End Time</th>\n", "      <th>Latency (sec)</th>\n", "      <th>Steps Missing Start Times</th>\n", "      <th>Hints</th>\n", "      <th>Incorrects</th>\n", "      <th>Corrects</th>\n", "      <th>Avg Corrects</th>\n", "      <th>Steps</th>\n", "      <th>Avg Assistance Score</th>\n", "      <th>Correct First Attempts</th>\n", "      <th>KCs (F2011)</th>\n", "      <th>Steps without <PERSON>s (F2011)</th>\n", "      <th>KC List (F2011)</th>\n", "      <th><PERSON>s (Unique-step)</th>\n", "      <th>Steps without <PERSON>s (Unique-step)</th>\n", "      <th><PERSON> List (Unique-step)</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams</td>\n", "      <td>_m2_assess</td>\n", "      <td>1</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>2011/9/21 17:35</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>9</td>\n", "      <td>12</td>\n", "      <td>0.571</td>\n", "      <td>21</td>\n", "      <td>0.429</td>\n", "      <td>12</td>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>gravitational_forces, identify_interaction, represent_interaction_cord, represent_interaction_spring, simple_step</td>\n", "      <td>0</td>\n", "      <td>21</td>\n", "      <td>.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>Stu_00b2b35fd027e7891e8a1a527125dd65</td>\n", "      <td>sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Force</td>\n", "      <td>tutor_03_01</td>\n", "      <td>1</td>\n", "      <td>2011/9/21 17:49</td>\n", "      <td>2011/9/21 17:49</td>\n", "      <td>9</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>1.000</td>\n", "      <td>3</td>\n", "      <td>0.000</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>distinguish_rotation_translation</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td><PERSON>523, <PERSON>680, KC768</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Row                       Anon Student Id  \\\n", "0    1  Stu_00b2b35fd027e7891e8a1a527125dd65   \n", "1    2  Stu_00b2b35fd027e7891e8a1a527125dd65   \n", "\n", "                                                                                         Problem Hierarchy  \\\n", "0  sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams   \n", "1                    sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Force   \n", "\n", "  Problem Name  Problem View Problem Start Time Problem End Time  \\\n", "0   _m2_assess             1    2011/9/21 17:35  2011/9/21 17:35   \n", "1  tutor_03_01             1    2011/9/21 17:49  2011/9/21 17:49   \n", "\n", "   Latency (sec)  Steps Missing Start Times  Hints  Incorrects  Corrects  \\\n", "0              0                          0      0           9        12   \n", "1              9                          0      0           0         3   \n", "\n", "   Avg Corrects  Steps  Avg Assistance Score  Correct First Attempts  \\\n", "0         0.571     21                 0.429                      12   \n", "1         1.000      3                 0.000                       3   \n", "\n", "   KCs (F2011)  Steps without KCs (F2011)  \\\n", "0            5                          0   \n", "1            1                          0   \n", "\n", "                                                                                                     KC List (F2011)  \\\n", "0  gravitational_forces, identify_interaction, represent_interaction_cord, represent_interaction_spring, simple_step   \n", "1                                                                                   distinguish_rotation_translation   \n", "\n", "   KCs (Unique-step)  Steps without KC<PERSON> (Unique-step) KC List (Unique-step)  \n", "0                  0                               21                     .  \n", "1                  3                                0   <PERSON><PERSON><PERSON>, <PERSON>680, <PERSON>768  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_problem_clear.head(2)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-------------------num_unique_toal and num_nonull_toal----------------------\n", "<class 'pandas.core.series.Series'>\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>col_name</th>\n", "      <th>num_nonull</th>\n", "      <th>num_null</th>\n", "      <th>num_unique</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Row</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>45002</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON> Student Id</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Problem Hierarchy</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>27</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Problem Name</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>300</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Problem View</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Problem Start Time</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>25983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Problem End Time</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>25884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Latency (sec)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Steps Missing Start Times</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Hints</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>Incorrects</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>37</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>Corrects</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>51</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>Avg Corrects</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>195</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>Steps</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>Avg Assistance Score</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>335</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>Correct First Attempts</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>KCs (F2011)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>Steps without <PERSON>s (F2011)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>31</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>KC List (F2011)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>170</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>KCs (Unique-step)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>Steps without KCs (Unique-step)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>KC List (Unique-step)</td>\n", "      <td>45002</td>\n", "      <td>0</td>\n", "      <td>1470</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                           col_name  num_nonull  num_null  num_unique\n", "0                               Row       45002         0       45002\n", "1                   Anon Student Id       45002         0         333\n", "2                 Problem Hierarchy       45002         0          27\n", "3                      Problem Name       45002         0         300\n", "4                      Problem View       45002         0          32\n", "5                Problem Start Time       45002         0       25983\n", "6                  Problem End Time       45002         0       25884\n", "7                     Latency (sec)       45002         0        1290\n", "8         Steps Missing Start Times       45002         0           8\n", "9                             Hints       45002         0          35\n", "10                       Incorrects       45002         0          37\n", "11                         Corrects       45002         0          51\n", "12                     Avg Corrects       45002         0         195\n", "13                            Steps       45002         0          31\n", "14             Avg Assistance Score       45002         0         335\n", "15           Correct First Attempts       45002         0          33\n", "16                      KCs (F2011)       45002         0          10\n", "17        Steps without <PERSON><PERSON> (F2011)       45002         0          31\n", "18                  KC List (F2011)       45002         0         170\n", "19                KCs (Unique-step)       45002         0          32\n", "20  Steps without KCs (Unique-step)       45002         0          16\n", "21            KC List (Unique-step)       45002         0        1470"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# the remaining columns\n", "print(\"-------------------num_unique_toal and num_nonull_toal----------------------\")\n", "df_result = work_col_analysis(df_problem_clear)\n", "df_result "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 3. Data Visualization"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import plotly.express as px\n", "from plotly.subplots import make_subplots\n", "import plotly.graph_objs as go\n", "import matplotlib.pyplot as plt "]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# The distribution of continuous values\n", "def show_value_counts_histogram(colname, sort = True):\n", "    # create the bins\n", "    start = int(df_problem_clear[colname].min()/10)*10\n", "    end = int(df_problem_clear[colname].quantile(q=0.95)/10+1)*10\n", "    problem = int((end - start)/20)\n", "    print(start, end, problem)\n", "    counts, bins = np.histogram(df_problem_clear[colname],bins=range(start, end, problem))\n", "    bins = 0.5 * (bins[:-1] + bins[1:])\n", "\n", "    fig = px.bar(x=bins, y=counts, labels={'x': colname, 'y':'count'})\n", "    fig.show(\"svg\")\n", "\n", "# Box distribution of continuous values\n", "def show_value_counts_box(colname, sort = True):\n", "    # way1: plotly (too costy for box-plot)\n", "    # fig = px.box(df_problem_clear, y=colname)\n", "    # fig.show(\"svg\")\n", "    # way2: mat<PERSON><PERSON><PERSON>b\n", "    plt.figure(figsize=(10,5))\n", "    plt.title('Box-plot for '+ colname,fontsize=20)#标题，并设定字号大小\n", "    plt.boxplot([df_problem_clear[colname].tolist()])\n", "    plt.show(\"svg\")\n", "\n", "# Histogram of discrete values\n", "def show_value_counts_bar(colname, sort = True):\n", "    ds = df_problem_clear[colname].value_counts().reset_index()\n", "    ds.columns = [\n", "        colname,\n", "        'Count'\n", "    ]\n", "    if sort:\n", "        ds = ds.sort_values(by='Count', ascending=False)\n", "    # histogram\n", "    fig = px.bar(\n", "        ds,\n", "        x = colname,\n", "        y = 'Count',\n", "        title = colname + ' distribution'\n", "    )\n", "    fig.show(\"svg\")\n", "\n", "\n", "# Pie of discrete values\n", "def show_value_counts_pie(colname, sort = True):\n", "    ds = df_problem_clear[colname].value_counts().reset_index()\n", "    ds.columns = [\n", "        colname,\n", "        'percent'\n", "    ]\n", "    ds['percent'] /= len(df_problem_clear)\n", "    if sort:\n", "        ds = ds.sort_values(by='percent', ascending=False)\n", "    fig = px.pie(\n", "        ds,\n", "        names = colname,\n", "        values = 'percent',\n", "        title = colname+ 'Percentage',\n", "    )\n", "    fig.show(\"svg\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##  （1）sort by single attributes"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-b5737a\"><g class=\"clips\"><clipPath id=\"clipb5737axyplot\" class=\"plotclip\"><rect width=\"540\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb5737ax\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb5737ay\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"270\"/></clipPath><clipPath class=\"axesclip\" id=\"clipb5737axy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"270\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"270\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,304.27)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,238.54)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,172.8)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,107.07)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,370)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clipb5737axyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M5.4,270V13.5H48.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M59.4,270V88.92H102.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M113.4,270V198.69H156.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M221.4,270V243.84H264.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M167.4,270V250.57H210.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M275.4,270V253.42H318.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M329.4,270V263.9H372.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M437.4,270V264.37H480.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M491.4,270V264.44H534.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M383.4,270V266.73H426.6V270Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(107,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(215,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(323,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(431,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">6</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(539,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">8</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,304.27)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,238.54)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,172.8)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">15k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,107.07)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-b5737a\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">KCs (F2011) distribution</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">KCs (F2011)</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,31.840625000000003,235)\" x=\"31.840625000000003\" y=\"235\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Count</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-703eda\"><g class=\"clips\"><clipPath id=\"clip703edaxyplot\" class=\"plotclip\"><rect width=\"540\" height=\"64\"/></clipPath><clipPath class=\"axesclip\" id=\"clip703edax\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clip703eday\"><rect x=\"0\" y=\"78\" width=\"700\" height=\"64\"/></clipPath><clipPath class=\"axesclip\" id=\"clip703edaxy\"><rect x=\"80\" y=\"78\" width=\"540\" height=\"64\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"78\" width=\"540\" height=\"64\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,117.15)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,92.3)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,142)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,78)\" clip-path=\"url('#clip703edaxyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M2,64V3.2H18V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M22,64V3.98H38V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M42,64V9.84H58V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M62,64V13.19H78V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M82,64V19.45H98V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M102,64V27.44H118V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M122,64V29.56H138V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M142,64V36.2H158V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M162,64V36.99H178V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M182,64V41.51H198V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M202,64V48.28H218V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M222,64V48.48H238V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M242,64V50.49H258V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M262,64V50.62H278V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M282,64V53.08H298V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M302,64V53.26H318V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M322,64V53.66H338V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M342,64V53.72H358V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M362,64V54.2H378V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M382,64V56.16H398V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M402,64V59.63H418V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M422,64V60.01H438V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M442,64V60.56H458V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M462,64V60.58H478V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M482,64V60.94H498V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M502,64V61.29H518V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M522,64V62.48H538V64Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(90,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Equilibrium of a Single Subsystem</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(110,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Statically Equivalent Loads</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(130,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Friction, module Friction</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(150,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Choosing a Solvable Subsystem</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(170,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Representing Interactions Between Bodies</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(190,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Applications of Static Equivalency to Distributed Forces, section1 Simplifying 3D loadings to 2D or 1D loading</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(210,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Couples</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(230,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Multiple Body Equilibrium - Frames, module Solving Multiple Subsystems</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(250,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Multiple Body Equilibrium - Trusses, module Method of Joints</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(270,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Force</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(290,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces, section1 Applying Force and Moment Equilibrium</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(310,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Pin Connections</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(330,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces, section1 Combining Moments</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(350,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces, section1 Combining Concurrent Forces</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(370,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Applications of Static Equivalency to Distributed Forces, section1 Center of Gravity and Centroid</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(390,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces, section1 Applying Force Equilibrium</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(410,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Multiple Body Equilibrium - Trusses, module Method of Sections</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(430,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Multiple Body Equilibrium - Frames, module Drawing FBDs of Multiple Subsystems</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(450,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Moments of Inertia, module Second Moment of Area</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(470,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Moments of Inertia, module Mass Moment of Inertia</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(490,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(510,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Other Connections</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(530,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Introduction to Free Body Diagrams</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(550,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Equilibrium Under 2D Concentrated Forces</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(570,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Concentrated Forces and Their Effects, module Effects of Multiple Forces</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(590,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Engineering Systems - Single Body Equilibrium, module Drawing FBDs of a Single Subsystem</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"155\" transform=\"translate(610,0) rotate(90,0,149)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">sequence Statics, unit Complex Interactions Between Bodies, module Representing Engineering Connections, section1 Fixed Connections</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,142)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,117.15)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">2000</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,92.3)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">4000</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-703eda\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"39\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Hierarchy distribution</text></g><g class=\"g-xtitle\" transform=\"translate(0,-810.26875)\"><text class=\"xtitle\" x=\"350\" y=\"1257.065625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Hierarchy</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,23.684375000000003,110)\" x=\"23.684375000000003\" y=\"110\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Count</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Bar\n", "show_value_counts_bar('KCs (F2011)')\n", "show_value_counts_bar('Problem Hierarchy')"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 720x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["0 340 17\n"]}, {"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-cf6606\"><g class=\"clips\"><clipPath id=\"clipcf6606xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"310\"/></clipPath><clipPath class=\"axesclip\" id=\"clipcf6606x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clipcf6606y\"><rect x=\"0\" y=\"60\" width=\"700\" height=\"310\"/></clipPath><clipPath class=\"axesclip\" id=\"clipcf6606xy\"><rect x=\"80\" y=\"60\" width=\"540\" height=\"310\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"60\" width=\"540\" height=\"310\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,300.21000000000004)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,230.41)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,160.62)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,90.83)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,370)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,60)\" clip-path=\"url('#clipcf6606xyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M2.84,310V15.5H25.58V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M31.26,310V233.44H54V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M59.68,310V257.36H82.42V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M88.11,310V272.4H110.84V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M116.53,310V282.26H139.26V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M144.95,310V289.05H167.68V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M173.37,310V294.05H196.11V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M201.79,310V296.64H224.53V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M230.21,310V300.06H252.95V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M258.63,310V301.75H281.37V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M287.05,310V302.83H309.79V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M315.47,310V303.9H338.21V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M343.89,310V304.71H366.63V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M372.32,310V306.02H395.05V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M400.74,310V306.01H423.47V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M429.16,310V306.62H451.89V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M457.58,310V306.96H480.32V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M486,310V307.57H508.74V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M514.42,310V307.46H537.16V310Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(80,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(163.59,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">50</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(247.18,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">100</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(330.77,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">150</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(414.37,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">200</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(497.96,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">250</text></g><g class=\"xtick\"><text text-anchor=\"middle\" x=\"0\" y=\"383\" transform=\"translate(581.55,0)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">300</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,370)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,300.21000000000004)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">5k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,230.41)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">10k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,160.62)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">15k</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,90.83)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">20k</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-cf6606\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"/><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"410.20625\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Latency (sec)</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,31.840625000000003,215)\" x=\"31.840625000000003\" y=\"215\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">count</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# analysis for \"duration\" \n", "# It is obvious that there are unreasonable outliers\n", "\n", "show_value_counts_box('Latency (sec)') \n", "show_value_counts_histogram('Latency (sec)')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## （2）group by Problem Name,  sorted by meam(avg corrects) "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg class=\"main-svg\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"700\" height=\"450\" style=\"\" viewBox=\"0 0 700 450\"><rect x=\"0\" y=\"0\" width=\"700\" height=\"450\" style=\"fill: rgb(255, 255, 255); fill-opacity: 1;\"/><defs id=\"defs-44afe5\"><g class=\"clips\"><clipPath id=\"clip44afe5xyplot\" class=\"plotclip\"><rect width=\"540\" height=\"223\"/></clipPath><clipPath class=\"axesclip\" id=\"clip44afe5x\"><rect x=\"80\" y=\"0\" width=\"540\" height=\"450\"/></clipPath><clipPath class=\"axesclip\" id=\"clip44afe5y\"><rect x=\"0\" y=\"100\" width=\"700\" height=\"223\"/></clipPath><clipPath class=\"axesclip\" id=\"clip44afe5xy\"><rect x=\"80\" y=\"100\" width=\"540\" height=\"223\"/></clipPath></g><g class=\"gradients\"/></defs><g class=\"bglayer\"><rect class=\"bg\" x=\"80\" y=\"100\" width=\"540\" height=\"223\" style=\"fill: rgb(229, 236, 246); fill-opacity: 1; stroke-width: 0;\"/></g><g class=\"layer-below\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"cartesianlayer\"><g class=\"subplot xy\"><g class=\"layer-subplot\"><g class=\"shapelayer\"/><g class=\"imagelayer\"/></g><g class=\"gridlayer\"><g class=\"x\"/><g class=\"y\"><path class=\"ygrid crisp\" transform=\"translate(0,250.18)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,177.35)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/><path class=\"ygrid crisp\" transform=\"translate(0,104.53)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 1px;\"/></g></g><g class=\"zerolinelayer\"><path class=\"yzl zl crisp\" transform=\"translate(0,323)\" d=\"M80,0h540\" style=\"stroke: rgb(255, 255, 255); stroke-opacity: 1; stroke-width: 2px;\"/></g><path class=\"xlines-below\"/><path class=\"ylines-below\"/><g class=\"overlines-below\"/><g class=\"xaxislayer-below\"/><g class=\"yaxislayer-below\"/><g class=\"overaxes-below\"/><g class=\"plot\" transform=\"translate(80,100)\" clip-path=\"url('#clip44afe5xyplot')\"><g class=\"barlayer mlayer\"><g class=\"trace bars\" style=\"opacity: 1;\"><g class=\"points\"><g class=\"point\"><path d=\"M0.18,223V11.15H1.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M1.98,223V42.59H3.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M3.78,223V44.42H5.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M5.58,223V47.94H7.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M7.38,223V48.22H8.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M9.18,223V48.71H10.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M10.98,223V50.58H12.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M12.78,223V52.2H14.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M14.58,223V52.56H16.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M16.38,223V53.57H17.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M18.18,223V54.15H19.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M19.98,223V56.1H21.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M21.78,223V57.49H23.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M23.58,223V58.3H25.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M25.38,223V59.88H26.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M27.18,223V61.17H28.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M28.98,223V61.39H30.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M30.78,223V61.75H32.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M32.58,223V61.75H34.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M34.38,223V62.65H35.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M36.18,223V62.7H37.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M37.98,223V63.09H39.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M39.78,223V63.33H41.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M41.58,223V63.61H43.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M43.38,223V63.76H44.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M45.18,223V64.75H46.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M46.98,223V65.11H48.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M48.78,223V65.49H50.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M50.58,223V65.57H52.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M52.38,223V66.41H53.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M54.18,223V66.99H55.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M55.98,223V67.27H57.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M57.78,223V67.46H59.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M59.58,223V67.64H61.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M61.38,223V67.8H62.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M63.18,223V68.08H64.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M64.98,223V68.19H66.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M66.78,223V68.27H68.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M68.58,223V68.4H70.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M70.38,223V68.65H71.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M72.18,223V69.88H73.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M73.98,223V70.12H75.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M75.78,223V70.14H77.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M77.58,223V70.38H79.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M79.38,223V70.39H80.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M81.18,223V70.5H82.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M82.98,223V70.69H84.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M84.78,223V70.94H86.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M86.58,223V70.97H88.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M88.38,223V71.12H89.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M90.18,223V71.41H91.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M91.98,223V71.88H93.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M93.78,223V71.96H95.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M95.58,223V72.05H97.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M97.38,223V72.08H98.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M99.18,223V72.11H100.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M100.98,223V72.15H102.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M102.78,223V72.15H104.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M104.58,223V72.2H106.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M106.38,223V72.29H107.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M108.18,223V72.45H109.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M109.98,223V72.65H111.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M111.78,223V72.78H113.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M113.58,223V72.8H115.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M115.38,223V72.85H116.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M117.18,223V73.04H118.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M118.98,223V73.1H120.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M120.78,223V73.14H122.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M122.58,223V73.18H124.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M124.38,223V73.19H125.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M126.18,223V73.2H127.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M127.98,223V73.22H129.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M129.78,223V73.25H131.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M131.58,223V73.28H133.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M133.38,223V73.4H134.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M135.18,223V73.52H136.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M136.98,223V73.56H138.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M138.78,223V73.58H140.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M140.58,223V73.67H142.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M142.38,223V73.69H143.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M144.18,223V73.69H145.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M145.98,223V73.76H147.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M147.78,223V73.8H149.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M149.58,223V74.16H151.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M151.38,223V74.29H152.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M153.18,223V74.38H154.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M154.98,223V74.43H156.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M156.78,223V74.5H158.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M158.58,223V74.54H160.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M160.38,223V74.6H161.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M162.18,223V74.61H163.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M163.98,223V74.75H165.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M165.78,223V74.88H167.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M167.58,223V74.91H169.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M169.38,223V74.93H170.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M171.18,223V75.05H172.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M172.98,223V75.1H174.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M174.78,223V75.13H176.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M176.58,223V75.19H178.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M178.38,223V75.24H179.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M180.18,223V75.31H181.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M181.98,223V75.33H183.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M183.78,223V75.47H185.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M185.58,223V75.5H187.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M187.38,223V75.51H188.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M189.18,223V75.56H190.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M190.98,223V75.63H192.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M192.78,223V75.67H194.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M194.58,223V75.73H196.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M196.38,223V75.73H197.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M198.18,223V75.79H199.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M199.98,223V75.84H201.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M201.78,223V75.86H203.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M203.58,223V75.9H205.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M205.38,223V75.91H206.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M207.18,223V76.03H208.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M208.98,223V76.04H210.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M210.78,223V76.1H212.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M212.58,223V76.1H214.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M214.38,223V76.11H215.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M216.18,223V76.14H217.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M217.98,223V76.15H219.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M219.78,223V76.25H221.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M221.58,223V76.28H223.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M223.38,223V76.46H224.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M225.18,223V76.52H226.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M226.98,223V76.54H228.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M228.78,223V76.6H230.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M230.58,223V76.61H232.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M232.38,223V76.63H233.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M234.18,223V76.65H235.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M235.98,223V76.66H237.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M237.78,223V76.7H239.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M239.58,223V76.73H241.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M241.38,223V76.76H242.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M243.18,223V76.78H244.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M244.98,223V76.82H246.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M246.78,223V76.83H248.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M248.58,223V76.83H250.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M250.38,223V76.84H251.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M252.18,223V76.9H253.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M253.98,223V76.92H255.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M255.78,223V76.97H257.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M257.58,223V76.97H259.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M259.38,223V76.98H260.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M261.18,223V77.01H262.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M262.98,223V77.25H264.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M264.78,223V77.25H266.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M266.58,223V77.33H268.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M268.38,223V77.35H269.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M270.18,223V77.35H271.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M271.98,223V77.35H273.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M273.78,223V77.35H275.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M275.58,223V77.35H277.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M277.38,223V77.35H278.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M279.18,223V77.35H280.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M280.98,223V77.35H282.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M282.78,223V77.35H284.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M284.58,223V77.35H286.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M286.38,223V77.35H287.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M288.18,223V77.35H289.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M289.98,223V77.35H291.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M291.78,223V77.35H293.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M293.58,223V77.36H295.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M295.38,223V77.37H296.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M297.18,223V77.55H298.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M298.98,223V77.61H300.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M300.78,223V77.73H302.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M302.58,223V77.75H304.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M304.38,223V77.78H305.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M306.18,223V77.85H307.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M307.98,223V77.9H309.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M309.78,223V77.98H311.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M311.58,223V77.99H313.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M313.38,223V78.17H314.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M315.18,223V78.18H316.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M316.98,223V78.2H318.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M318.78,223V78.41H320.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M320.58,223V78.52H322.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M322.38,223V78.65H323.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M324.18,223V78.65H325.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M325.98,223V78.67H327.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M327.78,223V78.93H329.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M329.58,223V79.06H331.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M331.38,223V79.11H332.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M333.18,223V79.16H334.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M334.98,223V79.38H336.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M336.78,223V79.42H338.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M338.58,223V79.82H340.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M340.38,223V79.89H341.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M342.18,223V79.92H343.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M343.98,223V80.08H345.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M345.78,223V80.3H347.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M347.58,223V80.32H349.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M349.38,223V80.61H350.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M351.18,223V80.79H352.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M352.98,223V81.35H354.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M354.78,223V81.42H356.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M356.58,223V81.53H358.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M358.38,223V81.56H359.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M360.18,223V81.62H361.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M361.98,223V82.19H363.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M363.78,223V82.59H365.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M365.58,223V82.72H367.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M367.38,223V82.75H368.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M369.18,223V82.78H370.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M370.98,223V83.18H372.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M372.78,223V83.38H374.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M374.58,223V83.61H376.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M376.38,223V83.97H377.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M378.18,223V84.44H379.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M379.98,223V84.76H381.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M381.78,223V84.97H383.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M383.58,223V85.08H385.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M385.38,223V85.85H386.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M387.18,223V86.25H388.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M388.98,223V87.62H390.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M390.78,223V87.63H392.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M392.58,223V87.76H394.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M394.38,223V87.94H395.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M396.18,223V88.14H397.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M397.98,223V88.83H399.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M399.78,223V88.98H401.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M401.58,223V89.04H403.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M403.38,223V89.54H404.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M405.18,223V90.02H406.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M406.98,223V90.16H408.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M408.78,223V90.21H410.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M410.58,223V90.63H412.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M412.38,223V91.14H413.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M414.18,223V91.9H415.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M415.98,223V92.76H417.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M417.78,223V92.95H419.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M419.58,223V93.07H421.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M421.38,223V93.15H422.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M423.18,223V93.56H424.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M424.98,223V93.78H426.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M426.78,223V94.22H428.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M428.58,223V94.4H430.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M430.38,223V94.49H431.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M432.18,223V95.73H433.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M433.98,223V95.82H435.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M435.78,223V96.31H437.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M437.58,223V96.96H439.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M439.38,223V97.46H440.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M441.18,223V97.93H442.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M442.98,223V98.16H444.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M444.78,223V98.67H446.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M446.58,223V98.71H448.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M448.38,223V99.39H449.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M450.18,223V100.18H451.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M451.98,223V100.57H453.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M453.78,223V102.19H455.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M455.58,223V102.85H457.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M457.38,223V102.87H458.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M459.18,223V102.92H460.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M460.98,223V104.75H462.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M462.78,223V104.99H464.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M464.58,223V106.31H466.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M466.38,223V107.64H467.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M468.18,223V108.13H469.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M469.98,223V108.65H471.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M471.78,223V110.17H473.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M473.58,223V112.31H475.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M475.38,223V112.31H476.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M477.18,223V114.48H478.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M478.98,223V115.66H480.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M480.78,223V116.52H482.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M482.58,223V120.97H484.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M484.38,223V122.41H485.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M486.18,223V123.13H487.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M487.98,223V124.26H489.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M489.78,223V127.38H491.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M491.58,223V129.76H493.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M493.38,223V136.38H494.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M495.18,223V141.07H496.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M496.98,223V143.72H498.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M498.78,223V147.75H500.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M500.58,223V147.79H502.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M502.38,223V153.09H503.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M504.18,223V154.22H505.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M505.98,223V155.38H507.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M507.78,223V155.51H509.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M509.58,223V156.8H511.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M511.38,223V157.12H512.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M513.18,223V158.01H514.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M514.98,223V159.28H516.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M516.78,223V161.56H518.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M518.58,223V163.1H520.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M520.38,223V164.74H521.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M522.18,223V165.27H523.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M523.98,223V166.16H525.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M525.78,223V173.48H527.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M527.58,223V173.58H529.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M529.38,223V178.02H530.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M531.18,223V178.19H532.62V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M532.98,223V179.2H534.42V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M534.78,223V186.59H536.22V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M536.58,223V195.1H538.02V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g><g class=\"point\"><path d=\"M538.38,223V218.15H539.82V223Z\" style=\"vector-effect: non-scaling-stroke; opacity: 1; stroke-width: 0.5px; fill: rgb(99, 110, 250); fill-opacity: 1; stroke: rgb(229, 236, 246); stroke-opacity: 1;\"/></g></g></g></g></g><g class=\"overplot\"/><path class=\"xlines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><path class=\"ylines-above crisp\" d=\"M0,0\" style=\"fill: none;\"/><g class=\"overlines-above\"/><g class=\"xaxislayer-above\"><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(80.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_21_4_2</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(95.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_05_11</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(109.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_11_15</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(124.1,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_09_13</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(138.5,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_08_18</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(152.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_22_02</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(167.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_09_09</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(181.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_11_25</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(196.1,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_15_07</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(210.5,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_08_02</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(224.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_6_03c</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(239.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">_m1_tutor9</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(253.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_11_07</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(268.1,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_17_06</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(282.5,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_13_02</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(296.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_17_08</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(311.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_04_09</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(325.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_6_10</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(340.1,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_11_02</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(354.5,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_03_13</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(368.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_09_08</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(383.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_7_14</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(397.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_15_02</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(412.1,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_07_18</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(426.5,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_03_15</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(440.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_08_01</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(455.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_08_08</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(469.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_08_06</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(484.1,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_08_07</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(498.5,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_22_11</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(512.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_07_25</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(527.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">_m6_assess</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(541.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">_m8_assess</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(556.1,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_22_20</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(570.5,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_22_16</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(584.9,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_20_3_4</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(599.3,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">_m20_assess</text></g><g class=\"xtick\"><text text-anchor=\"start\" x=\"0\" y=\"336\" transform=\"translate(613.7,0) rotate(90,0,330)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">tutor_08_24</text></g></g><g class=\"yaxislayer-above\"><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,323)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,250.18)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">0.5</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,177.35)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1</text></g><g class=\"ytick\"><text text-anchor=\"end\" x=\"79\" y=\"4.199999999999999\" transform=\"translate(0,104.53)\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 12px; fill: rgb(42, 63, 95); fill-opacity: 1; white-space: pre;\">1.5</text></g></g><g class=\"overaxes-above\"/></g></g><g class=\"polarlayer\"/><g class=\"ternarylayer\"/><g class=\"geolayer\"/><g class=\"funnelarealayer\"/><g class=\"pielayer\"/><g class=\"treemaplayer\"/><g class=\"sunburstlayer\"/><g class=\"glimages\"/><defs id=\"topdefs-44afe5\"><g class=\"clips\"/></defs><g class=\"layer-above\"><g class=\"imagelayer\"/><g class=\"shapelayer\"/></g><g class=\"infolayer\"><g class=\"g-gtitle\"><text class=\"gtitle\" x=\"35\" y=\"50\" text-anchor=\"start\" dy=\"0em\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 17px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Questions sorted by the average accuracy</text></g><g class=\"g-xtitle\"><text class=\"xtitle\" x=\"350\" y=\"438.034375\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Problem Name</text></g><g class=\"g-ytitle\"><text class=\"ytitle\" transform=\"rotate(-90,34.575,211.5)\" x=\"34.575\" y=\"211.5\" text-anchor=\"middle\" style=\"font-family: 'Open Sans', verdana, arial, sans-serif; font-size: 14px; fill: rgb(42, 63, 95); opacity: 1; font-weight: normal; white-space: pre;\">Avg Corrects</text></g></g></svg>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Classification Statistic\n", "\n", "# Problem Name,Avg Corrects, Avg Assistance Score\n", "df_problem_group1 = df_problem_clear.groupby(['Problem Name'])['Avg Corrects'].mean().reset_index()\n", "df_problem_group1.columns = [\"Problem Name\",\"Avg Corrects\"]\n", "df_problem_group1 = df_problem_group1.sort_values(by='Avg Corrects', ascending=False)\n", "fig = px.bar(df_problem_group1, x=\"Problem Name\", y=\"Avg Corrects\", title=\"Questions sorted by the average accuracy\")\n", "fig.show(\"svg\")"]}], "metadata": {"celltoolbar": "原始单元格格式", "kernelspec": {"display_name": "Data", "language": "python", "name": "data"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.13"}}, "nbformat": 4, "nbformat_minor": 4}