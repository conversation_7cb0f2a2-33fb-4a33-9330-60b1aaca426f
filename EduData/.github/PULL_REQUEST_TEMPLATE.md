Thanks for sending a pull request! 
Please make sure you click the link above to view the [contribution guidelines](https://github.com/bigdata-ustc/EduData/blob/master/CONTRIBUTE.md), 
then fill out the blanks below.

## Description ##
(Brief description on what this PR is about)

### What does this implement/fix? Explain your changes.
...

#### Pull request type
- [ ] [DATASET] Add a new dataset
- [ ] [BUGFIX] Bugfix
- [ ] [FEATURE] New feature (non-breaking change which adds functionality)
- [ ] [BREAKING] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] [STYLE] Code style update (formatting, renaming)
- [ ] [REFACTOR] Refactoring (no functional changes, no api changes)
- [ ] [BUILD] Build related changes
- [ ] [DOC] Documentation content changes
- [ ] [OTHER] Other (please describe): 


#### Changes
- Feature1, tests, (and when applicable, API doc)
- Feature2, tests, (and when applicable, API doc)

or

- Fix1, tests
- Fix2, tests

### Does this close any currently open issues?
...

### Any relevant logs, error output, etc?
...

## Checklist ##
Before you submit a pull request, please make sure you have to following:

### Essentials ###
- [ ] PR's title starts with a category (e.g. [BUGFIX], [FEATURE], [BREAKING], [DOC], etc)
- [ ] Changes are complete (i.e. I finished coding on this PR)
- [ ] All changes have test coverage and al tests passing
- [ ] Code is well-documented (extended the README / documentation, if necessary)
- [ ] If this PR is your first one, add your name and github account to [AUTHORS.md](https://github.com/bigdata-ustc/EduData/blob/master/AUTHORS.md)

## Comments ##
- If this change is a backward incompatible change, why must this change be made.
- Interesting edge cases to note here
