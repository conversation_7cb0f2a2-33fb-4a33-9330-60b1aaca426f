[pytest]
# For pytest usage, refer to https://hb4dsai.readthedocs.io/zh/latest/Architecture/Test.html
norecursedirs = docs *build* trash dev EduData/Task EduData/Dataset/EdNet EduData/Dataset/junyi EduData/Dataset/synthetic

# Deal with marker warnings
markers =
    flake8: flake8

# Enable line length testing with maximum line length of 120
flake8-max-line-length = 120

# Ignore module level import not at top of file (E402)
# Others can be found in https://flake8.pycqa.org/en/latest/user/error-codes.html
flake8-ignore = E402 F401 F403 F405 F821 F841 W605 E126

# --doctest-modules is used for unitest
addopts = --doctest-modules --cov --cov-report=term-missing --flake8
