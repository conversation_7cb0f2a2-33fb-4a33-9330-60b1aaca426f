{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Performance Factors Analysis (PFA) Model Training\n", "\n", "## ⚠️ **IMPORTANT: To see skill-specific parameters, please:**\n", "1. **<PERSON><PERSON>** (Kernel → Restart)\n", "2. **Run All Cells** (Cell → Run All)\n", "\n", "This ensures you get the latest PFA model with unique parameters for each skill!\n", "\n", "---\n", "\n", "This notebook demonstrates the complete workflow for training a PFA model with **skill-specific parameter estimation**:\n", "1. Load and explore the dataset\n", "2. Clean and preprocess the data\n", "3. Explore data statistics and skills\n", "4. Split data into train/test sets\n", "5. **Train the PFA model with skill-specific parameters**\n", "6. **Analyze parameter distributions and skill characteristics**\n", "7. Save the trained model\n", "8. Show training statistics\n", "9. Run prediction examples"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, roc_auc_score, classification_report\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PFA model imported successfully!\n", "✅ Using updated PFA model with skill-specific parameter estimation\n"]}], "source": ["# Import PFA model from the existing implementation\n", "import sys\n", "import os\n", "sys.path.append('App/Dashboard/models')\n", "\n", "# Force reload the module to get latest changes\n", "import importlib\n", "if 'train_pfa' in sys.modules:\n", "    importlib.reload(sys.modules['train_pfa'])\n", "\n", "from train_pfa import PerformanceFactorsAnalysis, load_skill_builder_data\n", "from joblib import dump, load\n", "\n", "print(\"PFA model imported successfully!\")\n", "print(\"✅ Using updated PFA model with skill-specific parameter estimation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Dataset\n", "\n", "We'll load the 2009 Skill Builder dataset which contains student responses to educational problems."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the dataset\n", "DATA_PATH = \"../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading dataset...\")\n", "df = load_skill_builder_data(DATA_PATH)\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["First 5 rows of the dataset:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>assignment_id</th>\n", "      <th>user_id</th>\n", "      <th>assistment_id</th>\n", "      <th>problem_id</th>\n", "      <th>original</th>\n", "      <th>correct</th>\n", "      <th>attempt_count</th>\n", "      <th>ms_first_response</th>\n", "      <th>tutor_mode</th>\n", "      <th>...</th>\n", "      <th>hint_count</th>\n", "      <th>hint_total</th>\n", "      <th>overlap_time</th>\n", "      <th>template_id</th>\n", "      <th>answer_id</th>\n", "      <th>answer_text</th>\n", "      <th>first_action</th>\n", "      <th>bottom_hint</th>\n", "      <th>opportunity</th>\n", "      <th>opportunity_original</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>33022537</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33139</td>\n", "      <td>51424</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>32454</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>32454</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>26</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33022709</td>\n", "      <td>277618</td>\n", "      <td>64525</td>\n", "      <td>33150</td>\n", "      <td>51435</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4922</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4922</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>55</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35450204</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33159</td>\n", "      <td>51444</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>25390</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>42000</td>\n", "      <td>30799</td>\n", "      <td>NaN</td>\n", "      <td>88</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>35450295</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33110</td>\n", "      <td>51395</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4859</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>4859</td>\n", "      <td>30059</td>\n", "      <td>NaN</td>\n", "      <td>41</td>\n", "      <td>0</td>\n", "      <td>NaN</td>\n", "      <td>2</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35450311</td>\n", "      <td>220674</td>\n", "      <td>70363</td>\n", "      <td>33196</td>\n", "      <td>51481</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>14</td>\n", "      <td>19813</td>\n", "      <td>tutor</td>\n", "      <td>...</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>124564</td>\n", "      <td>30060</td>\n", "      <td>NaN</td>\n", "      <td>65</td>\n", "      <td>0</td>\n", "      <td>0.0</td>\n", "      <td>3</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 30 columns</p>\n", "</div>"], "text/plain": ["   order_id  assignment_id  user_id  assistment_id  problem_id  original  \\\n", "0  33022537         277618    64525          33139       51424         1   \n", "1  33022709         277618    64525          33150       51435         1   \n", "2  35450204         220674    70363          33159       51444         1   \n", "3  35450295         220674    70363          33110       51395         1   \n", "4  35450311         220674    70363          33196       51481         1   \n", "\n", "   correct  attempt_count  ms_first_response tutor_mode  ... hint_count  \\\n", "0        1              1              32454      tutor  ...          0   \n", "1        1              1               4922      tutor  ...          0   \n", "2        0              2              25390      tutor  ...          0   \n", "3        1              1               4859      tutor  ...          0   \n", "4        0             14              19813      tutor  ...          3   \n", "\n", "   hint_total  overlap_time  template_id answer_id  answer_text  first_action  \\\n", "0           3         32454        30799       NaN           26             0   \n", "1           3          4922        30799       NaN           55             0   \n", "2           3         42000        30799       NaN           88             0   \n", "3           3          4859        30059       NaN           41             0   \n", "4           4        124564        30060       NaN           65             0   \n", "\n", "  bottom_hint  opportunity  opportunity_original  \n", "0         NaN            1                   1.0  \n", "1         NaN            2                   2.0  \n", "2         NaN            1                   1.0  \n", "3         NaN            2                   2.0  \n", "4         0.0            3                   3.0  \n", "\n", "[5 rows x 30 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# Display first few rows\n", "print(\"First 5 rows of the dataset:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Info:\n", "Total records: 401,756\n", "Memory usage: 200.29 MB\n", "\n", "Data types:\n", "order_id                  int64\n", "assignment_id             int64\n", "user_id                   int64\n", "assistment_id             int64\n", "problem_id                int64\n", "original                  int64\n", "correct                   int64\n", "attempt_count             int64\n", "ms_first_response         int64\n", "tutor_mode               object\n", "answer_type              object\n", "sequence_id               int64\n", "student_class_id          int64\n", "position                  int64\n", "type                     object\n", "base_sequence_id          int64\n", "skill_id                float64\n", "skill_name               object\n", "teacher_id                int64\n", "school_id                 int64\n", "hint_count                int64\n", "hint_total                int64\n", "overlap_time              int64\n", "template_id               int64\n", "answer_id               float64\n", "answer_text              object\n", "first_action              int64\n", "bottom_hint             float64\n", "opportunity               int64\n", "opportunity_original    float64\n", "dtype: object\n", "\n", "Missing values:\n", "order_id                     0\n", "assignment_id                0\n", "user_id                      0\n", "assistment_id                0\n", "problem_id                   0\n", "original                     0\n", "correct                      0\n", "attempt_count                0\n", "ms_first_response            0\n", "tutor_mode                   0\n", "answer_type                  0\n", "sequence_id                  0\n", "student_class_id             0\n", "position                     0\n", "type                         0\n", "base_sequence_id             0\n", "skill_id                 63755\n", "skill_name               76119\n", "teacher_id                   0\n", "school_id                    0\n", "hint_count                   0\n", "hint_total                   0\n", "overlap_time                 0\n", "template_id                  0\n", "answer_id               356302\n", "answer_text              89208\n", "first_action                 0\n", "bottom_hint             334712\n", "opportunity                  0\n", "opportunity_original     73465\n", "dtype: int64\n"]}], "source": ["# Basic dataset information\n", "print(\"Dataset Info:\")\n", "print(f\"Total records: {len(df):,}\")\n", "print(f\"Memory usage: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB\")\n", "print(\"\\nData types:\")\n", "print(df.dtypes)\n", "print(\"\\nMissing values:\")\n", "print(df.isnull().sum())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Clean Dataset\n", "\n", "Clean the data by handling missing values, duplicates, and ensuring proper data types."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Missing values in key columns:\n", "user_id: 0 (0.00%)\n", "problem_id: 0 (0.00%)\n", "skill_name: 76119 (18.95%)\n", "correct: 0 (0.00%)\n"]}], "source": ["# Check for missing values in key columns\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "print(\"Missing values in key columns:\")\n", "for col in key_columns:\n", "    if col in df.columns:\n", "        missing = df[col].isnull().sum()\n", "        print(f\"{col}: {missing} ({missing/len(df)*100:.2f}%)\")\n", "    else:\n", "        print(f\"{col}: Column not found\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = [col for col in key_columns if col in df.columns]\n", "df_clean = df.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "if 'problem_id' in df_clean.columns:\n", "    df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Explore Dataset\n", "\n", "Analyze the dataset to understand the distribution of skills, users, and performance."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Statistics:\n", "Total interactions: 325,637\n", "Unique users: 4,151\n", "Unique problems: 16,891\n", "Unique skills: 110\n", "Overall accuracy: 0.658\n"]}], "source": ["# Basic statistics\n", "print(\"Dataset Statistics:\")\n", "print(f\"Total interactions: {len(df_clean):,}\")\n", "print(f\"Unique users: {df_clean['user_id'].nunique():,}\")\n", "print(f\"Unique problems: {df_clean['problem_id'].nunique():,}\")\n", "print(f\"Unique skills: {df_clean['skill_name'].nunique():,}\")\n", "print(f\"Overall accuracy: {df_clean['correct'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SKILLS ANALYSIS ===\n", "\n", "Top 15 skills by number of attempts:\n", "                                          Total_Attempts  Accuracy  \\\n", "skill_name                                                           \n", "Equation Solving Two or Fewer Steps                24253     0.679   \n", "Conversion of Fraction Decimals Percents           18742     0.637   \n", "Addition and Subtraction Integers                  12741     0.599   \n", "Addition and Subtraction Fractions                 11334     0.677   \n", "Percent Of                                          9497     0.595   \n", "Proportion                                          9054     0.641   \n", "Ordering Fractions                                  8539     0.792   \n", "Equation Solving More Than Two Steps                8115     0.758   \n", "Probability of Two Distinct Events                  7963     0.490   \n", "Finding Percents                                    7694     0.538   \n", "Subtraction Whole Numbers                           7669     0.641   \n", "Probability of a Single Event                       7438     0.742   \n", "Pattern Finding                                     7343     0.600   \n", "Absolute Value                                      7340     0.757   \n", "Ordering Positive Decimals                          7317     0.750   \n", "\n", "                                          Unique_Users  Unique_Problems  \n", "skill_name                                                               \n", "Equation Solving Two or Fewer Steps                961             1040  \n", "Conversion of Fraction Decimals Percents          1225              488  \n", "Addition and Subtraction Integers                 1226              413  \n", "Addition and Subtraction Fractions                1353              433  \n", "Percent Of                                        1115              465  \n", "Proportion                                         756              485  \n", "Ordering Fractions                                 882              464  \n", "Equation Solving More Than Two Steps               412              419  \n", "Probability of Two Distinct Events                 452              339  \n", "Finding Percents                                   771              371  \n", "Subtraction Whole Numbers                          903              242  \n", "Probability of a Single Event                      939              350  \n", "Pattern Finding                                    447              554  \n", "Absolute Value                                    1002              241  \n", "Ordering Positive Decimals                         942              543  \n"]}], "source": ["# Skills list and statistics\n", "print(\"\\n=== SKILLS ANALYSIS ===\")\n", "skill_stats = df_clean.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean'],\n", "    'user_id': 'nunique',\n", "    'problem_id': 'nunique'\n", "}).round(3)\n", "\n", "skill_stats.columns = ['Total_Attempts', 'Accuracy', 'Unique_Users', 'Unique_Problems']\n", "skill_stats = skill_stats.sort_values('Total_Attempts', ascending=False)\n", "\n", "print(f\"\\nTop 15 skills by number of attempts:\")\n", "print(skill_stats.head(15))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAASmCAYAAADBBeLHAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdd3yN9///8cc5WdIgqJkaNSMqQUiJEa2iaGvWFi1F0aJW7CI2NT5ilRoVo4rUnh/6MVoNakTN2qGtGpFB9snvD7+crzQ5kdTI8LzfbrnJucb7/bqu1zntldd5X+/LkJCQkICIiIiIiIiIiIiIiCRjzOgAREREREREREREREQyKxXRRUREREREREREREQsUBFdRERERERERERERMQCFdFFRERERERERERERCxQEV1ERERERERERERExAIV0UVERERERERERERELFARXURERERERERERETEAhXRRUREREREREREREQsUBFdRERERERERERERMQCFdFFRCRLGDp0KM7Ozqn+eHt7P9cY/vjjDwYNGkTt2rWpUqUKLVu2ZOfOnUm2iYmJYcaMGbz11ltUrFiR9957j4CAgCe2HRcXx5o1a2jTpg01atTA1dWVunXrMmDAAM6fP2/eLjAwEGdnZ/bv32+xrXr16tG/f38Abty4gbOzM6tXrwYgICAAZ2dnLl269G9OAfAoF/Xq1fvX+6fFu+++i7OzMz/++ONz7UdERESyp39eO1asWJG33nqLfv36cejQoWTbP3799Cz5+fnh7OxMdHQ0AN7e3s/tmnXo0KHUqlXrubT9b+3atYu6devi5ubGsWPHLG73008/0b17d+rWrUvFihWpWbMmH3/8Mf/973+TbOfs7MxXX31lsZ1/noPUrotT8+uvv+Ls7EytWrWIi4t74vYikv2piC4iIlnCiBEjOHjwoPnnnXfeoXDhwkmW+fn5Pbf+Q0ND8fb25saNG/j5+bF+/XqqVq1K3759OXjwoHm7cePG8f333/Pll1+yY8cO2rVrx4gRI9i6dWuq7Y8ePZrJkyfTrFkzVqxYwfbt2xk/fjzBwcF06NCB4ODgNMe6bt06fH19//WxZrTDhw9z9epVXFxcWL9+fYrbjBo1KskfUHFxcVSuXPmpvhx41jJjTCIiIi+TfPnyma8Td+7cyeTJk8mdOzddu3Zl4sSJSbZN7/XTsWPH0lSw7tq1KwcPHsTOzi7d8ael7ccLwiNGjGDz5s3PvJ+n4efnh6OjI1u2bKFChQopbrNx40a6du1KyZIlmTdvHrt27WL+/PkUKFCAzz//nB07dqS5v2d1DtatW0f58uW5d+8e+/bte+r2RCTrs87oAERERNIiV65c5MqVy/zazs4OKysrChQo8EL637ZtGzdu3GD58uW89tprAAwfPpw9e/bwww8/ULt2bf744w/zH2CJI7W9vb05ceIEs2fP5r333kux7QcPHvDDDz/Qo0cPOnbsaF5etGhR3njjDT7++GNOnjxJsWLF0hRrvnz5nvJoM9batWupXLky7du3Z+TIkdy7dy/ZMZ04cYK6deuaX587d47IyMgXHWqqMmNMIiIiLxOj0ZjkWvG1116jRo0aVKtWDR8fH8qWLUvr1q2B9F8/nThxItX1cXFxWFlZ4eDggIODQ7pjfxKTycTJkydp0KCBednj18qZRUhICLVq1aJ48eIWt1mxYgVVq1Zl+PDh5mVOTk5UqlSJ0NBQgoKCaNSoUZr6exbnICIigh07djBkyBC2bdvG+vXreeedd5663WchNjYWGxubjA5D5KWkkegiIpKt/Pjjj7Rp0wY3NzcqV65Mhw4d+OWXX8zrDx48iLOzMwcPHuTzzz+nSpUqVK1alaFDh6Za8GzVqhX79+83F9ABDAYD+fLl4++//wYe3YZqMpnw8vJKsq+XlxdXr161OJrcZDIRHx9vvs33cfny5WPTpk28//77FmPbu3cvFSpUYNWqVUD6bkcODQ1lxIgR1KlTh4oVK1K3bl3Gjx9PVFTUE/f96aef+OCDD6hYsSL16tVj3bp15nicnZ0JDAxMsn1MTAzVqlVj8uTJFtsMDw9n165dNGvWjAYNGmBjY8PGjRuTbOPs7MyFCxdYtGgRzs7OBAQE0KpVKwCaNGmS5BbpFStW0LhxY/NtwV9++SXh4eHm9QMHDqRNmzb873//491338XV1ZXmzZtz9uxZDh8+TNOmTalUqRItW7bk3Llz5v3atWtHz5492bhxIw0aNDBP3ZM4UslSTOfOnaN79+7UqFEDNzc3mjRpgr+//xPPtYiIiDw7zZo1o1atWixcuNC87J/XT7t376ZVq1a4u7vj7u5Ou3bt+Pnnn4FHU4ZMmTKFO3fu4OzsjJ+fn3mqkLVr19KuXTvc3NwIDw9PNp1Los2bN9OwYUMqVqxI48aNk0xhl9I+0dHRSfpycXEhIiKCMWPG4OzsbI7r8dHxCQkJfPPNN7z77rtUrFiR6tWr07dv3yTXpDNnzqRatWpcvHiRDh06UKlSJby8vJgzZ84Tz2Nq192J5+P27dts2LAhxWvDRLGxscTExJCQkJBs3cKFC/Hx8bEYQ3BwMLVq1WLAgAGYTKZnMqXNli1biIuLo1GjRjRr1ox9+/Zx586dZNsdO3aMjh07UqlSJTw9PRk8eHCS7cLDwxk1ahSenp5UrlyZNm3a8NNPP5nXe3t706ZNmyRt7t+/P8m58vPzo1q1auzZs4fatWvzxRdfABAZGcn48eOpV68erq6ueHl5MXz4cEJCQtIU471793B1dU3xTt5PPvmEli1b/uvzJ5JdqYguIiLZxqFDh+jVqxflypXj+++/Z9WqVRQsWJBu3bqZC6DW1o9uwho/fjzvvvsumzZtYuTIkWzdupWpU6dabNvW1pZChQolWXbjxg3OnTtHlSpVALh8+XKK2yWOIL98+XKKbefKlQt3d3eWLl3KuHHjCAoKIj4+Pk3HfPLkSQYMGMBnn31Ghw4d0rTP48aPH09QUBCzZ89m165djB8/nj179jBp0qRU9wsLC2PevHmMHj2aDRs2UK1aNUaOHMnJkyd56623cHJySjYX/P79+wkPDzeP+ErJpk2bAHj//fdxcHCgUaNGyaZ0SZw+p1OnThw8eJBGjRqZRy6tXLnS/MfA119/zYQJE2jWrBmbN29m0qRJ7N+/n88++8zclo2NDX///TerVq3iP//5D8uXL+fOnTv4+Pgwb948Jk6ciL+/P3fu3Ely27e1tTWnT59m586d+Pn5sW7dOgoXLkyfPn34888/adKkSYox9ezZk5w5c+Lv78/WrVvp0qULU6ZMYdu2bamebxEREXm23n77ba5fv86ff/6ZbN2VK1f44osvePfdd9m4cSNr167F1dWVHj168OeffzJixAgaN25sni6ma9eu5n2XLl3Khx9+yM6dO8mZM2eKfV+5coUNGzbw1VdfsW7dOpycnOjbt2+KsaSkSJEirF27FoBBgwYlmVrwcX5+fsyaNYv27duzefNmZs+ezZUrV/joo4/Mg0esra2Ji4tj7Nix9O7dm61bt9KkSRP8/Pw4fPiwxRiedN1dpEgRDh48SL58+WjcuDEHDx40XzP/U926dTl16hTdunVj//79ab6TLyQkhG7duuHi4sKUKVMwGp9NiWvt2rU0bNiQPHny0LhxY+zs7JIN6rh06RIff/wxJUuWZN26dSxYsIDz58/To0cP85cBvXv35vDhw8yYMYONGzdSuXJlPv30U06dOpWueOLj41mxYgULFixgzJgxwKNr+I0bNzJmzBh27tzJ9OnT+eWXX/jyyy/TFGPevHlp2LAhP/zwQ5IvL0JCQvjll1/48MMP/+XZE8m+VEQXEZFs45tvvqFo0aKMGzeO8uXLU6FCBSZPnoy9vb15tK/BYAAeXax/8MEHFCtWjBYtWvDBBx+wefPmFEfApCQqKopBgwbh6OjIxx9/DDwabZLS7bqJf0CFhYVZbG/GjBl4eHiwYsUKWrdujYeHB926dWP16tUW/5C4evUqn376Ka1bt05SGE6P06dP4+7uTpUqVXBycqJOnTp8++23dOnSJdX9wsPDGTZsGNWqVaNMmTKMGzeOHDlysHnzZoxGo/mPx4iICPM+27dvp0qVKpQuXdpiu+vWraNhw4bkzp0bgA8//JDff/+doKAg8zaJt2Xb29tToEABXnnlFfOtu3nz5iVPnjzExsayaNEimjRpQs+ePSlZsiR169ZlxIgRBAYGcvLkSXN7f/75J6NHj6Z8+fJUqVKFBg0acOHCBfr160fFihVxc3Ojfv36nD171ryPwWDg3r17TJgwgfLly1O+fHnGjx9PdHQ0O3fuJEeOHMliunv3Ln/++ScNGjSgbNmyFCtWjNatW7N27Vo8PDzSmjIRERF5BooUKQJgvqPwcWfPniUuLo6WLVtSrFgxSpcuzbBhw/D39yd37tzkypWLHDlymKeLefz6r0yZMnz44YcUK1bMYlE3JCSEKVOm4ObmRvny5Rk3bhwxMTHJHlhviZWVlXn6mZw5c6Y4vWFMTAzLli3jgw8+MBdSq1evjq+vLzdv3mTXrl3mbSMjI+natSu1a9emaNGifPrppwBJrr/+6UnX3YnTLhqNRnLkyEGBAgWwtbVNsa3PPvuMNm3acOjQIbp3746Hhwdt27Zlzpw53Lp1K8V9oqKi6NmzJ3nz5sXPz++ZTXFy7tw5fvvtN3MR+ZVXXqFJkybJBnUsX74cR0dHfH19KVu2LJUqVWL06NGUKVOGO3fucPLkSQ4fPsyIESPw9PSkRIkSDB06lPfff58//vgjXTE9fPiQzp07U7FiRXOuBw4cyObNm/Hy8sLJyQkPDw/zlxWJf888Kcb27dtz8+bNJHft7tq1C2traz744IOnOY0i2ZKK6CIikm2cOnWKatWqmQvlADly5KB8+fJJCqAA7u7uSV67uLgQHh7O7du3n9hPREQE3bp14+LFi8yfP/+Jc2gmXsg+Htc/FSlSBH9/f7Zt28awYcOoXr06J06cYMyYMbz33ntcvXo1yfb37t2jW7du1KpVK8n8kenVoEEDvv/+e4YNG8bu3bsJCwujePHivP7666nuZ2dnR8WKFZO8LlmyJFeuXAGgdevWxMbGsn37duDRHzp79+5NdRT66dOnOXPmDC1btiQuLo64uDgqVapkHj2THpcvXyY8PJzq1asnWe7p6Qk8urU1Ub58+ZJM05M3b17g0XsiUZ48eZJ9CVKqVCnztvAoh3ny5LF4x0G+fPmoWrUqo0ePZvr06QQGBhITE4OLi8sLm9tfREREHomJiQFI8YGfVatW5dVXX6VTp04sXryYc+fOYTAYqFKlyhPnN3/8+siS4sWLkz9/fvNrJycnHB0dLV5D/BuXL1/mwYMHyb6or1SpElZWVsmujStXrmz+PU+ePMCjaf8sSc9195PY2toybtw49u/fz6RJk3j//fe5desWfn5+NGzYkP/+979Jto+Pj6d///6Eh4fz9ddfY29vn67+UrN27Vpee+01qlWrZr4ebd68OZcuXUoyD/6pU6dwcXFJ8kVJ1apVmTp1KgUKFDB/AfH4+8FoNDJ58mTefffddMf1z/dVbGwss2bN4p133qFq1apUqVKFb7/9locPH5rf20+KsVq1apQtWzbJFwTbtm2jUaNGmXJ+fZGMpiK6iIhkGxERETg6OiZb7ujomGRENCR/6FDixfeTbh+9ffs2HTt25OrVq/j7++Pm5pasn3+OZk+cgzul2P6pdOnSfPzxx8yfP5+ff/6ZMWPG8PfffyebambcuHHcvHmT27dvp3n0fEr69+/PpEmTuHnzJv3796dGjRp8/vnnFkf9JErpWHLkyMHDhw8BKFiwIPXq1TNflP/4448YDAYaN25ssc3vv/8egI8//pg33njD/HPlyhW2bduWpnnaEyWe8/Hjx1OlShXzT506dQCSfFnyzz+8Ev8YzJEjR7Jlj0vpFm17e3vzOfgng8HA4sWL6dq1K/v376dz587UqFGDKVOmmP/YERERkRfj+vXrGAyGZNPwARQqVIi1a9dSq1Yt/P39adasGW+99VaavtRPS/HxSddRz0Lite8/+zIajeTMmTPZtfErr7xi/j3xuie1a8z0XHenVf78+WnZsiWTJ0/mxx9/ZNmyZeTKlYsRI0Ykmerw+++/53//+x/3799/ptdQ0dHRbN68mZs3b1KxYkXztWjHjh0BkhSbw8PDk1wr/lPitWhKX9L8G4l3acKj5yn17t2bPXv20KdPH9asWcOGDRto165dshhSixGgbdu27N69m/DwcO7cucORI0dSHfQi8jKzzugAREREnpVcuXKlOGLm/v37yQqe/9wu8Y+W1EYXhYWF0bVrV+Lj4/n+++9xcnJKsr506dLExsbyxx9/JBnZfO3aNfN6S+7evcurr76aZJmtrS3t27fn4MGDSR5qCY8eftW2bVs++ugj5s+f/6+ncwFo3rw5zZs358GDB+zfv5+pU6cyaNCgVB94+fjDOROFhYWZb42GRxfl3bp14+bNm2zfvp33338/yR9oj4uKimLr1q107tyZpk2bJln34MEDunTpws6dO2nWrFmajinxj7pBgwZRt27dZOufxeialKbnefjwYarvIXt7e3r16kWvXr24ffs2W7ZsYcaMGTg4OPD5558/dUwiIiKSNrt27aJy5cpJ7ip73GuvvcaoUaMYNWoUFy9eZPny5YwYMYLixYvz5ptvPlXflq6jEq8hUvryPj2DCeD/rnX+ec0bHx9PeHj4U18Lpee6+0nCw8Oxs7NLMt2LwWDA09OT7t27M3HiRG7dumW+9i5atCgzZsygW7du+Pj4sGTJklTv+EyrnTt3EhYWxpIlS5IUreHRtIRr1qxh+PDh2NvbkytXrmQP8Xxc4vm9f//+E+9eeNw/H0CbkuDgYH777TfGjh1L8+bNzcv/+UylJ8UIj/4OmD59Onv27CEiIoLixYtTrVq1NMcr8jLRSHQREck2KlWqxNGjR5OMmnnw4AFnzpxJMmIc4Pjx40lenzlzhrx58ya5tfZxCQkJfPHFF8TGxrJixYpkBXSA2rVrY21tzd69e5Ms37t3L87OzinuA7BkyRJq1aqVrFCe2O8ff/yRbJTUBx98QLVq1ejbty9z587l6NGjKbadmsjISLZu3WouBjs4ONC4cWO6dOnC6dOnn7jv4/FGRERw/fp1ypYta15Wq1YtihUrZh4tlNqolu3btxMeHk6nTp1wdXVN8lOjRg3efPPNZHNRpjY6qmTJkuTOnZubN29SokQJ80/RokWJi4t74hQ8aXHt2jXu3btnfn3jxg1CQ0MpV65citvfunUryQNECxQoQJcuXahbty6//fbbU8cjIiIiabN8+XJOnz5N9+7dU1x/9uxZDh06ZH5dpkwZfH19yZMnT5JrpH97N+A/ryEuX75MZGSk+ToqsYD7+IjuM2fOpNiWpRhKlSpFrly5OHLkSJLlv/76KyaTCVdX138Ve6L0XHenJigoCA8PD/Mdif908+ZNbG1tzVPMANSpU4fSpUszbdo0AgMDWbhw4b8+jsetXbsWd3d3atWqlex6tFOnTjx48MA8b72rqyunT59OchdrUFAQ7du35+rVq+Zz8M9r9M8++4xvv/0WSHnUvqU8Py5xn8evZyMiIti9ezfwf++JJ8UIjwrtjRs3ZsuWLWzZskWj0EVSoSK6iIhkG4mjnkePHs2FCxc4deoUAwcOJD4+Hm9v7yTb/ve//2XdunVcu3aN9evXs2XLFlq0aGGx7e3bt/PTTz8xZMgQ4uPjuX37dpIfeDSFSYcOHfDz8+O///0vN27cYNGiRezZs4cvvvjCYtvNmzenePHi9OjRg7Vr13Lx4kVu3rzJkSNHGDBgAL///ju9evVKcd/Ehy8NGjSI+/fvp+t8WVtbM23aNAYPHkxQUBB//vknx44dY8OGDamOsEpISCBXrlyMHz+eX3/9lYsXLzJ69Gji4uKSjBQ3GAy0adOGxYsXU7JkyVT/WFu7di2urq6UKFEixfXvvfcehw8fJjg4GHj0x2VQUBBnz54lLCzM/Mfmvn37OHfuHNbW1nTr1o1Vq1bh7+/PtWvXOHfuHMOGDaNNmzYpPkQsvXLlyoWPjw+//fYb586dY+TIkbzyyivmeS7/GVNoaCiDBg3iq6++Mud4z549HD58+KlHtImIiEhyJpPJfK32119/cfToUUaMGMHEiRPp1asX77zzTor7nThxgt69e7N+/XqCg4O5fv06y5YtIzw8nKpVqwKP/j9///59fvnlF/Ndh2mRkJBAnjx5GDp0qPkawtfXN8k1ROI107p164iPj+fatWssWrQoydQgidcZR48e5cyZM8lGqtvY2NC1a1c2b97M8uXLuXr1Kj///DOjRo2iVKlS1K9fP+0nMgXpue5OjZubG/Xq1WPy5MnMmTOHM2fO8Mcff3D69Gn+85//sGLFCrp165bi3YzVqlWjd+/ezJ49O9kAmfS6fv06R44c4b333ktxvZOTE5UrVzYP6vD29iY2NpahQ4dy/vx5goKCGDt2LFFRURQvXpxKlSrh4eHB9OnT2b9/P9evX+err75i3759VKlSBXiU56tXr3L48GFMJhOHDh1iz549T4y1ZMmSODo6smrVKq5cucLRo0f59NNPqVevHgCBgYE8fPjwiTEmat++PYcOHeK3335LMrJdRJLSdC4iIpJtvPnmmyxYsIA5c+bQqlUrrK2tqVSpEsuXL082lcrAgQPZunUrEyZMwMrKihYtWtC/f3+LbR84cACAnj17prj+/PnzAAwZMoScOXPi6+vLvXv3KFmyJDNnzjRf1KYkX758fPfdd3z77bd8++23TJkyhcjISPLly4e7uzurVq2yOKLHaDQybdo0mjZtyvDhw5k3b16q5+hxNjY2LFmyhOnTp/Ppp58SHh5O/vz58fLyYsCAARb3i4uLo1ChQnz66aeMHj2aq1evUrhwYaZNm0b58uWTbNukSRO++uor2rRpY7G9K1eu8OuvvzJs2DCL2zRs2BBfX18CAgLo168fvXr1Ys6cOXzyySfMmzeP2rVr8+abbzJr1ix27drFd999x6effoqDgwMrV65kypQpvPLKK1SpUoWVK1dSsGDBNJ8nS8qUKUOjRo344osv+OuvvyhZsiTz5s0z382QUkzz589nwYIFfPfdd8TGxuLk5ES3bt3o0qXLU8cjIiIiSd27d4/atWsDj77cz58/PxUrVmTx4sXUqlXL4n7t27cnKiqKJUuWMG7cOKysrChTpgyzZ882X5O1bt2aH3/8kV69etGuXTvzvNlPEhcXR4UKFWjSpAn9+/fnzz//5PXXX2fevHnmB41XqVKFL774gpUrVzJv3jzKlSvHl19+SZcuXYiLiwMeFdE/+ugjvv/+e44cOZLiSO5evXqRI0cOVqxYwZQpU8iVKxd16tRh8ODBSaZO+TfSc939JLNnz2bNmjVs3ryZlStXEhYWRs6cOSlfvjxfffUVTZo0sbhvr169+OWXXxg4cCAbNmz418ezbt06rKysUn1+z/vvv8/48eO5fv06ZcqUYenSpcyYMYPWrVvzyiuvULt2bXx8fMwP8pw7dy5Tp07Fx8eHqKgoypQpw/z5883voU6dOvH777/Tp08f4uLi8PT0xMfHh08++STZ1CyPe+WVV/jqq6+YNGkSTZs25fXXX+eLL77A3d2d48ePM2jQIGbMmEGdOnWeGCM8+iKjUKFCVKxYMdn0kiLyfwwJT/M0MhERkSwmMDCQzp07s3z5cqpXr57R4WR7S5cuZd68efzvf/9L13yQmV3iCKvU5o0XEREREcnsTp8+TcuWLVm1apX5TgsRSU4j0UVEROSZ++OPPzh27BizZs1i0KBB2aqALiIiIiKS1d29e5crV64wfPhwGjZsqAK6yBOoiC4iIiLP3HvvvYetrS3du3dP17yYIiIiIiLy/A0fPpxffvmFevXqMW7cuIwORyTT03QuIiIiIiIiIiIiIiIWGJ+8iYiIiIiIiIiIiIjIy0lFdBERERERERERERERC1REFxERERERERERERGxQEV0ERERERERERERERELrDM6ABH5PwkJCdy79wCTSc/7zcqMRgP58jkol9mAcpl9KJfZh3KZfaQnlwUK5HpBUWVft2+Hv5B+9BnN2pS/rE35y9qUv6xN+cva0nKtqZHoIpmIwWDAaDRkdBjylIxGg3KZTSiX2YdymX0ol9mHcpk9Ka9Zm/KXtSl/WZvyl7Upf9mfiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigXVGByAi/ydqwFTsMzoIeSaiQLnMJpTL7EO5zD6Uy8whfFivjA5BREREROSFUBFdREREREREREQki7Bfu+2F9RXZuskL60skM9N0LiIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIWZPic6Ddu3KBRo0ZUqVIlyXIPDw/69u37TPvavHkzjRs3xtraGm9vbxYvXoytre1TtXn37l3Gjh3LvXv3AIiOjqZ///7UrFnT4j6BgYFMnz6d77//PsX1Z8+eZc2aNYwZM+apYuvfvz937twhLCyM4OBg3njjDQA+/vhj3nnnnadq+98aOnQox48fp2DBguZlJUuWxNfX94XGkVreIiMj2bdvH40aNXqhMYmIiIiIiIiIiEjmk+FFdABHR0f8/f2fez9+fn40bNgQa2vrZ9bfjBkzcHV1pXv37gCcOXOGUaNGUaNGDYzGfzfQ38XF5akL6AAzZ84E/q9o/yLO8T8lJCRgMBiSLPv4449p3759hsaQWt7OnDnDzp07VUQXERERERERERGRzFFET83evXuZPn06xYoVo3z58vzwww/s27ePoUOHUqlSJXMxtlatWqxZs4b8+fMzbNgwbt26RUxMDFWrVmXYsGHMnj2ba9eu0bVrV/z8/PD09CQoKAiTycTo0aMJDg4mPj4eLy8vPv/8cw4dOsQ333xD0aJFOXfuHLGxsXz99dcUKFAgSXzh4eFERESYX1eoUIH169cDEBkZmWLbif73v/+xZMkSli9fDjwq5Pr4+DBq1CjzSPUOHTpQt25dfv31V65cuUKHDh3o0qUL9+7do3///kRHR/PGG29w4cIFevfujaen5xPPab169Vi/fj158+ZlyJAhREdHM2vWLB48eMB7773H//73PxYuXMiuXbuwtbWlUKFCjBs3jpw5cyZp59SpU0yaNAmj0UhcXBxDhgyhSpUqDB06FFtbW27cuMGYMWMoXrx4mnL9888/M2fOHKytrbGysmLMmDFs2LCB3Llz06VLFw4dOkS3bt0IDAwkZ86cfPHFF7Rq1YpXX32VyZMnA2AymfDx8cHNzQ1vb2+cnZ25ePEiCxYsIEeOHE/MW1RUFCNGjCA0NJTJkyczZMgQpk6dSlBQEAkJCVSoUIERI0bwyy+/MGvWLIoWLcrNmzcxGo3MmjWLV199lVGjRnHp0iViY2NxdnZm3LhxWFtn+o+aiIiIiIiIiIiIpCDTz4k+duxYpk+fbi6CWllZpbr9nTt38PDwYNWqVaxdu5a9e/dy4cIF89QwS5YsIV++fObt/f39sbGxYfXq1axcuZLdu3fz66+/YjQaOXbsGL1792bNmjWULFmSHTt2JOuvZ8+ebNmyhcaNG+Pr68uPP/5IfHx8qm0nql27NpcvX+bu3bsAbN26laZNmyZp38rKirt377Jw4UIWLlzIggULAPj2228pW7Ys3333Hc2aNePYsWNpHvnu6elpjuPmzZvm/o8ePUqNGjU4efIkmzZtYtWqVaxatcrinQI+Pj4MGDCAFStWMHz4cIYMGWJeFxYWxpIlS9JcQI+KimLs2LHMmTOH5cuX8/HHHzNp0iRq1arFkSNHAPjll1+oXbu2OfagoCA8PDwYMmQIY8eOZfny5YwdO5YRI0aY27Wzs2PZsmVJCuhgOW85cuSgR48e1KhRg6FDh7Jjxw7u3LnDypUrWbVqFXfv3mX37t0YjUbzlx7fffcdbm5uLF++nPPnz3PkyBHWrFlDQEAAHh4ehISEpOkciIiIiGQl1tbGp/qxsnp07Wpl9eRtRUREREQyUqYYHhsaGoq3t3eSZb1796Z8+fJERERQvnx54FHR2dI84ony5MnDmTNnaN++PdbW1ty9ezfVIuaJEydo1qwZADY2NlSrVo1Tp07h4uJCyZIlKVSoEABFihQhNDQ02f4VKlRg165dHDt2zDxtyty5c1m9enWqbQNYW1vTsGFDdu3aRfv27dm5cycrVqzg2rVrSfqoUaMGAE5OTty/fx+A8+fP07JlSwDc3NwoUqRIquflcYmF6dKlS1O0aFGsra25dOkShw8fpnbt2hw/fpzq1aub54v39PRk48aNSdoIDw/nr7/+olq1auYYbt++TVhYGECyOe4ft2zZMrZt22Z+7eHhQYMGDbh9+zb9+vUDHo0oj4uLo3Llyly4cIGEhASOHz9Or169OHjwIEWLFqVkyZLExMRw5coVvvzyS3N7UVFRxMbGphpHanl73LFjxwgKCjK/PyMiIrhx4waOjo5J3h9Vq1Zl/fr1lCxZkly5cvHJJ59Qr149GjZsmOzuBREREZHsIG9eh2fSTu7c9s+kHRERERGR5yVTFNEtjXROfOhjIpPJZP79n3Ncx8XFAY8KtGFhYaxcuRKj0cj777//xP4TEhKS/J44ovufU3A8vl2iyMhI7O3t8fDwwMPDg549e9K4cWN+++23VNtO9MEHHzBz5kxcXFwoWrQohQsXTlZET2kqkH/O852e+ddr1qzJ0qVLef311/Hw8MDKyorDhw9z9OhRPvnkEzZt2vTEuFM6F4/HkdoDW1OaE/3cuXMULFgwxfdBuXLlOHHiBEajkapVqzJv3jycnJyoU6cORqMRo9Focb53S3E8KW+JDAYDrVq1okePHkmWBwYGJjkHJpMJg8GAvb0969ev59SpU/zvf/+jefPmLF++nNKlS1s8HyIiIiJZUUjIg6fa38rKSO7c9oSFRRIfb0p122dVsM8IBw4cYMiQIVSvXt38zCKAefPmMX/+/CTbJiQkULBgQfbu3ZusnSFDhrB58+Ykd+aWLFmSTZs2Pb/gRURERATI5NO55M2bF3t7e37//XcA9u3bZ17n6Ohonobk6tWr5hHa9+/fp1SpUhiNRo4ePcqff/5JTEwM8Kggmvh7Ind3d/bv3w9ATEwMhw8fpnLlymmKLz4+nkaNGvHzzz+bl4WGhhIVFYWTk1Oa2q5SpQp///0369evN49aT4tSpUpx/Phx4NFc6jdv3kzzvnny5MHKyoq9e/dSvXp1qlWrxr59+zAYDOTLlw93d3cCAwPN5+rAgQPJ4s6dOzdOTk4cPXoUeDQVjJOTU7J509Pq9ddfJywsjHPnzgFw6NAhVq5cCTwaOf/1119TtWpVbG1tsba2Zv/+/dSuXZucOXNSokQJ8x8aFy9e5D//+U+qfT0pb0aj0XzsVatWZc+ePeaR7bNmzeLSpUsAXLlyhb/++guA48ePU758eU6dOsUPP/yAm5sbffv2pXz58pw/f/5fnRMRERGRzCwuzvRUP4mF8/j4J2+bVS1atIjx48dTokSJZOt69+7NqVOnkvy88847fPDBBym2FRYWRt++fZNsrwK6iIiIyIuRKUaipzSdS968eZk9ezYjR46kX79+5M+fH1dXV/P6Vq1a4ePjQ3BwMIULF+b1118nISGB1q1bM2jQIA4fPoyHhwfdunVj8uTJvPHGG3h5edGhQwdmz55tbqdjx46MHj2a9u3bExcXR4sWLXBzcyMwMPCJcVtZWbFgwQKmTp2Kn58ftra2xMfHM3bsWAoVKpTmths3bsy3336bZE7xJ/noo4/o27cvHTp0oEKFCjg7Oz9xvvjH1ahRg82bN1O0aFEALly4YJ6P3c3NjebNm9OxY0esra0pUaIEnTp1StbGlClTmDhxIkajkYSEBKZMmZLm/v8pR44cTJ8+nVGjRmFnZ4fJZMLX1xeAOnXqMG7cOLp06QJApUqV2LhxI2XKlDHHMX78eBYvXkxsbCw+Pj6p9vWkvLm6uvLVV18xbNgwJk6cyMmTJ2nfvj0GgwEXFxdKlCjBnTt3KFOmDNOnTyc4OBgbGxv+85//YGNjw/z581m7di0Gg4FChQrx9ttv/+vzIiIiIiJZl52dHevWrWPChAlER0enuu3+/fs5deoUkydPTnF9WFgYjo6OzyNMEREREXkCQ4KleTkyoUuXLtG9e/cUb2982Vy+fJng4GDq1q1LREQEDRs2ZOPGjZp/+wVJnEf9SXP0p1fUgKnPtD0RERGR5yV8WK+n2t/a2kjevA6EhDx44mjzAgVyPVVfGW3o0KFER0cnmc7lcfHx8TRt2pTevXvz3nvvpbjNBx98QKFChbh69SqhoaG4ubkxatQoXn/99TTFcPduBEaj4ckbPqX0TNMjmY/yl7Upf1lbevJn+93WFxQVxLRL+f9LkpQ+f1lbWqYOzBQj0SX9cuXKxbJly5g3bx6xsbF89tlnKqCLiIiIiGRBW7duxWg00qRJE4vbvPbaa+TPn5+JEydiY2PDuHHj6N69O1u3bk31eUSJ8uVzSPZcqedJD4zN2pS/rE35y9rSkr8Ym7TPRPC0HLLwc0kygj5/2VeWKqKXLl1ao9D/vwIFCrB06dKMDuOlVb169Wc+Cl1EREREXk7Lli2jY8eOqRa5FyxYkOS1r68v1atX58iRI9SqVeuJfdy790Aj0eWJlL+sTfnL2tI1Ej02/gVFBQ+e8kHiLwt9/rI2jUQXERERERHJxIKDgzl37hyNGjVK1345c+bE0dGR27dvp2l7kykBk+nFzeSZ+MBYyZqUv6xN+cva0pI/mxc4M7PeS+mjz1/2ZczoAERERERERF5W+/fvp3z58uTJk8fiNhEREfj6+nLr1i3zspCQEEJCQihWrNgLiFJERETk5aYiuoiIiIiISAY5e/YsZcuWTbY8KCiIRo0aERMTQ86cOTlx4gQTJkwgNDSU0NBQxo4di4uLC1WqVMmAqEVEREReLprORSQTyTHDh5CQB7r1J4uztjaSN6+DcpkNKJfZh3KZfSiXkpW4uroCEBcXB8B///tfAE6dOmXe5vbt27z++uvJ9o2MjOTKlSsk/P9b9ufMmcPEiRNp0KAB1tbWeHh4MH/+fIxGjYsSERERed5URBcREREREXkOHi+WW/L111+nuLx69eqcP3/e/NrJyYk5c+Y8s9hEREREJO00bEFERERERERERERExAIV0UVERERERERERERELNB0LiKZSNSAqdhndBDyTESBcplNKJfZh3L5fIQP65XRIYiIiIiIiDxXGokuIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigXV6Nn748CHTp0/nyJEj5MqVi+joaNq2bUvr1q2fV3zp1r9/fwYPHoyTk9NzaT8sLIwOHTrg5OTEwoULzctv3LhBo0aNqFKlSpLtBwwYkGxZeh07doxXX32VEiVKMGHCBD744APc3Nyeqk0/Pz/Wr19PsWLFePjwIXZ2dnTp0oUGDRr8q/a8vb1ZvHgxtra2TxWXs7MzQUFB2NnZpanP+/fvkydPHkwmEzExMfTr14/atWs/VQz/xq1bt7h06RI1a9Z84X2LiIiIiIiIiIjI85OuIvqoUaMoUKAAGzZswGg0cvfuXTp37kzevHmpX7/+84oxGZPJhNGY8iD6mTNnPte+L1y4gK2tbZICeiJHR0f8/f1T3T+12C0JCAigYcOGlChRghEjRqRr39S8//77DBo0CIDr16/Tq1cvYmNjadKkSbrbetJxPy+DBw/Gy8sLgEuXLtG+fXt++uknbGxsnrjvv8mFJYGBgVy4cEFFdBERERERERERkWwmzUX069evc/jwYfbs2WMuPL766qusWrWK3LlzA7Bw4UJ27dqFra0thQoVYty4cdjb21OtWjX69evH3r17uXXrFr6+vkRGRrJkyRKWL18OwJkzZ/Dx8WHLli0sXbqUnTt3YmNjQ+HChfH19eXu3bt8+umnuLm5UahQId577z1GjRqFtbU1ERER9OjRgyZNmlCvXj0WLVpEyZIlmTx5MidPnsRgMFC+fHlGjRrFzZs36dWrF/Xr1+fIkSOEhoYya9YsypYtm+R4r127xujRo4mLiyM6OpqePXvy5ptvMm7cOIKDg+nVqxfz589P07nz8/Pjxo0b3L59m969e2NjY8PEiROxtbXl4cOHDBw4kJo1a/Lw4UNGjRrFrVu3iIqK4vPPPyc2NpYdO3Zw8uRJrKysWLBgAd27d8fLyytd57t69eqpxli8eHGGDRvGlClTaNKkCcHBwYwdO5aYmBhiYmLo1asXdevW5e7du4wYMYIHDx4QExPD8OHDqVSpknkE+fz58wkJCSE8PJyTJ0/StWtXLl68yMmTJ3F0dOSbb77BaDQybtw4Tp8+jcFg4LXXXmPKlClYW//f2/Hw4cNMmzYNW1tbIiMj8fHxoUaNGqkeQ+nSpbGysuLevXs4Ojri6+tLcHAwsbGx1KtXjx49ehAQEMCPP/5IdHQ0LVu2xMPDI8Xj+fnnn5kzZw7W1tZYWVkxZswYSpQoQd26denatSv79+/n2rVr9O/fHzc3N2bNmoXJZCJPnjy4ubmlO3YRERERERERERHJnNJcRP/9999xdnZONl2Ho6MjACdPnmTTpk0EBARga2vLmDFj8Pf3p1evXjx8+JCiRYuyfPly1qxZw4oVK5g5cyYjR47k7t27vPrqq2zdupWmTZsSFBTE3r17WbVqFUajkWnTpvHdd9/RoEEDLl++zMyZMylXrhzjx4/Hy8uL3r17ExERwcaNG5PEtWPHDi5evMjq1asxGAx8+umnbN26FXd3dy5fvsy7775L//79mTFjBuvXr2fo0KFJ9h83bhwtWrSgWbNm/PXXX7Rs2ZL//ve/DB8+nOnTp6e5gJ7o6tWrrF69GqPRyJ49e/Dx8aFq1aocOXKESZMmERAQwIoVK8iXLx/Tp0/nypUrzJw5k9mzZ7N8+XK6d+9OrVq1WLBgwb86308qogNUqlSJixcvYjKZGDduHJ988gmenp7cu3ePDz/8kB07djB79mxq1KjBxx9/zOHDh9m0aROVKlUyt2FlZcXvv//OihUrOH78ON7e3mzevJnRo0fz7rvv8ttvv/H666/j5OTEyJEjMRqNdO7cmQMHDvD222+b21m6dCmdOnWiWbNm3Lx5k+PHjz8x/sDAQHLmzEnBggVZvHgxBQsWZOLEiZhMJjp06GAeJf7bb7+xY8cO7OzsGD16dLLjcXZ2ZuzYsaxevZp8+fKxb98+Jk2axIIFC7CysgJg8eLFHDx4kBkzZhAQEECLFi2Ijo6mW7du9OrVK92xi4iIZFXW1i/2ETtWVsYk/0rWpVyKiIiISFaR5iJ6bGwsJpPJ4vrjx49TvXp1c5Hd09MzSWE7cSRukSJFuH//PtbW1jRs2JBdu3bRvn17du7cyYoVK9ixYwfBwcF89NFHAERGRuLu7g5Azpw5KVeuHADvvPMOgwcP5q+//sLLy4u2bdsmi6d27drmUfM1atTg1KlTuLu7kzt3bipUqGCOJygoKNnxnDhxgmnTpgFQuHBhXn31Va5du5bqOQoNDcXb2zvJsokTJwKPCtSJseTJk4d58+YRHR1NVFQU9+/fN/fZokULAEqWLMns2bMt9pXe850WBoMBa2trjEYjx44d48GDB8ybNw8AGxsbbt++zYkTJ8zn+s033+TNN99M1k7isRYqVIhcuXJRunRpAAoWLEhYWBgODg7cuXOHjh07Ym1tzcWLFwkJCUnSxrvvvsuMGTM4ffo0b731Fu+9916KMU+bNo1FixaRkJBAnjx5WLBgAQaDgWPHjhEcHMyvv/4KwIMHD7h+/ToAFSpUMM+5ntLxnD17ltu3b9OvXz/g0bQvcXFxaT63aY1dREQkO8ib1yFD+s2d2z5D+pVnT7kUERERkcwuzUV0FxcXzpw5Q1RUFDly5DAvDw4ONr9OSEgwL09ISEgy33RKc1R/8MEHzJw5ExcXF4oWLUrhwoUxGAx4eXnh6+ubZNsbN24kGQXv6enJjh07OHToEAEBAaxcuZKlS5cm2cdSPI9PG/LP7VLb32AwpLhdotTmRH88dh8fH4YPH84777zDqVOnzMXahISEVL+oeFJ8TzrfT3LkyBFcXV2BRwX1WbNmUaBAgWR9PinGxNHakPK53rJlCydOnGD58uXY2try6aefJmujefPmeHl58dNPPzFv3jy2bdvG+PHjk233+JzojzMYDPTs2TNZATtx5H5qx2MwGChYsKDFXP7zmP5t7CIiItlBSMiDF9qflZWR3LntCQuLJD4+7ddNkvmkJ5cZ9WWNiIiIiAhAmu+dLFGiBB4eHkyYMME8Kvfu3bv06dOH48eP4+7uTmBgIDExMQAcOHCAypUrp9pmlSpV+Pvvv1m/fj3NmjUDwN3dnYMHDxIREQHAt99+y9GjR5Pt6+/vz+3bt2nQoAFjx47l2LFjSYrKie2YTCZMJhM//fTTE+N5nLu7O/v37wcefVFw//59Xn/99TTvn5r79++b52DfsmWL+ZxVqVKFgwcPAvD333/Trl074FFRN3Gbx+NL7/lOzR9//MHUqVPp1asXAFWrVmXHjh0A3Llzx/ylxuMx/vbbb/Tp0yfdfd2/f59ixYpha2vLpUuXOH36dLLjmz17NgaDgQ8++IBBgwaZR5SnVdWqVdm5cyfwaCS5r68v9+7dS7ZdSsfz+uuvExYWxrlz5wA4dOgQK1euTLW/x3P0tLGLiIhkJXFxphf6k1hsjY9/sf3qJ2NzKSIiIiKSkdI8Eh0eTZ0xa9YsWrRoQe7cuTGZTPTs2ZOGDRsCj0bgJk7RUaJECTp16vTENhs3bsy3337LkCFDAHB1daVz58507twZW1tbChYsSKtWrZJNm1GmTBl8fHzIkSMHUVFRjBw5MslI8XfffZfjx4/Tvn174FGxtFGjRty8eTNNxzpy5Ei+/PJL1q1bR2xsLJMnT04yAv9p9OrVi169epEvXz569+7NwYMHmTx5Mn379mXUqFF06NCBmJgYPv/8cwBq167NxIkTeXxKETc3t391vh+3ZcsWTp48SXx8PCaTicGDB1OnTp0kx79jxw6ioqLo0aMHAH379mXEiBF06tSJuLg4hg8fnu7jb9y4MZs2baJ9+/aULVuW/v37M2/ePDw8PMzblC5dmm7duvHKK68QHR3NoEGD0tVHx44duXjxIm3atCE+Pp46deqQL1++ZNuldDw5cuRg+vTpjBo1Cjs7O3MRPjUeHh70798fe3t7ypUr91Sxi4iIiIiIiIiISOZhSLA0l4mIvHBRA6ZmdAgiIiLpEj6s1wvtz9raSN68DoSEPNAI5SwuPbksUCDXC4oq+7p9O/yF9KPPaNam/GVtyl/Wlp782a/d9oKigsjWTV5YX1mZPn9ZW1quNdM8nYuIiIiIiIiIiIiIyMtGRXQREREREREREREREQtURBcRERERERERERERsUBFdBERERERERERERERC6wzOgAR+T85ZvjoIRTZgB4okn0ol9mHcikiIiIiIiL/lkaii4iIiIiIiIiIiIhYoCK6iIiIiIiIiIiIiIgFKqKLiIiIiIiIiIiIiFigIrqIiIiIiIiIiIiIiAV6sKhIJhI1YCr2GR2EPBNRoFxmEy9zLsOH9croEEREREREREQynEaii4iIiIiIiIiIiIhYoCK6iIiIiIiIiIiIiIgFKqKLiIiIiIiIiIiIiFigIrqIiIiIiIiIiIiIiAXZuoh+7949KlasiJ+fn8VtLl26RL169VJcV6tWLW7cuMH+/fuZM2cOAMeOHePatWsATJgwgaCgoGcfeDqtXr2aoUOHJlseGRnJ8OHDadeuHd7e3rRs2ZItW7ak2taNGzeoVatWiuseP/antXnzZuLi4gDw9vYmJibmqdscOnQoq1evTnWbW7du8fPPPz91XyIiIiIiIiIiIvJyyNZF9A0bNlCyZEkCAgIwmUz/uh0vLy8+//xzAAICAsyF5BEjRuDm5vZMYn0eli5diq2tLd999x3+/v7MnTuXBQsWEBER8a/ae/zYEyUkJPyrtvz8/IiPjwfA398fW1vbf9VOegUGBj7XIvrTvM9EREREREREREQk87HO6ACep3Xr1jFkyBAmTpzIoUOHzCOsf/zxR6ZPn07RokUpVaqUeftr164xePBgHBwcKF++vHl5QEAABw4coEmTJuzYsYOTJ09iZWXFggUL6N69O15eXixcuJBdu3Zha2tLoUKFGDduHPb29lSrVo1+/fqxd+9ebt26ha+vL9WrV08S5/Xr1xk+fDgGg4EHDx7w8ccf07RpU9auXUtgYCAAly9fxsHBgUWLFpEjRw78/PzYvXs3xYsXJ1euXCkef3h4OA8ePCAhIQGDwUCRIkXMI9ETj2nmzJkA9O/fnzp16vDmm28CMGXKFM6dO0doaCjTpk3j8uXLyY7d2dmZixcvsmDBAjZv3syaNWuwt7cnISGBqVOn4uTkxNWrVxkzZgzx8fEkJCQwYcIENm7cyLVr1+jatSt+fn54enoSFBSEyWRi9OjRBAcHEx8fb/7y4tChQ3zzzTcULVqUc+fOERsby9dff02BAgUs5r5u3bp07dqV/fv3c+3aNfr374+bmxuzZs3CZDKRJ08eOnXqhK+vL8HBwcTGxlKvXj169OhBVFQUPj4+/Pnnn5QoUQKTyUTNmjX58MMP2bJlCytWrMDW1hYHBwfGjx/Pq6++SuXKlenUqRNhYWF8/vnnDBo0CJPJRHh4OC1btuSjjz5K79tXREREREREREREMoFsW0T/9ddfCQ8Pp3bt2rRq1Yp169aZi+hjxoxh0aJFlCtXjoULF5r38fPzo2nTpnTq1IkTJ06wZMmSJG02aNCA5cuX0717d2rVqsWCBQsAOHnyJJs2bSIgIABbW1vGjBmDv78/vXr14uHDhxQtWpTly5ezZs0aVqxYkayIfuvWLbp168Zbb73FH3/8QcuWLWnatClWVlYcPXqUrVu34uDgQLt27fj5558pVaoU69evZ+fOndja2tKjRw9effXVZOfA29ubnj178s477+Dp6UmtWrWoX7/+E0d937lzh/r16zNkyBD8/f2ZM2cOM2fOTHbsdnZ2LFu2DHg0dczcuXMpVKgQc+bMwd/fnyFDhjB+/Hg6depE/fr12bp1K9u3b6dv377MnTuXJUuWYGdnZ+7X398fGxsbVq9eTWxsLB9++CGenp4YjUaOHTvGxIkTKVSoEAMHDmTHjh14e3tbPAYrKysAFi9ezMGDB5kxYwYBAQG0aNGC6OhounXrxjfffEPBggWZOHEiJpOJDh06ULNmTc6ePUtsbCxr167l5s2bNG3alNq1a3Pr1i0WLFjAunXryJEjB6tWrWL+/PmMHDmSyMhIPDw8qFu3LsuWLaNYsWJMmDCB2NhY/P39Uz3fIiKZlbV19rlhzcrKmORfybqUy+xDuRQRERGRrCLbFtHXrVtHixYtsLKyomXLljRo0ID79++TkJBAREQE5cqVAx7Ne/7dd98BcP78ebp27QpA5cqVcXBwSFNfx48fp3r16ubitKenJxs3bjSvr1GjBgBFihTh/v37yfbPmzcvixcvZtGiRQCEhISY17m6uprjKFKkCKGhoZw/fx4XFxdzAbpmzZqcP38+WbtOTk5s3LiRU6dO8csvv/Dtt98yY8YM1q1bl+rxvPLKK1StWhUAd3d3vv/++xS3q1KlSpJ9BgwYgMFg4Pbt2+Z1J06cYPr06QC89957qfZ74sQJmjVrBoCNjQ3VqlXj1KlTuLi4ULJkSQoVKpTkPDzJk877sWPHCA4O5tdffwXgwYMHXL9+nXPnzplH5L/22mtUrFgRgFOnTnH37l26d+8OQGxsLAULFjS35+7uDjzKx5IlS/Dx8cHLy4sOHTo8MVYRkcwob960/X8wK8md2z6jQ5BnRLnMPpRLEREREcnssmURPSIigh07dlC4cGH27dsHgNFoZPPmzckKuYnzckPy+b3TM7/14/smJCRgNP7fiBobG5tU9/X19aV+/fp07tyZe/fu4enpaV5nbZ00RQkJCcnifPwYHhcdHY2trS1ubm64ubnRo0cPvL29OXjwIAaDwWIbj69Lbc7zxC8Nbty4weTJk9myZQuFCxdm8eLF/P777+btnsV5TOk8PMk/9/kng8FAz549k70njh49muQcPJ7LChUqsHjx4hTbSzwf5cqVY/fu3QQGBrJ9+3bmz5/Phg0bnvg+EBHJbEJCHmR0CM+MlZWR3LntCQuLJD5ez6/IypTL7CM9ucyOX+qJiIiISNaRLe+d3LJlC1WrVmX79u1s3LiRjRs38tVXX7F+/Xry5s2Lvb29uci7f/9+835lypTh+PHjABw5coTIyMhkbRsMBmJiYpIsc3d3JzAw0Lz8wIEDVK5cOc3x3r9/n7JlywKwceNGjEZjsj4eV7ZsWc6ePUtMTAwmk4mDBw+muF3nzp2TjDp/+PAh9+7do3jx4jg6OnL37l3g0Yjq06dPm7d78OABQUFBwKNpcRLnh0/p2AHCwsJwcHCgcOHCPHjwgD179pi3q1y5sjm+PXv2MH78eIttubu7m/MRExPD4cOH03Ue0+LxfqtWrcrOnTuBR4V+X19f7t27R6lSpczvg7/++ovffvsNeHRXwG+//cbff/8NPHqfJe7/uC1btnD69Gm8vLzw9fXlzz//TNPIeRGRzCYuzpRtfhILdPHxGR+LfpRL/aQ/lyIiIiIiGSlbjkRft24dvXv3TrKsbt26jBs3jjNnzjBy5Ei++OIL81QdiSOl+/Tpw+DBg9m9ezdlypShePHiyUY8165dm4kTJxIXF2de5ubmRvPmzenYsSPW1taUKFGCTp06pTne7t27M3bsWAoUKGCel3vQoEG89dZbKW5funRp3n//fT788EOcnJwoU6YMYWFhybabPn06EydOZN26ddjZ2REbG0vXrl1xc3MjKiqKpUuX0qdPH1599VVcXFwwmUyYTCacnJzYvn07M2fOJDw8nK+++srisQO4uLhQqVIlWrVqRf78+c3ncdu2bYwcOZLRo0ezZs0aACZMmABgnuZk9uzZ5nY6duzI6NGjad++PXFxcbRo0QI3Nzfzw1WfBQ8PD/r374+9vT29e/fm4sWLtGnThvj4eOrUqUO+fPlo3rw5e/fupXXr1pQpUwZ3d3esrKwoVKgQo0aNomfPntjb22NnZ8ekSZOS9VGmTBnGjBmDjY0NUVFRfPLJJ+TPn/+ZHYOIiIiIiIiIiIi8OIaEtMyLIfISuXXrFr/++itNmjQhLi6Opk2bMnnyZNzc3J5731EDpj73PkRE0ip8WK+MDuGZsbY2kjevAyEhDzSqNYtTLrOP9OSyQIFcLyiq7Ov27fAX0o8+o1mb8pe1KX9ZW3ryZ7922wuKCiJbN3lhfWVl+vxlbWm51syWI9FFnkauXLnYvn07S5YsISEhgQYNGryQArqIiIiIiIiIiIhkPiqii/zDK6+8gp+fX0aHISIiIiIiIiIiIplAtnywqIiIiIiIiIiIiIjIs6AiuoiIiIiIiIiIiIiIBSqii4iIiIiIiIiIiIhYoDnRRTKRHDN89CTnbEBP5c4+lEsRERERERER0Uh0ERERERERERERERELVEQXEREREREREREREbFARXQREREREREREREREQs0J7pIJhI1YCr2GR2EPBNRoFxmExmZy/BhvTKoZxERERERERFJpJHoIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFmSLIvq9e/eoWLEifn5+Fre5dOkS9erVS3FdrVq1uHHjBvv372fOnDkAHDt2jGvXrgEwYcIEgoKCnn3g6bR69WqGDh2abHlkZCTDhw+nXbt2eHt707JlS7Zs2ZJqWzdu3KBWrVoprnv82J/W5s2biYuLA8Db25uYmJinbtPb25sPPvgAb29v88/s2bOfut3IyEh27NgBkOS9ICIiIiIiIiIiIi8v64wO4FnYsGEDJUuWJCAggM8++wyj8d99N+Dl5YWXlxcAAQEBNGzYkBIlSjBixIhnGe4zt3TpUmxtbfnuu+8A+PPPP+nevTtvvfUWOXPmTHd7jx97ooSEBAwGQ7rb8vPzo2HDhlhbW+Pv75/u/S0ZPHiwOVcpMZlM6X4fnDlzhp07d9KoUaMk7wURERERERERERF5eWWLIvq6desYMmQIEydO5NChQ+YR1j/++CPTp0+naNGilCpVyrz9tWvXGDx4MA4ODpQvX968PCAggAMHDtCkSRN27NjByZMnsbKyYsGCBXTv3h0vLy8WLlzIrl27sLW1pVChQowbNw57e3uqVatGv3792Lt3L7du3cLX15fq1asnifP69esMHz4cg8HAgwcP+Pjjj2natClr164lMDAQgMuXL+Pg4MCiRYvIkSMHfn5+7N69m+LFi5MrV64Ujz88PJwHDx6YC91FihQxj0RPPKaZM2cC0L9/f+rUqcObb74JwJQpUzh37hyhoaFMmzaNy5cvJzt2Z2dnLl68yIIFC9i8eTNr1qzB3t6ehIQEpk6dipOTE1evXmXMmDHEx8eTkJDAhAkT2LhxI9euXaNr1674+fnh6elJUFAQJpOJ0aNHExwcTHx8PF5eXnz++eccOnSIb775hqJFi3Lu3DliY2P5+uuvKVCgQJreBzdu3ODTTz/Fzc2NQoUK0bNnT4YNG8atW7eIiYmhatWqDBs2DID169ezZs0aAFxdXRk4cCAjRowgNDSUyZMnU65cOfN5O3XqFJMmTcJoNBIXF8eQIUOoUqUKgwcPxsnJifPnz3P16lXq1q3LsGHD+P333xk1ahTW1tZERETQo0cPmjRpkqZjEBEREZHs5cCBAwwZMoTq1aubr8kBgoODqV+/Pra2tkm2nzp1Ko0bN07WTnR0NBMmTGDHjh3ExsZSp04dxowZQ758+Z77MYiIiIi87LJ8Ef3XX38lPDyc2rVr06pVK9atW2cuoo8ZM4ZFixZRrlw5Fi5caN7Hz8+Ppk2b0qlTJ06cOMGSJUuStNmgQQOWL19O9+7dqVWrFgsWLADg5MmTbNq0iYCAAGxtbRkzZgz+/v706tWLhw8fUrRoUZYvX86aNWtYsWJFsiL6rVu36NatG2+99RZ//PEHLVu2pGnTplhZWXH06FG2bt2Kg4MD7dq14+eff6ZUqVKsX7+enTt3YmtrS48ePXj11VeTnQNvb2969uzJO++8g6enJ7Vq1Urxgvyf7ty5Q/369RkyZAj+/v7MmTOHmTNnJjt2Ozs7li1bBjya8mTu3LkUKlSIOXPm4O/vz5AhQxg/fjydOnWifv36bN26le3bt9O3b1/mzp3LkiVLsLOzM/fr7++PjY0Nq1evJjY2lg8//BBPT0+MRiPHjh1j4sSJFCpUiIEDB7Jjxw68vb3T/H64fPkyM2fOpFy5cty4cQMPDw86dOhAQkICDRs2pFWrVuTKlYu5c+eydetW7O3t8fHx4erVq/To0YMDBw4wdOhQAgICzG36+Pgwbtw4qlWrRlBQEIMGDWLXrl1YWVlx6dIl5s+fz4MHD6hZsyZ9+vRhzZo1eHl50bt3byIiIti4cWOa4xcReZy1dbaYdS1TsLIyJvlXsi7lMvt4GXK5aNEi1q1bl+QOz0Th4eHY2Nhw6tSpNLU1bdo0jh07xvr168mZMyfDhg1j+PDh5r9VREREROT5yfJF9HXr1tGiRQusrKxo2bIlDRo04P79+yQkJBAREUG5cuWAR/OeJ053cv78ebp27QpA5cqVcXBwSFNfx48fp3r16ubitKenZ5ICaY0aNQAoUqQI9+/fT7Z/3rx5Wbx4MYsWLQIgJCTEvM7V1dUcR5EiRQgNDeX8+fO4uLiYC9A1a9bk/Pnzydp1cnJi48aNnDp1il9++YVvv/2WGTNmsG7dulSP55VXXqFq1aoAuLu78/3336e4XZUqVZLsM2DAAAwGA7dv3zavO3HiBNOnTwfgvffeS7XfEydO0KxZMwBsbGyoVq0ap06dwsXFhZIlS1KoUKEk5yEl06ZNM59HgMaNG+Pl5UXOnDnNOc+TJw9nzpyhffv2WFtbc/fuXUJCQrh+/TrOzs7Y29sDj0b7AJw7dy5ZP+Hh4fz1119Uq1YNADc3N27fvk1YWBgA1atXx2AwkDNnTnLlykVoaCjvvPMOgwcP5q+//sLLy4u2bdumej5ERCzJmzdt/3+StMud2z6jQ5BnRLnMPrJzLu3s7Fi3bh0TJkwgOjo6ybrQ0FAcHR3T1E5cXBw//PADU6ZMoVixYsCjgR6NGzfm1q1b5utnEREREXk+snQRPSIigh07dlC4cGH27dsHgNFoZPPmzckKufHx8ebfExISkqwzmUxp7vPxfRMSEpLMu21jY5Pqvr6+vtSvX5/OnTtz7949PD09zeusrZOmIiEhIVmcjx/D46Kjo7G1tcXNzQ03Nzd69OiBt7c3Bw8eTDaP+eNtPL7un309LvFLgxs3bjB58mS2bNlC4cKFWbx4Mb///rt5u2dxHlM6DylJaU70GzduJBl9v2zZMsLCwli5ciVGo5H333/f3GZqx5uW/lOL19PTkx07dnDo0CECAgJYuXIlS5cuTVN/IiKPCwl5kNEhZBtWVkZy57YnLCyS+Pi0//9KMh/lMvtITy6z6peKnTt3trguLCwMk8lEjx49OH78OHnz5qVNmzZ88sknya7hr1+/TkREBG+88YZ5WalSpbC3t+f06dNpKqIbjQaMxvQ/4yi9XoY7DLIz5S9rU/6ytvTk7988s+7f0t2xaaPPX/aXpYvoW7ZsoWrVqnzzzTfmZT/++CP/+c9/6NSpE/b29vz++++ULVuW/fv3m7cpU6YMx48fp0KFChw5coTIyMhkbRsMBmJiYpIsc3d3Z+3atcTExGBra8uBAweoXLlymuO9f/8+ZcuWBWDjxo0YjcZkfTyubNmynD17lpiYGKytrTl48CCFCxdOtl3nzp358MMPad26NQAPHz7k3r17FC9enDt37nD37l0AYmNjOX36NG+99RYADx48ICgoCDc3N3799Vfz/PApHTs8utB3cHCgcOHCPHjwgD179pjjqVy5MgcPHuSDDz5gz549HDp0iJEjR5rbenw6F3d3d/bv30+jRo2IiYnh8OHDNG/ePMU8PI379+9TqlQpjEYjR48e5c8//yQmJoaKFSsyduxYwsPDyZUrF0OGDKFVq1Yp5iN37tw4OTlx9OhRqlWrxtGjR3Fyckr1ga3+/v7Url2bBg0aULlyZerXr/+vH8wqIi+3uDgVCJ+1+HiTzms2oVxmHy9rLm1sbChevDjt2rVj9uzZ/Prrr/Tt2xdHR0fzdX2ixDtY/zlyPXfu3Ny7dy9N/eXL5/BCr0ez8x0GLwPlL2tT/rK2tOQvxsbqBUTyiEMW/SI7o+jzl31l6SL6unXr6N27d5JldevWZdy4cZw5c4aRI0fyxRdf8Nprr1GxYkXzSOk+ffowePBgdu/eTZkyZShevHiyEce1a9dm4sSJxMXFmZe5ubnRvHlzOnbsiLW1NSVKlKBTp05pjrd79+6MHTuWAgUK0KFDB2rWrMmgQYPMRe1/Kl26NO+//z4ffvghTk5OlClTxjyNyOOmT5/OxIkTWbduHXZ2dsTGxtK1a1fc3NyIiopi6dKl9OnTh1dffRUXFxdMJhMmkwknJye2b9/OzJkzCQ8P56uvvrJ47AAuLi5UqlSJVq1akT9/fvN53LZtGyNHjmT06NHmh3VOmDABAC8vLzp06MDs2bPN7XTs2JHRo0fTvn174uLiaNGiBW5ubuaHqz4rrVu3ZtCgQRw+fBgPDw+6devG5MmT8ff3p3///nTp0gUrKyveeOMNPDw8uHz5Ml999RXDhg3Dw8PD3M6UKVOYOHEiRqORhIQEpkyZkmq/ZcqUwcfHhxw5chAVFWX+MkFEREREJFG9evWoV6+e+XWtWrVo27Yt69evT1ZET01arzPv3Xvwwkai626RrEv5y9qUv6wtPfmzjU15poLn4YHujk0Tff6ytrTc9WhISOu8FiLy3EUNmJrRIYhIJhI+rFdGh5BtWFsbyZvXgZCQBy/liNfsRLnMPtKTywIFcr2gqJ6PoUOHEh0dzcyZM1PdbtWqVSxZsoT//ve/SZZfvXqVd999lx9//BEnJyfg0TSClSpVYvbs2RYH5Tzu9u3wfx1/eugzmrUpf1mb8pe1pSd/9mu3vaCoILJ1kxfWV1amz1/WlpZrTU3UIyIiIiIi8oLt3LmTlStXJll2+fJl84NDH1esWDHy5MnD6dOnzcvOnz9vnqpQRERERJ4vFdFFREREREReMBsbG6ZOncpPP/1EXFwcP//8M+vXr6djx44ABAUFmZ8hZGVlRZs2bZg1axbBwcHcvXuXSZMm0ahRI/Lnz5/BRyIiIiKS/WXpOdFFREREREQyK1dXVwDzs4YSp2k5deoU9erVY/jw4fj6+nLr1i2KFi3KqFGjqF+/PgCRkZFcuXLF/OymPn368ODBA1q2bEl8fDxvv/02Y8aMefEHJSIiIvISUhFdRERERETkOTh16lSq69u2bUvbtm1TXFe9enXOnz9vfm1ra8uXX37Jl19++UxjFBEREZEnUxFdJBPJMcNHD6HIBvRAkexDuRQRERERERERzYkuIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFujBoiKZSNSAqdhndBDyTESBcplJhQ/rldEhiIiIiIiIiEgWopHoIiIiIiIiIiIiIiIWqIguIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhaoiC4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFlhndAAi/3Tjxg0aNWpElSpVAIiNjSV//vz4+vqSL1++Fx7Pvn37cHV1TbHvHTt2sGTJEgDi4uJwcXFh0KBB5M2bF4AvvviCy5cvM3/+fF577bUXGreIiIiIiIiIiIg8PY1El0zJ0dERf39//P39+e677yhdujSzZ89O074JCQnPNJZly5YREhKSbPmhQ4eYO3cus2fP5vvvvycgIAAXFxe6dOlCbGwsADt37mTVqlUqoIuIiIiIiIiIiGRRGokuWYK7uzvLli0D4MyZM0yePBkAk8mEj48Pbm5ueHt74+zszMWLF1mwYAGBgYHMnz8fo9FIwYIFmTx5MnZ2dkydOpWgoCASEhKoUKECI0aM4JdffuGbb76haNGinDt3jtjYWL7++mt2797N0aNHGTJkCBMmTMDZ2dkc09y5cxk0aBCFCxc2L+vUqRNbtmzhwIED7N69G5PJxKeffsrkyZMpVqzYCz1nIiIiIiIiIiIi8vRURJdMz2QysWvXLqpWrQrAkCFDmD17NiVLluTSpUt88cUXbN68GQA7OzuWLVtGZGQkI0eOJCAggAIFCjB16lQCAwN5+PAhd+7cYeXKlQD079+f3bt34+joyLFjx5g4cSKFChVi4MCB7NixA29vb7755humTJlC6dKlk8R14cIF3njjjWTxurq6cv78eSZNmkRAQABLlizBzs7uOZ8lEUkra+u034RlZWVM8q9kXcpl9qFcZh/KpYiIiIhkFSqiS6YUGhqKt7c3APHx8bi7u9OjRw/CwsK4cuUKX375pXnbqKgo8/QpifOoX716lbx581KgQAEAfHx8AJgwYQJBQUHmtiMiIrhx4waOjo6ULFmSQoUKAVCkSBFCQ0NTjTE2NhaTyZRseUJCAkaj/hgUyazy5nVI9z65c9s/h0gkIyiX2YdymX0olyIiIiKS2amILplS4pzo/xQTE4PRaExxHYCtrS3wqJCd0tzoBoOBVq1a0aNHjyTLAwMDsbZO+nF40tzq5cuX5+TJkzRo0CDJ8lOnTtGnT59U9xWRjBMS8iDN21pZGcmd256wsEji45N/aSZZh3KZfSiX2Ud6cvlvvgAVEREREXlWVESXLCVnzpyUKFGCvXv3Uq9ePS5evMjWrVvp169fku1KlizJvXv3uHnzJq+99hozZ86kTJkyVK1alSVLltClSxdsbGyYNWsWH3zwQap9GgwG80j3x3Xv3p1p06ZRoUIF84NDV6xYAUDNmjWf0RGLyLMWF5f+olt8vOlf7SeZj3KZfSiX2YdyKSIiIiKZnYrokuVMmTKF8ePHs3jxYmJjY81TtTzO3t6eiRMn8sUXX2BjY0PBggXp3bs3tra2nDx5kvbt22MwGHBxcaFEiRLcuXPHYn916tShX79+TJgwgWrVqpmX16tXj/j4eAYOHAg8GiXv4uLCokWLNJ2LiIiIiIiIiIhINmFIeNKcFSLywkQNmJrRIYhke+HDeqV5W2trI3nzOhAS8kCjJLM45TL7UC6zj/TkskCBXC8oquzr9u3wF9KPPqNZm/KXtSl/WVt68me/dtsLigoiWzd5YX1lZfr8ZW1pudbUcFkREREREREREREREQs0nYuIiIiIiIiIiIgk86JGvWvEu2R2GokuIiIiIiIiIiIiImKBiugiIiIiIiIiIiIiIhZoOheRTCTHDB89hCIb0ANFRERERERERESyD41EFxERERERERERERGxQEV0ERERERERERERERELVEQXEREREREREREREbFARXQREREREREREREREQv0YFGRTCRqwFTsMzoIeSaiQLl8wcKH9croEEREREREREQkG9JIdBERERERERERERERC1REFxERERERERERERGxQEV0ERERERERERERERELVEQXEREREREREREREbFADxaVTOvGjRs0atSIKlWqABAbG0v+/Pnx9fUlX758LySGW7ducenSJWrWrElAQACxsbG0bdv2hfQtIiIiIiIiIiIiGU9FdMnUHB0d8ff3N7+eOXMms2fPZsyYMQCYTCaMxudzQ4XJZCIwMJALFy5Qs2ZNWrZs+Vz6ERERERERERERkcxLRXTJUtzd3Vm2bBn16tXjvffe49KlS8ybN48NGzawYsUKcuTIwSuvvML48eMpWLAgdevWpXnz5hw9epSQkBD69evHu+++y7179xgxYgRhYWFER0fTpk0b2rRpQ0BAAD/++CPR0dG8+eabrFq1CpPJRJ48eYiMjCQ6OppBgwaxf/9+5syZg62tLQaDgTFjxlC6dGk6dOhA3bp1+fXXX7ly5QodOnSgS5cuGX3aRERERERERERE5F9SEV2yDJPJxK5du6hatSrXrl2jSJEiDBw4kD///JOvvvqKbdu2kTt3bhYtWsTs2bMZP348VlZWODo6snLlSs6fP88nn3zCu+++y3/+8x9cXV3p3bs3ERERNG7cmFq1agHw22+/sWPHDuzs7MyF827duuHn5wdAZGQkw4YN47vvvqNYsWJs27aN8ePHs3TpUqysrLh79y4LFy7kypUrtGvXTkV0kRfE2vrZ35ViZWVM8q9kXcpl9qFcZh/KpYiIiIhkFSqiS6YWGhqKt7c3APHx8bi7u9OjRw8CAgJwd3cH4PTp01SsWJHcuXMD4OnpyZYtW8xt1KhRAwBnZ2ciIyMJCQnhxIkTTJgwAYCcOXPyxhtvcPbsWQAqVKiAnZ2dxZiuXr1Kvnz5KFasmLm/L7/8Mll/Tk5O3L9//1mcBhFJg7x5HZ5b27lz2z+3tuXFUi6zD+Uy+1AuRURERCSzUxFdMrV/zon+OBsbG/PvCQkJSX5/fJ70x9c9Pof6P/cxGAwA2NraPjGu1PqzttbHSiQjhIQ8eOZtWlkZyZ3bnrCwSOLjTc+8fXlxlMvsQ7nMPtKTy+f5RamIiIiIyJOo2idZnqurK2PGjCE0NBRHR0cOHDhA5cqVzet//vln3njjDc6dO4ejoyOOjo64u7uzf/9+XF1dCQsL48yZM7zxxhv8/PPPSdo2GAzExMQkWVayZEnu379PcHAwxYoVS9afiGSMuLjnV0yLjzc91/blxVEusw/lMvtQLkVEREQks1MRXbK8QoUKMWjQID755BPs7OzIkycP48aNM6+/e/cuH330EXfv3mX06NEA9OnThxEjRtCxY0fzw0ILFy6crG0PDw/69++Pvb29eYR5jhw5mDRpEgMGDMDOzg5bW1vGjh37Yg5WREREREREREREXihDwuPzUohkM/Xq1WPRokWULl06o0NJk6gBUzM6BJEsK3xYr2feprW1kbx5HQgJeaBRklmccpl9KJfZR3pyWaBArhcUVfZ1+3b4C+lHn9GsTfnL2pS/rC09+bNfu+0FRfXiRLZuktEhPBV9/rK2tFxrGp+4hYiIiIiIiIiIiIjIS0rTuUi2tnfv3owOQURERERERERERLIwjUQXEREREREREREREbFARXQREREREREREREREQtURBcRERERERERERERsUBzootkIjlm+OhJztmAnsotIiIiIiIiIpJ9aCS6iIiIiIiIiIiIiIgFKqKLiIiIiIiIiIiIiFigIrqIiIiIiIiIiIiIiAWaE10kE4kaMBX7jA5CnokoUC6fUviwXhkdgoiIiIiIiIiIRqKLiIiIiIiIiIiIiFiiIrqIiIiIiIiIiIiIiAUqoouIiIiIiIiIiIiIWKAiuoiIiIiIiIiIiIiIBSqii4iIiIiIPCcHDhygZs2a9O/fP9m6wMBAWrduTZUqVXj77beZO3euxXaGDBlChQoVcHV1Nf80bdr0eYYuIiIiIv+fdUYHIBknJCSEqVOncubMGezt7TEYDHz88ce8++67T9x3//79LFq0CH9//zT3179/fwYPHoyTk9PThM3QoUM5fvw4BQsWNC8rWbIkvr6+T9UuwIYNG2jevDlnz55lzZo1jBkz5qnbFBEREZGX06JFi1i3bh0lSpRItu7PP/+kZ8+eDB06lFatWnHhwgU++ugjihYtSrNmzZJtHxYWRt++fenZs+eLCF1EREREHqMi+kusT58+NGzYkEmTJgFw69YtunfvTkJCAo0aNTJvl5CQgMFgeKq+EhISmDlz5lO18biPP/6Y9u3bW1xvMpkwGtN3o8WtW7dYtWoVzZs3x8XFRQV0EREREXkqdnZ2rFu3jgkTJhAdHZ1k3Z07d2jdujVt27YFoEKFCnh6enL06FGLRXRHR8cXEreIiIiIJKUi+kvql19+ITIyks6dO5uXFSpUCB8fH2bMmEGjRo3w9vbG2dmZixcvsmDBAjZu3Ii/vz8lSpSgUKFC5v2Cg4MZO3YsMTExxMTE0KtXL+rWrcvQoUOxtbXlxo0bjBkzho8//phFixZx7NgxAgMDAbh8+TIODg4sWrSIHDlyMGfOHHbu3EnhwoWpWLEiN2/eZOrUqWk+rsqVK9OpUyfCwsIYO3Ys48aN4/Tp0xgMBl577TWmTJmCtbU1+/btY/78+RiNRgoWLMjkyZMZOHAgFy9eZODAgbRp04bp06fz/fffc+3aNUaPHk1cXBzR0dH07NmTd955h1mzZhEREcGtW7e4evUqpUqVYtasWdy+fZtBgwZhMpkIDw+nZcuWfPTRR88ueSIiIiKSJTx+rf1PiVOyPO6PP/6gfPnyKW4fFhbGnj17WLx4MaGhobi5uTFq1Chef/31NMViNBowGp9uYExaWFkZk/wrWYvyl7Upf1lbevL3tAMdMyNr66z9vtXnL/tTEf0ldeHCBSpWrJhsuZubGxcuXCAhIQF4NHpm2bJlREREMGPGDLZv306+fPn48ssvzfuMGzeOTz75BE9PT+7du8eHH37Ijh07gEcX+0uWLEnSh5WVFUePHmXr1q04ODjQrl07fv75Z0qVKsWaNWvYtWsX1tbWtGvXjrJly6bruCIjI/Hw8KBu3bqEhobi5OTEyJEjMRqNdO7cmQMHDlCjRg1GjhxJQEAABQoUYOrUqQQGBtKnTx+mT5/O9OnTzUX+xONr0aIFzZo146+//qJly5Z4enpiZWXFiRMnWL16NQaDgfr163PhwgUOHTpEsWLFmDBhArGxsema8kZE/k9muIjShVD2oVxmH8pl9qFcJuXv709wcDCtWrVKcf1rr71G/vz5mThxIjY2NowbN47u3buzdetWbG1tn9h+vnwOL7Tokju3/QvrS5495S9rU/6ytrTkL8bG6gVE8mI55HXI6BCeCX3+si8V0V9SsbGx5kL540wmEwaDwXyBXaVKFQCuXr1KoUKFyJcvHwC1a9fmypUrABw7dowHDx4wb948AGxsbLh9+3aS/f/J1dUVB4dH/4EsUqQIoaGhnD9/njfeeAN7+0f/walXrx7BwcEp7r9s2TK2bdtmfu3h4UHfvn0BcHd3B8DBwYE7d+7QsWNHrK2tuXjxIiEhIVy9epW8efNSoEABAHx8fACSFM4fd+LECaZNmwZA4cKFefXVV7l27RoA1apVw8bGBoCCBQsSGhpKzZo1WbJkCT4+Pnh5edGhQ4cU2xWR1OXNRBdRuhDKPpTL7EO5zD6US1ixYgWzZ89m4cKFSe74fNyCBQuSvPb19aV69eocOXKEWrVqPbGPe/cevLCR6Llz2xMWFkl8vOm59yfPlvKXtSl/WVt68mcbG/+ConpxHoQ8yOgQnoo+f1lbWuoPKqK/pFxcXNi0aVOy5adOneKNN94wv04c1fLPgnt8/P/9B9tgMDBr1ixzUfpxlkbFWFsnfeslJCQkm3s9tTnNU5sTPbHPLVu2cOLECZYvX46trS2ffvppkr7S4/HtH48zpeMoV64cu3fvJjAwkO3btzN//nw2bNhgLraLSNqEZIKLKF0IZR/KZfahXGYf6cllZvpi9VmbOXMmP/zwAytWrMDZ2TnN++XMmRNHR0fz4JUnMZkSMJnSdw38NOLjTcTF6TOaVSl/WZvyl7WlJX826axpZAXZ5T2rz1/2pSL6S6p69erY2tqycOFCevToATx6sOa0adMYMGBAsu1LlCjBn3/+SUhICHnz5mX//v3mdVWrVmXHjh14e3tz584d5s2bl2S6l7QqVaoUZ8+eJSYmBqPRyI8//kipUqX+9THev3+fYsWKYWtry6VLlzh9+jRvv/02JUuW5N69e9y8eZPXXnuNmTNnUqZMGQoXLkxMTEyydtzd3dm/fz/NmzcnODiY+/fvpzr35JYtW3BycsLLywtPT0+qV69OaGgo+fPn/9fHIvIyykwXHroQyj6Uy+xDucw+XuZcLl26lG3btvHdd9/h5ORkcbvEqRU//fRT80j1kJAQQkJCKFas2IsKV0REROSlpSL6S8rKyopvvvmGadOm0aJFC+zs7DAYDPTt25e33nor2fa5c+emb9++eHt789prr1GmTBmuX78OwMiRI/nyyy/ZsWMHUVFR5qJ8epUrV44GDRrQsmVLSpQogZubG5GRkf/6GBs3bsymTZto3749ZcuWpX///sybNw8PDw8mTpzIF198gY2NDQULFqR37948fPiQsLAwunTpQs+ePc3tJB7funXriI2NZfLkyeTIkcNiv2XKlGHMmDHY2NgQFRXFJ598ogK6iIiIiCQRHBzM7NmzWb16dYoF9KCgIHx8fNi0aRM5c+bkxIkTTJgwgXHjxgEwduxYXFxcLE6fKCIiIiLPjiEhvfNaiDwncXFx/PDDDzRt2hQ7OztGjRpFiRIl6NatW0aH9sJEDZia0SGIZBrhw3pldAhYWxvJm9eBkJAHL+0oyexCucw+lMvsIz25LFAg1wuK6tlydXUFHl3nwv9NBXjq1Cnmzp2Ln59fsin/nJyc2LlzJ4GBgXTu3JmgoCDs7Oz4448/mDhxIocPH8ba2hoPDw+GDx9ucQ71f7p9O/wZHpll+oxmbcpf1qb8ZW3pyZ/92m2prs+KIls3yegQnoo+f1lbWq41NRJdMg1ra2v++OMPWrdujYODA6+++ipDhw7N6LBERERERP6VU6dOWVz32Wef8dlnn1lcX716dc6fP29+7eTkxJw5c55pfCIiIiKSNiqiS6bSr18/+vXrl9FhiIiIiIiIiIiIiABgzOgAREREREREREREREQyKxXRRUREREREREREREQs0HQuIplIjhk+eghFNqAHioiIiIiIiIiIZB8aiS4iIiIiIiIiIiIiYoGK6CIiIiIiIiIiIiIiFqiILiIiIiIiIiIiIiJigYroIiIiIiIiIiIiIiIW6MGiIplI1ICp2Gd0EPJMRIFymYrwYb0yOgQRERERERERkTTRSHQREREREREREREREQtURBcRERERERERERERsUBFdBERERERERERERERC1REFxERERERERERERGx4Lk+WPTGjRs0atSIKlWqJFnu4eFB3759n2lfmzdvpnHjxlhbW+Pt7c3ixYuxtbV9qjbv3r3L2LFjuXfvHgDR0dH079+fmjVrWtwnMDCQ6dOn8/3336e4/uzZs6xZs4YxY8Y8VWwAzs7OfPrppwwYMMC87K+//uLtt99mwoQJtGzZ8qn7SBQQEMAPP/wAwIkTJ3B2dsbe3p7SpUvTuHHjVI/537h9+7b5uG7evInJZKJYsWIATJkyBScnp6fuIygoiGnTpmEwGIiJicHBwQFfX19ee+01Ll68SGRkJK6urk/dj4iIiIiIiIiIiGRdz7WIDuDo6Ii/v//z7gY/Pz8aNmyItbX1M+tvxowZuLq60r17dwDOnDnDqFGjqFGjBkbjvxvE7+Li8kwK6AAFChRgz5499O/fH4PBAMCmTZsoXrx4mttISEgw75uali1bmovy9erVY8qUKZQuXRp49MXBs1agQAFzHv38/IiOjmbQoEHPtI+BAwcyffp03NzcAFixYgVLlixh1KhR7N69mzx58qiILiIiIiIiIiIi8pJ77kX01Ozdu5fp06dTrFgxypcvzw8//MC+ffsYOnQolSpVon379gDUqlWLNWvWkD9/foYNG8atW7eIiYmhatWqDBs2jNmzZ3Pt2jW6du2Kn58fnp6eBAUFYTKZGD16NMHBwcTHx+Pl5cXnn3/OoUOH+OabbyhatCjnzp0jNjaWr7/+mgIFCiSJLzw8nIiICPPrChUqsH79egAiIyNTbDvR//73P5YsWcLy5cuBRwV4Hx8fRo0aZR613aFDB+rWrcuvv/7KlStX6NChA126dOHevXv079+f6Oho3njjDS5cuEDv3r3x9PRMEp+trS3Ozs788ssv5nU7duygTp065m3279/PnDlzsLW1xWAwMGbMGEqXLo23tzfOzs5cvHiRBQsWcOzYMebMmYO1tTVWVlaMGTOGEiVKpDmX8fHxjBs3jtOnTxMdHc3ChQspUKAAa9euZc2aNdjb25OQkMDUqVNxcnKyeOxPEhwczGeffcamTZuARwX9Tz75hI4dO7Jv3z42btzIhAkTUs1NorCwMMLDw82vO3XqBMDx48dZsWIFOXPmxN7enrp16/Lll18SGhpKTEwMbdu2pUWLFsyaNYubN29y9+5dQkJCKFeuHJMmTeLOnTsMGjQIk8lEeHg4LVu25KOPPkrzuRQREREREREREZHMI0OL6GPHjuXrr7+mfPnyLFiwACsrq1S3v3PnDh4eHnTo0IGEhAQaNmxIq1at6Nu3L3PnzmXJkiXY2dmZt/f398fGxobVq1cTGxvLhx9+iKenJ0ajkWPHjjFx4kQKFSrEwIED2bFjB97e3kn669mzJ3369GHXrl14enpSp04dvLy8sLKysth2otq1azNy5Eju3r3Lq6++ytatW2natGmS9q2srLh79y4LFy7kypUrtGvXji5duvDtt99StmxZRo4cSVBQEO3bt7c48r1p06Zs2rQJT09Pzp07R+HChXF0dAQeFfqHDRvGd999R7Fixdi2bRvjx49n6dKlANjZ2bFs2TKioqIYO3Ysq1evJl++fOzbt49JkyaxYMGCNOfy8uXLzJ07l8KFCyc5n5GRkcydO5dChQoxZ84c/P39GTJkiMVjf5JixYoRHR3N/fv3CQ0NpUyZMhw+fJiOHTty+PBhateubTE3VatWTdLW8OHD6devH2XLluXNN9+kfv36uLq6UqVKFerUqUOlSpVo3rw5Y8eOxcvLi9atWxMdHU3Tpk2pVasWVlZW/P7776xbtw6j0UibNm04ePAgly9fplixYkyYMIHY2NgXcieGSFZjbZ01HslhZWVM8q9kXcpl9qFcZh/KpYiIiIhkFc+9iB4aGpqsON27d2/Kly9PREQE5cuXBx4VnZ80p3aePHk4c+YM7du3x9ra2jwC2JITJ07QrFkzAGxsbKhWrRqnTp3CxcWFkiVLUqhQIQCKFClCaGhosv0rVKjArl27OHbsmHmu87lz57J69epU2wawtramYcOG7Nq1i/bt27Nz505WrFjBtWvXkvRRo0YNAJycnLh//z4A58+fN0+d4ubmRpEiRSweY+3atZkwYQKRkZFs3LiRpk2b8vvvvwNw9epV8uXLZ55L3NPTky+//NK8b+Jc9VeuXOH27dv069cPAJPJRFxcnMU+U1KyZEkKFy4MJD2fr7zyCgMGDMBgMHD79u0k8+OndOxp4enpya+//sqdO3do1KgRa9euBeDIkSN07tyZsWPHppibfxbRmzVrRv369Tl06BCBgYF8+umnvP/++wwfPjzJdseOHePUqVPm0e9WVlYEBwcDUK1aNaytH32MKleuzMWLF6lduzZLlizBx8cHLy8vOnTokOZjE3lZ5M3rkNEhpEvu3PYZHYI8I8pl9qFcZh/KpYiIiIhkdhk2J3riwzoTmUwm8+//nKM7saC7bNkywsLCWLlyJUajkffff/+J/SckJCT5PXFEd2LhM6XtEkVGRmJvb4+HhwceHh707NmTxo0b89tvv6XadqIPPviAmTNn4uLiQtGiRSlcuHCyIvo/40hs6/FzkNr869bW1tStW5fdu3ezb98+vvjiC3MR/UkxJj541WAwULBgwacaMZ3S+bxx4waTJ09my5YtFC5cmMWLFyeJLaVjT4vatWtz9OhR/vrrLwYMGMCRI0cICgoiLi7O/MXIk3IDj/Lr4OBA/fr1qV+/Pp07d6ZJkyYMGzYsyXYGg4GRI0dSuXLlJMt//vnnZMdsNBopV64cu3fvJjAwkO3btzN//nw2bNiAjY3NvzpekewoJORBRoeQJlZWRnLnticsLJL4eNOTd5BMS7nMPpTL7CM9ucxqX76KiIiISPaSYdO55M2bF3t7e37//XfKli3Lvn37zOscHR25e/cu8Gg0deIo5fv371OqVCmMRiNHjx7lzz//JCYmBnhU6IyJiUkynYu7uzv79++nUaNGxMTEcPjwYZo3b05kZOQT44uPj6dRo0ZMmjSJmjVrAo9G1UdFReHk5JSmtqtUqcLff//N+vXrzSOj06JUqVIcP36cBg0acObMGW7evJnq9s2aNWPYsGFUrlw5yfGXLFmS+/fvExwcTLFixThw4ECyQjDA66+/TlhYGOfOnaN8+fIcOnSIy5cv07FjxzTHnJKwsDAcHBwoXLgwDx48YM+ePebR6k+jRo0aLF68mNjYWIoVK4aHhwdff/21eWS7pdw87tKlS3z00UesX7/eXHj/448/KF68OAaDwfx+AqhatSo7duygcuXKREZGMmHCBPOI/iNHjhAbG4uVlRUnTpygfv36bNmyBScnJ7y8vPD09KR69eqEhoaSP3/+pz52kewiLi5rFb7i401ZLmZJmXKZfSiX2YdyKSIiIiKZXYZM55I3b15mz57NyJEj6devH/nz58fV1dW8vlWrVvj4+BAcHEzhwoV5/fXXSUhIoHXr1gwaNIjDhw/j4eFBt27dmDx5Mm+88YZ52ozZs2eb2+nYsSOjR4+mffv2xMXF0aJFC9zc3AgMDHxi3FZWVixYsICpU6fi5+eHra0t8fHxjB07lkKFCqW57caNG/Ptt98yZMiQNJ+zjz76iL59+9KhQwcqVKiAs7NzqvPFu7q6YjKZks25niNHDiZNmsSAAQOws7PD1taWsWPHJts/R44cTJ8+nVGjRmFnZ4fJZMLX1zfN8Vri4uJCpUqVaNWqFfnz56dPnz4MHjyYbdu2PVW7OXPmxMrKyvzg02rVqjF8+HDzXO+WcvO40qVLM3z4cPr06YOtrS1WVlbkyJHD/P7x9PRk6tSpWFlZ8fnnnzNq1Cjat29PdHQ0rVq1Mo/iL1GiBJ999hm3b9+mYsWK1KhRg/PnzzNmzBhsbGyIiorik08+UQFdREREREREREQkizIkpDSPSQa4dOkS3bt3Z+/evRkdSoa7fPkywcHB1K1bl4iICBo2bMjGjRspUKBARocmj/Hz8yM6OppBgwY9szajBkx9Zm2JZGbhw3pldAhpYm1tJG9eB0JCHmiUZBanXGYfymX2kZ5cFiiQ6wVFlX3dvh3+QvrRZzRrU/6yNuUva0tP/uzXPt3gxMwosnWTjA7hqejzl7Wl5Vozw6ZzEcty5crFsmXLmDdvHrGxsXz22WcqoIuIiIiIvCAHDx6kdu3aAJw/f56AgABKlSpF27ZtMzgyEREREckImaaIXrp0aY1C//8KFChgnppEMq8+ffpkdAgiIiIi8owtWrSIb775hsDAQO7fv89HH31E2bJl+fHHH7l37x69emWNu6lERERE5NkxZnQAIiIiIiIimcXatWv5+uuvAdi6dStFihTB39+fb775hg0bNmRscCIiIiKSIVREFxERERER+f9u375N5cqVAfjll1+oX78+AMWLF+fvv//OwMhEREREJKOoiC4iIiIiIvL/OTg4EB4eTkxMDIcPH8bT0xOA8PBwbG1tMzg6EREREckImWZOdBGBHDN89CTnbEBP5RYREcm6atasyRdffIG1tTX58uXD3d2duLg45syZQ6VKlTI6PBERERHJABqJLiIiIiIi8v+NHDmSokWLYmNjw9y5cwGIjIzkf//7H0OHDs3g6EREREQkI2gkuoiIiIiIyP+XO3duxo4dm2RZrly52LlzZwZFJCIiIiIZTUV0ERERERF5qc2YMSNN2xkMBvr37/+coxERERGRzEZFdJFMJGrAVOwzOgh5JqJAuXxM+LBeGR2CiIiIRVu2bEnTdiqii4iIiLycVEQXEREREZGX2t69ezM6BBERERHJxFREFxERERGRl9qVK1fSvG3JkiWfYyQiIiIikhmpiC4iIiIiIi+1xo0bYzAYUt0mISEBg8HA2bNnX1BUIiIi8jzYr932zNs0GAzE2FhhGxuPTUKCeXlk6ybPvC/JGCqii4iIiIjIS+3bb799YhFdRERERF5eKqKLiIiIiMhLrXr16hkdgoiIiIhkYtm2iH7jxg0aNWpElSpVAIiNjSV//vz4+vqSL1++NLdTr149Fi1aROnSpdO0fUBAAAcOHGDmzJlJlt++fZuxY8cyZ84chg4dSqVKlahfv7552a1bt7h06RI1a9ZM+0Gm4Pr163zyySfUqFGDcePGmfseMGAAADdv3sRkMlGsWDEApkyZgpOT01P1CeDs7Ey1atUAiIiIoFKlSvTv35+8efNy9uxZ1qxZw5gxYyzuv2HDBpo3b57kPKXXxYsXiYyMxNXVNcnygIAApk2bRpkyZZIs79y5Mw0aNEh3P5YkHoOIiIiIZC1Dhw5l8uTJAAwcODDVbadPn/4iQhIRERGRTCTbFtEBHB0d8ff3N7+eOXMms2fPTlLMNZlMGI3G5x5LgQIFkhWGH18WGBjIhQsXnrqIfuzYMcqXL28uoCf2k3ge/Pz8iI6OZtCgQU/VT0qWLFmCnZ0dCQkJfPPNN3z00UesX78eFxeXVAvo8fHx+Pn50bx58xTPU1rt3r2bPHnyJCuiA9SoUSPZFxvP0uPHICIiIiJZy+3bt82///333xkYiYiIiIhkRtm6iP5P7u7uLFu2DHg0wvy9997j0qVLzJs3jw0bNrBixQpy5MjBK6+8wvjx4ylYsCDwaCTzpUuXuH79On379qVRo0Zcv36d4cOHYzAYePDgAR9//DFNmzYFICwsjIEDB3Lz5k0cHR2ZMWMGISEhtG3blp9++skcz40bN2jbti3fffcds2bNwmQykSNHDlavXs2uXbtwcHAgPj6et99+m3Xr1pnjATh16hSTJk3CaDQSFxfHkCFDcHR0ZMGCBYSGhvLll1/i6+ub6vkIDg7ms88+Y9OmTeZz8sknn9CxY0f27dvHxo0bmTBhAqNHjyY4OJj4+Hi8vLz4/PPPU23XYDDQvXt39uzZw48//oijoyPTp0/n+++/Z8WKFfzwww9YW1tjZ2fHtGnTmDFjBrdu3eKjjz5iwoQJ5vM0ePBgnJycOH/+PFevXqVu3boMGzYMk8nExIkTOXPmDNHR0bRv357SpUuzYsUKcubMib29fZqL2X379qVevXrm7cePH0+RIkVo2bIlX375JaGhocTExNC2bVtatGjBrFmziIiI4NatW1y9epVSpUoxa9Yshg8fbj6GefPmMXjwYO7fv8/Dhw+pVasWgwcPTlM8IiIiIvLiLV682Pz7t99+a3GQTdj/Y+/O42M62z+Of2cmq0S2lpRW7aWIXe1bqnZqJyGeWktri11riaXWWoqqUi1VFKWqqC7qQUutVUpRSy1dNMhCJLLM/P7wmJ8R0aRiJpl83q+XVzLn3Oe+r3OuTEyuuec+sbH2CgkAAABZSI4popvNZn311VeqVKmSdVu+fPk0ZMgQ/fnnn3rzzTe1ZcsW+fj4aPHixZo7d64mTZokSfL29tbChQt18uRJ9ejRQ40aNdLly5fVs2dP1atXT3/88YfatGljLaIfO3ZMX3/9tXLnzq3+/ftr48aNql27dpqxFShQQK1bt9atW7fUr18//fnnn/riiy/Url077du3T6VLl7YpoEvS8OHDNXHiRFWuXFlHjhzR0KFD9dVXX6l3797atWvXPxbQ74x769YtRUdHKyYmRsWKFdO+ffvUuXNn7du3T7Vq1dLy5cvl6uqqVatWKSkpSe3atVP16tVtrmNaypYtq1OnTqlKlSrWbbNnz9bmzZv1xBNPaO/evfrzzz/Vv39/fffdd1q2bJkuXbpkbWsymXTmzBm98847iouLU40aNdS/f3/t3LlTf/zxh1auXKmYmBiFh4dr4cKFql27tsqVK5eh2eAtW7bUJ598olatWslsNuvrr7/WmjVrNHfuXNWpU0ft27fXrVu31LJlS9WsWVMmk0mHDx/WqlWrZDAY1KBBA506dcrmHL7++mvdvHlTK1eulCR9+OGHSkhIkIeHR7rjApyNi8uj/8TPo2AyGW2+Ivsil86DXDqPrJrLIUOGaObMmakK6UeOHFF4eLi2bdvmoMgAAADgKE5dRI+JiVFYWJik28ttVKxYUb1797bur1ixoqTbRe8yZcrIx8dHklS9enVt2rTJ2q5WrVqSbq/7ffPmTUVHR8vf319LlizR4sWLJUlRUVHW9uXKlVPu3LmtY5w+ffqBRfR7hYSE6I033lC7du20efNmtW/f3mb/9evX9ddff1nXIC9btqwiIyP/1cyY6tWr6+DBg7py5YoaN26stWvXSpL279+vrl27avz48XrxxRclSa6urqpcubKOHj2ariK60WiUm5ubzbbmzZurV69eaty4sRo0aKASJUrYFM7vVbVqVRkMBnl7eyt37tyKiYnRjz/+qOeee07S7SV73n///X+M5YcffrD+LNwxb9481alTRxEREbp+/bqOHTumokWLKjAwUIcOHdLRo0ets/RNJpMuXrwoSapcubJcXV0lSXnz5lVMTIy8vLys/ZYrV04zZszQgAEDVKdOHbVq1YoCOnI8f3+vf26Uhfn4eDo6BGQScuk8yKXzyGq5PHPmjF599VW99dZb1teyy5cv17Rp09SiRQsHRwcAAABHcOoi+r1rot/rTiFUkiwWi833d888uft7i8Uig8GgCRMmqEGDBuratauuXbum6tWrW9sYDIZU7TOiTJkySkxM1JkzZ/TTTz9p/PjxNvvvjvVu/2Zt91q1aunAgQP666+/NHjwYO3fv19HjhxRcnKyAgMDU41377V5kH379qVaxmT8+PG6cOGCdu7cqVdffVX9+vWzvhlwPy4utj+iFotFFotFZrM5vaco6cFrogcHB2vbtm06dOiQ9Q0Dg8Gg0aNHq3z58jZtd+/efd+Y7pY3b15t2bJFhw4d0rZt2zR37lxt2LAhQze0BZxNVFSco0P4V0wmo3x8PBUbG6+UlIz93kHWQi6dB7l0HhnJpT3fjF25cqVefvll9e3bV1OnTtXEiRO1a9cuTZo0ifvfAAAA5FBOXURPr6CgIEVERCgmJka+vr7atWuXTfF09+7dKl26tE6cOCEfHx/5+fkpOjpaxYsXlyR99tlnMhqNSkxMlHR7vfIbN27I29tbBw8eVL169f4xBoPBYD1ekjp16qQxY8YoODhYJpPJpq2Pj4/y58+vAwcOqHLlyjpw4IDy588vb2/vDJ97tWrVtGTJEiUlJalAgQKqUqWK3n33XVWrVk3S7Zn0O3fuVOPGjZWYmKh9+/b94x8PFotFS5YskYeHh6pWrar9+/dLuv3JgA8//FCvvvqqunTpops3b1pnld997v+kYsWKWrdunbp166aEhAR17dpVS5cuTXUN06tly5ZatmyZfvnlF40cOVKSVKlSJW3dulXly5dXfHy83njjDY0dOzbNPu7O/3fffafExEQFBwfrueee086dO3Xx4kWK6MjRkpOzd6ErJcWc7c8Bt5FL50EunUdWy6W3t7c++OADDRw4UPXr11fhwoX1ySefqGjRoo4ODQAAAA5CEV1SYGCghg4dqh49esjd3V1+fn6aOHGipNvLwNy8eVN9+vTRxYsXrYXUXr16afz48cqTJ49CQ0NVo0YNDR06VLVr11ZQUJDGjRun33//XQEBAWrRooWuXLnywBiqVKmi8PBweXp6Kjw8XM2bN9ekSZM0bdq0+7afNm2aJk+eLKPRKIvFkma7f+Lt7S2TyaSCBQtKur1UyWuvvaYPPvhAktS5c2eNGzdOISEhSk5OVuvWrVW2bNn79tW9e3cZjUbFxcWpdOnSevfdd21mrfv6+iohIUEdOnSQp+ftj+1OmDBBefPmVf78+dWuXTvrOvQP0rhxYx06dEghISFKSkpSSEiIvLy8VL16dU2fPl0mk0ldunSxOeZ+y7nUqFFDffv2VaVKlTRq1ChVrFhRuXLlkiT169dPY8aMUUhIiG7duqW2bdumWprmbnefw9y5czVq1CgtWbJEycnJql69uoKCgv7xvAAAAOAY95uIMWvWLI0ePVpXrlzRU089ZW3zoNeEAAAAcE4GS1prg8ChvvnmG23YsEHz5893dCiwo4TB0x0dAvBIXB/V19Eh/CsuLkb5+3spKiouS82SRMaRS+dBLp1HRnKZJ0/uRxpLyZIl77sE450/le7e98svvzzSWB6VyMjrdhmH52j2Rv6yN/KXvWUkf55rt9gpKvuJb9/UbmM9iutnMBjk6mpSUlKKzbK/9jwv/Hvpea3JTPQsaODAgbp27Zpmzpzp6FAAAAAApzd58uQM38cIAAAAOQdF9CzorbfecnQIAAAAQI7Rpk0bR4cAAACALMz4z00AAAAAwPnt3LlTp0+ftj7+8ccfFRISokaNGmnBggUOjAwAAACORBEdAAAAQI73zTffqE+fPvr1118lSXFxcerbt6/i4+NVu3ZtLV26VJs2bXJwlAAAAHAElnMBshCPWcO5CYwT4IY+AABkP0uXLtWQIUPUpEkTSbeL6rdu3dKHH34oHx8fVapUSStXrlTz5s0dHCkAAADsjZnoAAAAAHK8M2fOqEOHDtbHP/zwg2rUqCEfHx9JUt26dXXu3DlHhQcAAAAHoogOAAAAIMdLSEhQ7ty5rY9//PFHVahQwfo4V65cunHjhiNCAwAAgINRRAcAAACQ4/n6+ioyMlKSFBkZqd9++00VK1a07r927Zq8vLwcFR4AAAAciCI6AAAAgByvYsWKevfddxUZGalZs2bp8ccfV/ny5a37N2zYoGeffdZxAQIAAMBhuLEokIUkDJ4uT0cHgUyRIOXIXF4f1dfRIQAA8K/07t1bISEhWrFihYxGo6ZMmSKj8facow8++ECzZs3S/PnzHRwlAAAAHIEiOgAAAIAcr2TJktq6dasOHjyoZ555RsWKFbPu8/b21sSJE1WvXj3HBQgAAACHYTkXAAAAAJAUGBiopk2b2hTQJal9+/Zq3br1v+pz165dqlGjhsLDw1Pt27Nnj1q2bKmgoCC98MIL2rhxY5r93Lp1S2PHjtVzzz2nChUqaMCAAbp27dq/igkAAAAZQxEdAAAAAB6BxYsXa9KkSSpYsGCqfZcvX1bfvn3Vrl077du3T6+//rrGjBmjI0eO3LevGTNm6NChQ1q3bp2+/fZbJSYm6rXXXnvUpwAAAABRRAcAAACAR8Ld3V2ffPLJfYvon3/+uQoWLKiuXbvK09NT9erV0/PPP6+1a9emapucnKxPP/1UgwYNUoECBeTv76/hw4dr+/btunz5sj1OBQAAIEdjTXTc16VLl9S4cWNVqFDBZvvgwYN17tw5JSUlqWPHjunq69atWypbtqxOnjyp9evXZ+jYtOzdu1d9+/ZV6dKlJUnx8fEqXry4IiIi5O7u/lB9/xuff/65mjRpIhcXnlIAAAC4rWvXrmnuO378uPW17B3PPvusvvjii1RtL1y4oBs3bti0L1KkiDw9PXXs2DEFBgb+YyxGo0FGoyED0f87JpPR5iuyF/KXvZG/7C0j+TMYHv3vc3tzcbHfz+2juH53urz99f/7t+d54dGi4oc0+fr6avny5am231tYz4g2bdo8TEg2ihUrZo3PYrFo2LBh+vDDD9WrV69/PNZsNstozLxfZPPmzVPDhg0pogMAAGRDR44cUdmyZSVJhw8fVvny5R/5mFFRUSpZsqTNNj8/v/uucx4VFSXp9uvzu/n4+KR7XfSAAC+7Fl18fDztNhYyH/nL3sifYyS+t+7h+5DkkZ6GrqaHHiurcV231Y6DPbrr5+Ji27eXv9cjGwv2RcUPGTZv3jzdunVL4eHhqly5sgYOHKhvv/1Wly9f1oQJE1S1alX99NNPGjt2rPLkyaNy5cpl6NgjR45ozJgx8vHxUZMmTTRx4kQdO3bsgUVvg8GgChUq6JdffpEk7d69W/Pnz5eLi4tMJpMiIiJUsGBBBQcHq1mzZjpz5owWLFigdevWafXq1ZKkoKAgvf7660pMTNSECRN08eJFJSUlKTg4WL1799batWu1d+9eSdLZs2fl5eWlxYsXa9GiRTp//ry6d++ut956S7NmzdKZM2eUlJSkEiVKaOLEiRTXAQAAsrD//Oc/OnDggEwmk/7zn//op59+clgsGS10p7f9tWtxdpuJ7uPjqdjYeKWkmB/5eMhc5C97I3+O5ZaU8lDHGwy3C7DJySmyWDIpKNhNWvmLi4pzXFBIN/90vNlBZQ//mslk0s2bN/XUU0/pww8/1OrVq/XRRx+patWqmjp1qgYNGqT69etr8+bNGTp2ypQp6tevn1544QUtXbo0XbPGExMTtX37djVr1kwJCQkaP368Vq1apYCAAO3YsUNTpkzRwoULJUn58uXTkCFD9Oeff+rtt9/W5s2b5enpqeHDh+vEiRPavXu38ubNq8mTJ8tsNis0NFQ1atSQyWTSgQMHtHnzZnl5ealTp07avXu3BgwYoLffflvvv/++zpw5o/379+vrr7+WJK1fv15RUVHKkydP5icAyIKc7aNqfCTWeZBL50EunUdWymWePHnUvn17FS5cWElJSRoyZEiabWfOnJkpYwYEBCg6OtpmW1RUlAICAlK1feyxxyRJ0dHRypUrl6Tbn8SMjo627vsnZrNFZrP9qjIpKWYlJ1PEy67IX/ZG/hzD9aEr37ff6LRYbv+OR3Zz//zxXHQeFNGRppiYGIWFhdlsmzx5cqp21apVk3S7OH3nD4ETJ06oYsWKkqSaNWumOUZax1apUkWS1KhRI02ZMuW+x54+fdoan9lsVr169dSqVSudOHFCkZGRGjhwoHVfcnKy9bg7cR07dkwlSpSQp+ftj7pNnz5dkjR//nxdvHhRBw8elCTFxcXpwoULkm7PVvfy8rLGHBMTYxNT4cKFlTt3bvXo0UPBwcFq2LAhBXTkKOl59zY74iOxzoNcOg9y6TyyQi7ffPNNLVmyRJGRkZKkv//++5GPGRQUpPXr19tsu3tZmbsVKFBAfn5+OnbsmPLnzy9JOnnypBITE1WmTJlHHisAAEBORxEdaUprTfR7ubq6PnC/2Zz2u25pHXvnY6kPmoF+95ro9x6bN2/eNGO/M6bFYrnvu7sGg0F9+vRRs2bNbLavX78+1bIs9x7v6empdevW6ejRo/rvf/+rVq1a6cMPP1TRokXTPA/AmUQ52UfV+Eis8yCXzoNcOo+M5PJRv0lbtmxZvfXWW5Kkxo0bp+s18MNq0aKF5s2bp6VLl6pTp07673//q507d2rNmjWSbhfUhw8fro0bN8rNzU0dOnTQnDlzVLJkSeXKlUtTpkxR48aN9fjjjz/yWAEAAHI6iuh4JIoVK6bDhw+rbt262rlzZ4aOLVy4sPXYO8uiZEShQoUUGxurEydOqGTJktqzZ4/Onj2rzp0727QrU6aMxo8fr+vXryt37twaMWKE2rZtq0qVKunLL79Us2bNZDabNWnSJPXr1++BYxoMBiUmJurUqVP69ddf1aZNG5UtW1Y//fSTTp48SREdOYazflSNj8Q6D3LpPMil88hqudy69faNzaKionTx4kVJt19f+vj4ZLivoKAgSbJ+KvKbb76RJB09elSPPfaY3n33XU2cOFEzZ85U/vz5NXPmTOvNRuPj43Xu3DnrpI3+/fsrLi5Obdq0UUpKiurXr6+IiIiHOlcAAACkD0V0PBIjRozQ+PHj9eGHH6py5cpycXF54Iz0uw0bNkwTJkzQsmXLVKdOHZlMGbtrsoeHh2bOnKkxY8bI3d1dZrNZEyZMSNUuX758Cg8PV7du3WQymVS6dGlVqVJF5cqV0+nTp9WhQwelpKSodu3a912b8m516tRRaGioZsyYoW+++UZr166VwWBQYGCg6tevn6H4AQAA4Dg3b97U0KFD9d///tf6yUWTyaQmTZpo8uTJcnNzS3dfR48efeD+ypUr67PPPrvvvqpVq+rkyZPWx25ubho7dqzGjh2b7vEBAACQOQwW7laALOaHH36Qn5+fSpYsqUOHDmnMmDH3vTmpM0oYPN3RIQAP5fqovo4OIVO5uBjl7++lqKi4LDVLEhlHLp0HuXQeGcllnjy57RSVNGnSJO3du1cvv/yyChUqJIvFotOnT+vdd99Vo0aNFB4ebrdYMlNk5HW7jMNzNHsjf9kb+XMsz7VbHup4g8EgV1eTkpJSuLFoNpRW/uLbN3VgVEiv9LzWZCY6shwXFxe99tpr8vDwUFJSksaPH+/okAAAAJBD7Ny5U4sWLVKhQoWs24KCglS6dGn1798/2xbRAQAA8O9RREeWU7lyZa1fv97RYQAAACAHunr1qp566qlU24sUKaK//vrLAREBAADA0YyODgAAAAAAsoqnnnpK33//fart33//vQIDAx0QEQAAAByNmegAAAAA8D9dunTRoEGD1LRpUxUrVkwGg0G//vqrtmzZosGDBzs6PAAAADgARXQAAAAA+J/27dvL1dVVH330kb7++mtJUqFChTRp0iQ1a9bMwdEBAADAESiiA1mIx6zh3EndCbi4GOXv70UuAQDIplq1aqVWrVo5OgwAAABkEayJDgAAAAAAAABAGiiiAwAAAAAAAACQBoroAAAAAAAAAACkgSI6AAAAAAAAAABp4MaiQBaSMHi6PB0dBDJFgpTjcnl9VF9HhwAAwENJTk5W69at9fnnnzs6FAAAAGQhzEQHAAAAAEkuLi6yWCz69ddfHR0KAAAAshBmogMAAADA/7Rt21aDBg1SrVq19NRTT8nNzc26z2AwqEOHDg6MDgAAAI5AER0AAAAA/mfatGmSpDNnzqTaRxEdAAAgZ6KIDgAAAAD/c+LECUeHAAAAgCyGNdEfwo4dOxQSEqKwsDC1bdtWAwcOVHR09AOPGTlypFatWnXffRs2bMiUuC5fvqzdu3dLktavX6/Vq1c/VH9ms1lVq1bVxYsXrdvmz5+vLl262LSrV6+eTpw4oeDg4PvO3EnL+vXrFR4enu728+bNU7Vq1Wyu9aVLlxQWFpbuPtJy6dIl1axZ86H7AQAAQPb2+++/a+/evY4OAwAAAFkARfR/KTExUUOGDNGcOXO0fPlyrVu3Tk899ZQ++eSTf9Xf5cuXtXLlylTbzWZzhvvau3evtYjepk0bdezY8V/FdIfRaFT16tX1/fffW7ft3r1bV65c0c2bNyVJZ8+eVXJyskqWLPlQY6VX/vz5NXPmTLuMlV4Wi8XRIQAAAOAhRUdHq0ePHnr++efVo0cPSVJkZKRatGihy5cvOzg6AAAAOALLufxLiYmJSkhI0I0bNxQYGChJGjZsmHV/cHCwFi9erKJFi+rMmTPq1auXvv32W0nSzz//rFdffVUXL15U7dq1NWzYMA0ZMkSnT5/WkCFD1KFDB7399tvy9fVV1apV1bRpUw0fPlwJCQm6efOmmjdvru7du0uS3nnnHe3YsUPJycl6/vnn1bx5c82ZM0dms1l+fn6Kj4/XrVu3NHToUO3cuVPz58+Xm5ubDAaDIiIiVLRoUYWGhqpu3bo6ePCgzp07p9DQUHXr1s3mfGvXrq0dO3aoU6dOunHjhq5fv67q1atr//79qlu3rvbs2aPatWtb22/dulU//vijzp07p8GDB6tZs2a6du2aXn/9dcXGxurWrVvq0KFDqjUljx8/rqlTp0q6/QbC8OHDVbZs2VTXPyQkRCtXrtRPP/2kcuXK2ewbOXKkypUrp5CQEElSzZo1tXr1au3Zs0d79uyRm5ubfvzxRzVu3Fgmk0m7d+9WYmKili1bZu1j2rRpOnHihGJiYjRjxgwVLVo0zdjCwsJUokQJnT59WgsWLNCkSZN05swZJSUlqUSJEpo4caJcXHiqAQAAZAdvvvmmzGaz1q5dq86dO0uScufOrWeffVZvvPGG5s6d6+AIAQAAYG9U9v4lb29vDRw4UO3atVO5cuX03HPPqVGjRipatOg/HvvXX3/pvffeU2Jiol544QW1adNG/fv318yZMzVz5kzt3btXR48e1bZt2xQQEKATJ07oxRdfVIsWLXTr1i1Vq1ZNHTp00JkzZ/T1119r7dq1MpvNevXVV9W2bVu1bt1at27dUs+ePTVv3jxJUnx8vEaNGqWPP/5YBQoU0JYtWzRp0iR98MEHMplMunr1qhYtWqRz586pU6dO9y2iT5s2TSkpKdq7d68qVaqkqlWr6vvvv1fdunW1e/duNWvWzNrey8tL7733nr777jvNmjVLzZo101tvvaWgoCC98sorunHjhpo0aZJq6ZQRI0Zo7ty5Kly4sM6cOaNBgwbp888/T3UNTSaTxo4dq/Hjx2vt2rXpypnJZNJPP/2kLVu2KDo6WvXq1dOiRYs0YMAAdevWTbt27VLZsmV15coVNWjQQCNGjNDy5cs1f/58zZ49+4Gxubu7a+nSpTp+/Lj279+vr7/+WtLtpWqioqKUJ0+edMUIZGcuLs734SaTyWjzFdkXuXQe5NJ5ZNVc7t69W6tWrVJgYKAMBoMkycPDQ6NGjVKTJk0cHB0AAAAcgSL6Q+jVq5fatWun3bt3a+/everYsaP69eunl1566YHH1ahRQwaDQe7u7ipVqpROnz4tPz8/mzYFCxZUQECAJMnf31/bt2/XqlWrZDKZlJSUpOjoaP3444+qWLGiTCaTTCaTFi1alOaYv/32mwICAlSgQAFJUvXq1TV27Fjr/mrVqkm6vUzK/dZ1z5s3r5544gn9/PPP2r17t6pXr67nnntOCxYsUEpKig4cOKA33njD5hwlKV++fNb+Dh8+bG3j7e2t0qVL65dffrEeExsbq3PnztnElZCQoKSkJLm6uqaKqUKFCipZsqQ++ugjPf/882me+91Kly4td3d3BQYGymw2q2LFipKkwMBAxcbGSpJy5cqlSpUqSZIqVqyoNWvWPDC2O7FIUuHChZU7d2716NFDwcHBatiwIQV05Bj+/l6ODuGR8fHxdHQIyCTk0nmQS+eR1XJ59erV+75+8/T0VEJCggMiAgAAgKNRRH8I8fHx8vf3V7NmzdSsWTM1bdpU48aNS1VET0lJsXlsNNrOtrkzw+Vubm5u1u/nzJmjPHnyaNasWTKbzapcubJ1X0bW4b67rcVisYkjPcuN1K5dW3v37tW+ffvUv39/+fn5yWQyadeuXSpUqJDNGwEmkyldMdx97kajUUajUcuXL0/3OQ0dOlTt27e3WfLl3uuZnJycZlx3n/ed2O4+/s62f4rtTr48PT21bt06HT16VP/973/VqlUrffjhh+n6hAKQ3UVFxTk6hExnMhnl4+Op2Nh4paRk/B4VyDrIpfMgl84jI7m05xu1hQsX1n//+18FBwfbbP/4449VuHBhu8UBAACArIMi+r+0a9cuTZs2TatWrVLu3LklSX/88YcKFiwoSfLz89O1a9dUtGhR/fTTTzbH7tmzRy+99JJu3bql48ePa+TIkYqMjFRiYuJ9x4qOjrbOmN68ebPMZrMSExNVvnx5ffLJJ0pMTJSLi4t69eqlcePGyWAwpOqrcOHCio6O1sWLF1WgQAHt2rVL5cuXz9A5165dW3PnzpW7u7u1YF6tWjUtXrzYZj30tFSsWFE7d+5UUFCQYmNjdfz4cZUuXdp6E1Rvb28VLFhQ3377rYKDg3X69Glt3rxZAwcOTLPPgIAA9e7dW9OmTbPOVvf19dXVq1cl3Z6Bf7+Z9Q8SFxenI0eOqGzZsjp48KBKliyZ7tiOHj2qX3/9VW3atFHZsmX1008/6eTJkxTRkSMkJztvMSslxezU55eTkEvnQS6dR1bL5SuvvKJBgwYpODhYKSkpmjBhgo4dO6ajR49qzpw5jg4PAAAADkAR/V+qXbu2zp8/r5deekm5cuWSJD322GOaOHGiJKlHjx6aPHmygoKClC9fPpnNt/8wSElJUf78+dW/f39duHBB7dq1U6FCheTr66vY2Fh169ZNffr0sRmra9eumjBhgjZs2KAmTZqoZcuWGjZsmNatW6fmzZurS5cuMpvNev755/X000+rSpUqCg8Pl6enp3WmtYeHh6ZMmaLBgwfL3d1dbm5uGj9+fIbOuVKlSjpx4oT1hp3S7SL6+++/r+HDh//j8f3799frr7+uzp07W292+sQTT9i0mTZtmiZNmqQlS5YoKSkpXf126NBB69atsz5u27athg8frosXL+qJJ55QoUKF0j1j32w2K3/+/Priiy80e/ZsXb9+XW+++Wa6YytUqJDeeecdrV27VgaDQYGBgapfv366xgYAAIDjNWzYUB999JGWLl2qYsWK6fDhwypcuLBef/31+97wHgAAAM7PYMnIeiAAHqmEwdMdHQLwr10f1dfRIWQ6Fxej/P29FBUVl6VmSSLjyKXzIJfOIyO5zJMnt52icl6RkdftMg7P0eyN/GVv5M+xPNdueajjDQaDXF1NSkpKydDSvcga0spffPumDowK6ZWe15rMRAcAAACA/0lJSdHSpUv17bff6s8//5S7u7vy58+vZs2aqU2bNo4ODwAAAA5AER0AAAAA/mf69Olas2aN6tatq/Lly8tisejSpUuaMGGCzp8/r/DwcEeHCAAAADujiA4AAAAA/7NlyxYtXrxYlStXttm+d+9eDR06lCI6AABADmR0dAAAAAAAkFUkJCSofPnyqbZXrFhRN2/etH9AAAAAcDiK6AAAAADwPw0aNNC3336bavvOnTsVHBzsgIgAAADgaCznAmQhHrOGcyd1J+DiYpS/vxe5BAAgm5g/f771+8cff1xjx47V+vXrVaRIERmNRp07d04HDx5Uly5dHBglAAAAHIUiOgAAAIAcbf369TaPc+XKpVOnTunUqVM229auXat+/frZOzwAAAA4GEV0AAAAADna/ZZvAQAAAO6giA4AAAAA97h27ZoSEhJSbc+fP78DogEAAIAjUUQHspCEwdPl6eggkCkSJKfO5fVRfR0dAgAAj8SWLVs0fvx4xcbG2my3WCwyGAz65ZdfHBQZAAAAHIUiOgAAAAD8z/Tp09WoUSM9//zz8vR05rfEAQAAkF4U0QEAAADgf2JiYjRu3DiZTCZHhwIAAIAswujoAAAAAAAgq6hdu7aOHj3q6DAAAACQhTATHQAAAAD+JyIiQv/5z39UpkwZ5c+fXwaDwWZ/v379HBQZAAAAHIUiOgAAAAD8z8SJE/Xrr7/q6tWr8vDwsNlnMBgoogMAAORAFNH/pUuXLqlx48aqUKGCJCkpKUmPP/64JkyYoICAgHT3ExwcrMWLF6to0aLpar9+/Xrt2rVLs2fPttkeGRmp8ePHa/78+Ro5cqTKlSunBg0aWLddvnxZZ86cUY0aNdJ/kvdx4cIF9ejRQ9WqVdPEiRMzfPxvv/2mSZMm6datW0pJSZEkjR49WqVKldKiRYtUtGhRPf/88xnu9845h4SEpKt9WFiYoqOj5efnZ7N98eLFqf5Y+rcy65oDAADAfrZv366PPvpIlStXdnQoAAAAyCIooj8EX19fLV++3Pp49uzZmjt3riIiIqzbzGazjMZHv/R8njx5NH/+/DS37d27V6dOnXrogu6hQ4dUsmTJf1VAl25/PLZDhw5q2rSpJGnbtm2aP3++FixYoN69ez9UbBk1bNgw1alT55H1n1nXHAAAAPbj7++vsmXLOjoMAAAAZCHcWDQTVaxYUefPn5d0e4b5zJkzrR/33LBhg9q1a6cuXbqod+/e+vvvv63HrV+/Xn369FHTpk21detWSbdnfHfp0kVhYWFq06aNNm7caG0fGxurIUOGqFOnTnr55ZcVFxenS5cuqWbNmjbx3Nl28eJFzZkzR5s2bdL8+fNVs2ZNxcXFSZJSUlJUp04dm3gk6ejRowoNDVWXLl3UqVMn/fjjjzp79qwWLlyoAwcOaOzYsTbtExISFB4ertDQULVr105Tpky57zWKjY3VjRs3rI+ff/55LViwQNLt2eSrVq3ShQsX1KxZM82ePVuhoaFq1qyZfv31V0nSf//7XzVv3lwvvfSSli1bpnr16qUaY9OmTerUqZO6du2qvn376urVq2lkLLWYmBhVrVpVCQkJkqTk5GTVrFlTkZGR2r17t0JDQ9W1a1d169bNmuu6detq2bJl6tGjhxo0aKDNmzfbXPP33nsv3eMDAADAscLDwzVnzhzFx8c7OhQAAABkEcxEzyRms1lfffWVKlWqZN2WL18+DRkyRH/++afefPNNbdmyRT4+Plq8eLHmzp2rSZMmSZK8vb21cOFCnTx5Uj169FCjRo10+fJl9ezZU/Xq1dMff/yhNm3aqGXLlpKkY8eO6euvv1bu3LnVv39/bdy4UbVr104ztgIFCqh169a6deuW+vXrpz///FNffPGF2rVrp3379ql06dLKmzevzTHDhw/XxIkTVblyZR05ckRDhw7VV199pd69e2vXrl2aMGGCTfsrV66oSpUqCg0NlcViUcOGDdW2bVs988wzNu2GDBmiYcOG6eOPP9Zzzz2n+vXrq2rVqjZtjEajzp49q0aNGik8PFyzZs3SunXrNHLkSI0bN07vvPOOSpUqpTfeeCPVLP/Lly9r4cKF+uSTT+Th4aGVK1fqnXfe0ejRo9OVR19fX1WuXFk7duxQo0aN9P333+vZZ59V7ty5NX78eK1atUoBAQHasWOHpkyZooULF8pkMkmSlixZou+++06zZs3S+vXrrde8Z8+e6RobyE5cXHLGe7Amk9HmK7Ivcuk8yKXzyKq5XLJkiX7//XctW7ZMfn5+qW4s+t133zkoMgAAADgKRfSHEBMTo7CwMEm3Z3RXrFjRZkmSihUrSrpd9C5Tpox8fHwkSdWrV9emTZus7WrVqiVJKlGihG7evKno6Gj5+/tryZIlWrx4sSQpKirK2r5cuXLKnTu3dYzTp08/sIh+r5CQEL3xxhtq166dNm/erPbt29vsv379uv766y/rOpBly5ZVZGSkYmNj0+zTz89Px48fV0hIiFxcXHT16lWbmO+oWbOm/vvf/2rv3r3at2+fRo0apWeffVZvv/22TTsfHx+VKlVK0u03I44cOaKoqChdv37dur1Ro0batm2bzXFHjx7V1atX1atXL0m316q/9w2CO2bMmGG9vpIUFBSk4cOHq0WLFtq6dasaNWqkLVu26MUXX9S5c+cUGRmpgQMHSrr9pklycrL12GrVqlljjY6OTvM6Ac7C39/L0SHYlY+Pp6NDQCYhl86DXDqPrJbLevXqydXV1dFhAAAAIAuhiP4Q7l0T/V53v/i2WCw23989g/ru7y0WiwwGgyZMmKAGDRqoa9euunbtmqpXr25tc/dsmDvtM6JMmTJKTEzUmTNn9NNPP2n8+PE2+++O9W4PWtt96dKlio2N1YoVK2Q0GtW8efP7touPj5enp6dq166t2rVrq0+fPqpVq1aq5WRcXGx/NC0WS6pzTeu8S5UqpSVLlqQZ6x1prYlev359TZ48WTExMfrhhx8UERGh8+fPK2/evGnm+954AWcXFRXn6BDswmQyysfHU7Gx8UpJMTs6HDwEcuk8yKXzyEgu7fnmbXh4uN3GAgAAQPZA5c8OgoKCFBERoZiYGPn6+mrXrl0qX768df/u3btVunRpnThxQj4+PvLz81N0dLSKFy8uSfrss89kNBqVmJgo6fZs6xs3bsjb21sHDx6877rg9zIYDNbjJalTp04aM2aMgoODrcuR3OHj46P8+fPrwIEDqly5sg4cOKD8+fPL29s7zf6jo6NVpEgRGY1GHThwQH/++afNeNLtmfsvvPCCVqxYYT23yMhIubu7y9/f/x/Pwd/fX25ubjp9+rSKFSumb775JlWboKAgvf766/r777+VN29ebdq0Sa6urmrUqNE/9n+Hu7u7atWqpdmzZ6tGjRry9PRUoUKFFBsbqxMnTqhkyZLas2ePzp49q86dO6fZz73XHHAmyck5q3CVkmLOcefsrMil8yCXziOr5XL+/Plp7ktJSbF+MhEAAAA5B0V0OwgMDNTQoUPVo0cPubu7y8/PTxMnTpR0+4X4zZs31adPH128eNF6w85evXpp/PjxypMnj0JDQ1WjRg0NHTpUtWvXVlBQkMaNG6fff/9dAQEBatGiha5cufLAGKpUqaLw8HB5enoqPDxczZs316RJkzRt2rT7tp82bZomT54so9Eoi8WSZrs72rdvr6FDh2rfvn2qUqWKevbsqalTp6p06dIKCAiQdHvm/ltvvaVx48bJYDDIxcVFBoNBCxYsSNdHZg0Gg1577TW9+uqrKlCggCpVqpTqDYDAwECNGTNGffr0kaenp9zd3dO8yem9y7lI0ogRI1SmTBm1aNFCPXr0sM5o9/Dw0MyZMzVmzBi5u7vLbDanWhf+XvdecwAAAGR9H3/8sc1js9ms6Ohoubu76+mnn6aIDgAAkAMZLGmt3QGn9s0332jDhg0PnGmTFX3zzTcqUaKEChQooM8//1yff/65Fi1a5OiwMk3C4OmODgFIl+uj+jo6BLtwcTHK399LUVFxWWqWJDKOXDoPcuk8MpLLPHly2ymq+7t586bmzZun0qVLp7lsYVYXGXndLuPwHM3eyF/2Rv7uz3PtFkeHkC4Gg0GuriYlJaWkucwusi5H5y++fVO7j+lM0vNak5noOdDAgQN17do1zZw509GhZJjZbFb//v3l5eWVrtngAAAAwMPKlSuXhg8frjZt2mTbIjoAAAD+PYroOdBbb73l6BD+tYYNG6phw4aODgMAAAA5jMFgUFRUlKPDAAAAgANQRAcAAACA/1m9enWqbUlJSdqxY4f8/f0dEBEAAAAcjSI6AAAAAPzPuHHjUm1zd3dXsWLFFBERYf+AAAAA4HAU0YEsxGPWcG4C4wS4oQ8AANnXiRMnHB0CAAAAshijowMAAAAAAAAAACCrYiY6AAAAgBwvLCxMBoPhgW0MBoOWLVtmp4gAAACQVVBEBwAAAJDjVa1aNc19ZrNZ69ev1+XLl+0YEQAAALIKiugAAAAAcrx+/frdd/vp06f12muvSZIWLlyYaePt379f3bt3T7U9MTFR3377rZ588knrtnXr1un111+Xq6urTdvt27fr8ccfz7SYAAAAcH8U0YEsJGHwdHk6OghkigTJKXN5fVRfR4cAAIBdpKSkaOHChVq0aJFat26t999/X97e3pnWf5UqVXT06FGbbStWrNCmTZuUP39+m+3Xr19XjRo19P7772fa+AAAAEg/iugAAAAAcJeff/5Zr732mm7duqX33ntPVapUeeRjXr16VXPnztWyZctSrc0eExMjX1/fRx4DAAAA7o8iOgAAAADo9lIqc+bM0fLlyxUWFqaBAwfK3d3dLmO/9dZbatSokUqWLJlqX2xsrM6fP682bdro/PnzevrppzVo0CDVrVs33f0bjQYZjQ++cWpmMJmMNl+RvZC/7I383d8/3TQ6q7gT5u2v2SNm/D9H58/Fhef9o0YRHQAAAECOt3//fo0ePVqenp76+OOPVbp0abuNfenSJX3++efaunXrfff7+fkpICBAQ4cO1dNPP601a9bo1Vdf1WeffaaiRYuma4yAAC+7FpJ8fJxxYbucg/xlb+TPVqKrydEhZIiLS/aKF7YclT8vfy+HjJuTUEQHAAAAkON17dpVAQEBatKkibZv367t27fft11aNyB9GCtWrFD9+vUVGBh43/39+/e3efzSSy9p06ZN2rhxo8LDw9M1xrVrcXabie7j46nY2HilpJgf+XjIXOQveyN/9+eWlOLoENLFYLhdgE1OTpHF4uhokFGOzl9cVJz9B3Ui/ul4E4IiOtLt0qVLaty4sSpUqCBJSkpK0uOPP64JEyYoICAgzeMuX76sM2fOqEaNGoqPj9eOHTvUuHHjTI+vRIkSqly5sozG//8IS9euXeXu7q4jR45k6A+emjVravXq1Tp79myGjwUAAED2U7lyZUnSwYMH02zzqGZyf/HFFxo9enSGjnnqqacUGRmZ7vZms0Vms/3+qk9JMSs5mSJedkX+sjfyZ8s121Skb/8fY7FIlmwTM/6fY/PHc/7Ro4iODPH19dXy5cutj2fPnq25c+cqIiIizWP27t2rU6dOqUaNGjp+/Li+/PLLdBfRzWazTVH8n7z//vv3XbeyTp066e7j3uP+7bEAAADIPu5+jWtPp0+f1l9//aVq1aql2ebdd99VUFCQatSoYd127ty5RzIxBQAAAKlRRMdDqVixopYuXSpJ2r59uxYsWCAPDw8lJiYqIiJC3t7emjNnjsxmszw9PfX5558rJiZGU6dO1YgRIzR9+nQdOXJEFotFpUqV0uuvv659+/bp7bfflq+vr6pWraorV67oxo0bunz5sn777TcVKVJEc+bMSfdMoPXr12vXrl2aPXu26tatq+7du2vnzp06f/68wsPD1axZM50/f17Dhg2Tl5eXzc2c0nPshQsXNHToULm6uqp69er69NNPtXTpUhUoUOBRXHIAAAA4kePHjytfvnzy9va22d64cWNNmjRJlStXVlRUlCZMmKCFCxcqf/78WrFihS5cuKA2bdo4KGoAAICchSI6/jWz2ayvvvpKlSpVkiTFxMRo8uTJKl68uDZs2KB33nlHc+fOVevWrXXr1i29+uqrypcvn3bt2qWRI0fqiy++0JUrV7RixQpJUnh4uL7++mv5+vrq6NGj2rZtmwICAjRv3jwdPnxYq1atksFgUIMGDXTq1CmVKFEiwzGbTLdv8LBkyRJ99913mjVrlpo1a6Z58+apZcuW6tKliw4fPqz3338/3cfOnTtXTZs21UsvvaSvvvpKv//+e7a5+zeQUTntjt8mk9HmK7Ivcuk8yKXzIJe3RUZGys/PL9X2c+fO6ebNm5KkwYMHy2w2q0uXLoqPj1eJEiW0dOnSNNdQBwAAQOaiiI4MiYmJUVhYmCQpJSVFFStWVO/evSVJuXPn1oQJE2SxWBQTE3PfPwbudujQIR05csTa340bN3Tp0iX5+vqqYMGCNuusV65cWa6urpKkvHnzKiYm5r59du/e3Wb5l8GDB6dqc+ejsvny5VN0dLQk6eTJk+revbskqXz58vLyuv8NBdI6tlu3bpKk559/Xi4uPK3gvNJzsw1n5OPj6egQkEnIpfMgl84jp+eyR48e6tGjR6rtJ0+etH7v5uam1157Ta+99po9QwMAAMD/UO1Dhty7JvodiYmJGjx4sFavXq2SJUtq69at1hnmaTEYDGrbtq21CH/H3r175ebmZrPt3sJ0WjdpuN+a6OfOnXtgX/frz2y+/w0Z0jr2zsxzg8HALHQ4tagcdsdvk8koHx9PxcbGKyWFG7VkZ+TSeZBL55GRXObUN3EBAACQNVBER6aIj4+X2WxWwYIFlZycrC1btigxMVHS7cLyne+NRqP1+0qVKun9999Xt27d5Orqqjlz5qhFixYOib9YsWL68ccfVapUKe3fv1/x8fHpPrZIkSLWY7dv367k5ORHGCngWDn1jt8pKeYce+7Ohlw6D3LpPMglAAAAsrqcvQAhMo2vr686duyoTp06qWfPnuratav++usvLV26VFWqVNGmTZs0e/ZsBQUF6aefftKoUaPUsGFDVapUSSEhIWrfvr2uXbumggULOiT+/v37a926dXrppZf05Zdf6umnn05ztvu9XnnlFa1evVphYWE6ceKE/P39WdIFAAAAAAAAcBIGS3orhQDu6+jRo7p165YqV66sP//8Uy1bttQPP/xgvRFpRiQMnv4IIgQyz/VRfR0dgl25uBjl7++lqKg4Zklmc+TSeZBL55GRXObJk9tOUTmvyMjrdhmH52j2Rv6yN/J3f55rtzg6hHQxGAxydTUpKSkl3ZP6kHU4On/x7ZvafUxnkp7XmkyXBR5Srly59MYbb0i6vTb8G2+88a8K6AAAAAAAAACyHorowEMqWrSoPv74Y0eHAQAAAAAAAOARYE10AAAAAAAAAADSwEx0AAAAAAAA2E12WaccAO5gJjoAAAAAAAAAAGlgJjqQhXjMGs6d1J2Ai4tR/v5e5BIAAAAAAMAJMBMdAAAAAAAAAIA0UEQHAAAAAAAAACANFNEBAAAAAAAAAEgDa6IDWUjC4OnydHQQyBQJklPm8vqovo4OAQAAAAAAwK6YiQ4AAAAAAAAAQBooogMAAAAAAAAAkAaK6AAAAAAAAAAApIEiOgAAAAAAAAAAaaCIDgAAAAAAAABAGiiiI1Ncu3ZNZcqU0bx582y2BwcH68yZMw/df3r6+fzzz5WcnJyu/vbs2aMWLVqk2v7RRx9p0KBBaR5XokQJ3bp1K11jAAAAAAAAAMj+KKIjU2zYsEGFCxfW+vXrZTabHRLDvHnzlJKSkq621apVU3x8vH7++Web7Z9++qnatWv3KMIDAAAAAAAAkA1RREem+OSTTzR06FC5ublpz549NvvWr1+vPn36qGnTptq6dask6YsvvlDbtm0VEhKijh076tSpU5JuF+PbtWunLl26qHfv3vr7779T9RUeHm59HB4ervXr12vu3Lk6f/68unfvrmvXrmn37t0KDQ1V165d1a1bN50/f96mH4PBoLZt2+rTTz+1bjt9+rSioqJUvXp1TZgwQR07dlSnTp00ZMiQVDPc582bpzfffNP6uEOHDtq7d68k6YMPPlCnTp0UFhamYcOGKT4+/t9eVgAAAAAAAAAO5uLoAJD9HTx4UNevX1etWrXUtm1bffLJJ6pZs6Z1v7e3txYuXKiTJ0+qR48eatSokd555x2NHTtWlStX1i+//KK//vpLuXPn1ptvvqktW7bIx8dHixcv1ty5czVp0qR/jGHAgAF6++239f7778tisWj8+PFatWqVAgICtGPHDk2ZMkULFy60OaZNmzZq3bq1RowYITc3N61fv15t2rTRjRs3lD9/fo0ePVpGo1Fdu3bVrl27VL9+/X+M48iRI/r222+1cuVKGY1GzZgxQx9//LG6deuW8QsLZEEuLjnrvVeTyWjzFdkXuXQe5NJ5kEsAAABkFxTR8dA++eQTtW7dWiaTSW3atNELL7yg6Oho+fn5SZJq1aol6fZ64jdv3lR0dLSaNWumUaNGqUmTJgoODladOnX0zTffqEyZMvLx8ZEkVa9eXZs2bcpwPOfOnVNkZKQGDhwoSTKbzfddKz0wMFBly5bV9u3b1aBBA23evFmrVq2Sl5eXrly5os6dO8vFxcU6Qz09Dh06pIsXL+o///mPJCk+Pl4VK1bM8DkAWZW/v5ejQ3AIHx9PR4eATEIunQe5dB7kEgAAAFkdRXQ8lBs3bmjr1q164okntGPHDkmS0WjU559/rrCwMOvjOywWiwwGg15++WW1atVKO3fu1NixY9WkSRMVL15cFovFpu3dx0q3l2G52/3WQDcYDMqbN6+WL1/+j/G3a9dOn3zyiXLlyqVixYopf/782rBhgw4fPqwPP/xQbm5uevnll+87xt3uFOkNBoPq1KmjCRMm/OPYQHYUFRXn6BDsymQyysfHU7Gx8UpJccz9HpA5yKXzIJfOIyO5zKlv4gIAACBroIiOh7Jp0yZVqlRJ7733nnXb9u3b9dZbb1mL6Lt371bp0qV14sQJ+fj4WJdtGTBggNq3b69cuXLp008/VZs2bRQREaGYmBj5+vpq165dKl++vM14vr6+unr1qiQpKSlJx44dU7169STdLmAnJiaqUKFCio2N1YkTJ1SyZEnt2bNHZ8+eVefOnVPFX69ePU2ePFlr165V+/btJUnR0dEqUKCA3NzcdObMGR07dizVUi6+vr76/fffJUkxMTH67bffJEkVK1bUsmXLdOPGDXl7e2vZsmUqXbq0Kleu/NDXGsgKkpNzZsEqJcWcY8/d2ZBL50EunQe5BAAAQFZHER0P5ZNPPtErr7xis61u3bqaOHGijh07ppSUFN28eVN9+vTRxYsXNXbsWJlMJj3++OMKCQlRrly5lJSUpBEjRigwMFBDhw5Vjx495O7uLj8/P02cONGm7xo1auiDDz5Q//799dhjj+nZZ5+V2Xz7j646deooNDRUc+fO1cyZMzVmzBi5u7vLbDanOTPcxcVFzZs315o1a6w3Cm3SpIk2btyokJAQFS9eXOHh4VqwYIGqVKliPa5x48bauHGjhgwZooCAAJUqVUpms1lBQUHq2rWrunbtKjc3N+XNm1dt27bNzEsOAAAAAAAAwI4MlrvXzwDgUAmDpzs6BOCBro/q6+gQ7MrFxSh/fy9FRcUxSzKbI5fOg1w6j4zkMk+e3HaKynlFRl63yzg8R7M38pe9Zaf8ea7d4ugQshyDwSBXV5OSklJEqS77cXT+4ts3tfuYziQ9rzWN/9gCAAAAAAAAAIAciiI6AAAAAAAAAABpoIgOAAAAAAAAAEAaKKIDAAAAAAAAAJAGF0cHAOD/ecwani1uAoMHy0439AEAAAAAAMCDMRMdAAAAAAAAAIA0MBMdAAAAAAAAALIpz7Vb7DZWfPumdhsrK2EmOgAAAAAAAAAAaaCIDgAAAAAAAABAGljOBchCEgZPl6ejg0CmSJCcLpfXR/V1dAgAAAAAAAB2x0x0AAAAAAAAAADSQBEdAAAAAAAAAIA0UEQHAAAAAAAAACANFNEBAAAAAAAAAEgDRfRHICoqSqNGjdKLL76oTp06KSQkRF9++WW6jt25c6fCwsIyNF54eLj++OOPfxOqjZEjR6pRo0YKCwtTly5d1KZNG23cuPFf9bVo0SJt27ZNkvT5558rOTlZkhQWFqbExMRMi7NNmzbq1q2b9u3b96/6ioyMVL9+/R4qHkm6dOmSatas+dD9AAAAAAAAAMhaXBwdgDPq37+/GjZsqClTpkiSLl++rF69eslisahx48bWdhaLRQaD4aHGslgsmj179kP1cbeXXnpJISEhkqSrV6+qefPmqlq1qgIDAzPUT+/eva3fz5s3Tw0bNpSLi4uWL1+e6XEeO3ZMr776qmbNmqWKFStmqJ88efJo/vz5mRITAAAAAAAAAOdDET2T/fDDD4qPj1fXrl2t2wIDAzV8+HDNmjVLjRs3VlhYmEqUKKHTp09r4cKF+uyzz7R8+XIVLFjQplh98eJFjR8/XomJiUpMTFTfvn1Vt25djRw5Um5ubrp06ZIiIiL00ksvafHixTp06JD27t0rSTp79qy8vLy0ePFieXh4aP78+fryyy/1xBNPqEyZMvr99981ffr0B57LY489pieffFKXLl1Snjx5NHXqVP30008yGAwqWbKkxowZo4SEBA0bNkzR0dG6efOmatasqWHDhmnkyJEqV66cIiMjdf78eXXv3l3z5s1T9erVtWvXLrVo0UI7duyQh4eHkpOTVbduXW3YsEG//vqr5s+fLxcXF5lMJkVERKhgwYIPjLN06dLq27evFi1apIULF+r48eOaOnWqJMlsNmv48OEqW7asfvvtN0VERCglJUUWi0VvvPGGTCaTOnbsqO+//17Dhg3T448/rt9//10///yzBg8erB07dujEiRMqVaqUpk2bpoSEBI0aNUqXL19WYmKiKlWqpFGjRtnE88UXX+i9996Tm5ubzGazJk6cqGeeeSZDP0cAAAAAAAAAsgaK6Jns1KlTKlOmTKrtZcuW1alTp2SxWCRJ7u7uWrp0qW7cuKFZs2bpiy++UEBAgMaOHWs9ZuLEierRo4eqV6+ua9euqV27dtq6daskKTY2Vu+//77NGCaTSQcOHNDmzZvl5eWlTp06affu3SpSpIhWr16tr776Si4uLurUqZOKFy/+j+dy5swZ/f333ypevLi2bt2q06dPa9WqVTIYDHr55Ze1efNmeXp66ubNm1q5cqUk6cMPP1RCQoK1jwEDBujtt9/W+++/L3d3d0mSr6+vKleurB07dqhRo0b6/vvv9eyzzyp37twaP368Vq1apYCAAO3YsUNTpkzRwoUL/zHWcuXK6d1335UkjRgxQnPnzlXhwoV15swZDRo0SJ9//rkmTZqkLl26qEGDBtq8ebO++OILNW/e3Ob6/f3335o7d642btyocePG6ZtvvpGXl5eqVq2qYcOGKSEhQVWqVFFoaKgsFosaNmyotm3bKleuXNZ+3nnnHY0dO1aVK1fWL7/8or/++osiOpyCi0vOWwHMZDLafEX2RS6dB7l0HuQSAAAA2QVF9EyWlJRkLZTfzWw2y2AwWJdvqVChgiTpt99+U2BgoAICAiRJtWrV0rlz5yRJhw4dUlxcnBYsWCBJcnV1VWRkpM3x9woKCpKXl5ckKV++fIqJidHJkydVunRpeXp6SpKCg4N18eLF+x6/dOlSbdmyRRaLRR4eHnrrrbfk4+OjH3/8UbVq1ZLRePuPnGrVquno0aPq1auXZsyYoQEDBqhOnTpq1aqVPDw8/vE6tWjRQlu3blWjRo20ZcsWvfjiizp37pwiIyM1cOBA6zW7s5b6PzEYDHJzc1NsbKzOnTtn82ZEQkKCkpKSdPjwYc2cOVOS1KxZM0m31zK/253rGhgYqIIFC8rf31+S5O/vr9jYWOXNm1fHjx9XSEiIXFxcdPXqVUVFRdkU0Zs1a6ZRo0apSZMmCg4OVp06ddJ1DkBW5+/v5egQHMbHx9PRISCTkEvnQS6dB7kEAABAVkcRPZM9++yz970Z59GjR1W6dGnrYzc3N0lKVXBPSUmxfm8wGDRnzhzlyZMnVX93jr+Xi4ttSi0WS6q11+8Uwu/n7rXG73V3rBaLRUajUXnz5tWWLVt06NAhbdu2TXPnztWGDRvS7P+O+vXra/LkyYqJidEPP/ygiIgInT9/Xnnz5v1X66bv379fQUFBMhqNMhqNafZhNpsf2I/JZLJ+f79ruXTpUsXGxmrFihUyGo02M9nvePnll9WqVSvt3LlTY8eOVZMmTdS3b98MnxOQ1URFxTk6BLszmYzy8fFUbGy8UlIe/PsDWRu5dB7k0nlkJJc5+Y1cAAAAOB5F9ExWtWpVubm5adGiRdaba16+fFkzZszQ4MGDU7UvWLCg/vzzT0VFRcnf3187d+607qtUqZK2bt2qsLAwXblyRQsWLLCZYZ1eRYoU0S+//KLExEQZjUZt375dRYoUyVAfFStW1Jo1a9StWzdJ0vfff6/27dvru+++U2JiooKDg/Xcc89p586dqWa5GwwGJSYmWpdzkW4vZ1OrVi3Nnj1bNWrUkKenpwoVKqTY2FidOHFCJUuW1J49e3T27Fl17tz5gbGdPHlS7733nt599115e3urYMGC+vbbbxUcHKzTp09r8+bNGjhwoMqXL6/vvvtOLVq00LZt27Rnzx699NJLGboO0dHRKlKkiIxGow4cOKA///xTiYmJ1v0pKSmaPXu2BgwYoPbt2ytXrlz69NNPMzQGkFUlJ+fcYlVKijlHn78zIZfOg1w6D3IJAACArI4ieiYzmUx67733NGPGDLVu3Vru7u4yGAwaMGCA6tWrl6q9j4+PBgwYoLCwMD355JMqVqyYLly4IEkaPXq0xo4dq61btyohIcFalM+oZ555Ri+88ILatGmjggULqmzZsoqPj89QH40aNdKPP/5onaVeoUIFNW7cWJcvX9aIESO0ZMkSJScnq3r16goKCtKqVausx9apU0ehoaGaO3euTZ8tWrRQjx49tGTJEkmSh4eHZs6cqTFjxsjd3V1ms1kTJky4bzx3lp1JTk6Wi4uLZs2apRIlSkiSpk2bpkmTJmnJkiVKSkrS8OHDJd2+nuPGjdPq1aslSW+88UaGroEktW/fXkOHDtW+fftUpUoV9ezZU1OnTrXeyNRkMunxxx9XSEiIcuXKpaSkJI0YMSLD4wAAAAAAAADIGgyW+y3gDaeSnJysTz/9VC1btpS7u7vGjBmjggULqmfPno4ODfdIGDzd0SEAabo+KuctS+TiYpS/v5eiouKYJZnNkUvnQS6dR0ZymSdPbjtF5bwiI6/bZRyeo9kb+cveslP+PNducXQIWY7BYJCrq0lJSSn3vdcesraclL/49k0dHUKmS89rTWai5wAuLi76448/1L59e3l5eemxxx7TyJEjHR0WAAAAAAAAAGR5FNFziIEDB2rgwIGODgMAAAAAAAAAshWjowMAAAAAAAAAACCroogOAAAAAAAAAEAaKKIDAAAAAAAAAJAG1kQHshCPWcOzxZ3U8WAuLkb5+3uRSwAAAAAAACfATHQAAAAAcID69eurTJkyCgoKsv6bOHHifdsuW7ZM9evXV9myZdW+fXsdO3bMztECAADkXMxEBwAAAAAHiI2N1YcffqiKFSs+sN3XX3+tOXPm6J133lG5cuW0ZMkSvfzyy/rqq6+UK1cuO0ULAACQczETHQAAAADsLCUlRXFxcfL19f3HtmvXrlW7du1UrVo1eXp66tVXX5XBYNC2bdvsECkAAACYiQ4AAAAAdhYbGyuLxaJ58+bpwIEDkqR69epp5MiR8vb2tml7/PhxNW3a1PrYYDCoZMmSOnbsmFq0aJGu8YxGg4xGQ+adQBpMJqPNV2Qv5C97y075Mxge/e+j7ObOJbn9leuT3eSk/Lm4ZP3fMY8CRXQgC0kYPF2ejg4CmSJByra5vD6qr6NDAADA6SUmJqp06dIKCgrSpEmT9Pfff2vgwIGKiIjQm2++adM2KipKfn5+Ntt8fX117dq1dI8XEOBl16KVj092fSUEifylR+J76+w2llvPthlqnx3yl+hqcnQIWZaLC9cmO8sJ+fPy93J0CA5BER0AAAAA7CwwMFDr16+3Pvb29tbQoUPVp08fTZ48WW5ubtZ9aRW/M1IUv3Ytzm4z0X18PBUbG6+UFPMjHw+Zi/yln1tSit3GiouKS1e77JQ/e16/7MJguF2ATU5OkcXi6GiQUTkpf+n9nZSd+KfjjQGK6AAAAACQBTz11FMym826evWq8uXLZ93u7++v6Ohom7ZRUVF65pln0t232WyR2Wy/v+pTUsxKTs7aRTykjfz9M1c7VskymovskD97Xr/s4/YbnRaLZOH6ZEM5J39Z/ffLo5IzF7EBAAAAAAc6ceKEpk6darPt7NmzcnNzU968eW22BwUF6eeff7Y+TklJ0fHjx1W2bFm7xAoAAJDTUUQHAAAAADvz9/fX6tWrtWjRIiUmJuq3337TW2+9pZCQEJlMJjVu3Nh6w9FOnTpp3bp1+uGHHxQXF6dZs2bJw8NDwcHBDj4LAACAnCHbFtEvXbqkEiVKaPXq1TbbDx48qBIlSmjv3r0PPD44OFhnzpyRJG3YsEGS9MsvvygiIuKBx82bN896o5+wsDAlJiZmOPbLly9r9+7dkqT169enOgdHOHPmzH1fhK9fv17Vq1dXWFiYOnTooPbt22vNmjXW/YsWLdK2bdvS7Pdh96fX3r171aFDh39sdyfXAAAAgCMFBgZaXwtXrVpV3bt3V7169TR06FBJ0rlz53Tz5k1JUp06dTR8+HCNGjVK1atX148//qhFixbJ3d3dkacAAACQY2TrNdELFSqkjRs3qmPHjtZtGzduVKFChdLdx+XLl7Vy5Uq1atVKzz777D8W0e+2fPnyDET7//bu3atTp06pRo0aatOmzb/qw56qVaum2bNnS5KuXLmiQYMGKSYmRr169VLv3r0feOzD7s9MKSkpmjdvnlq1avXIxrBYLBm6wRMAAAByripVqqQ5oebkyZM2j0NCQhQSEmKPsAAAAHCPbF1Ez5s3r5KSknTx4kUVKFBASUlJOnDggMqVKyfp9mz1jh076vvvv5ckrVq1Sj/99JPN2oNDhgzR6dOnNWTIEHXo0EEzZ87UmjVrFBoaqnLlyunnn3/W1atXFRISorCwMJvxS5QooSNHjiglJUVjxozR5cuXlZCQoH79+qlevXravn27FixYIA8PDyUmJioiIkLe3t6aM2eOzGaz/Pz8FB8fr1u3bmno0KHauXOn5s+fLzc3NxkMBkVERKho0aIKDQ1V3bp1dfDgQZ07d06hoaHq1q2bTSwJCQkaNWqULl++rMTERFWqVEmjRo3ShQsX1LdvXzVo0ED79+9XTEyM5syZo+LFi2v79u2aOXOmnnrqKRUpUiRd1/zxxx/XxIkT1bFjR3Xv3l2vv/66ypUrpz179ig4ONhaoJ40aZLy5cunX3/9VeXKlVPLli01bNgwRUdH6+bNm6pZs6aGDRumkSNHqly5cgoJCdGGDRv00UcfycPDQ7ly5dKkSZOUN29e1a1bV927d9fOnTt1/vx5hYeHq1mzZmnGOGfOHN24cUOXL1/Wb7/9piJFimjOnDl67bXXdPnyZf3nP//RsmXLtGnTJn300Udyc3OTl5eXJk2apMcee0xr167V0qVLFRgYqGrVqmnHjh1asWKFLl68qPHjxysxMVGJiYnq27ev6tatq5EjR8rNzU2XLl1SRESEPvnkE+3evVsGg0GPP/64ZsyYIW9v73RdXwAAAAAAAABZS7YuoktSixYt9Nlnn6lfv37asWOHatSooevXr6f7+P79+2vmzJmaOXOmzRIwJpNJiYmJWr58ua5du6ZGjRrpxRdfvG8fH330kQICAjRz5kydO3dOs2fPVr169RQTE6PJkyerePHi2rBhg9555x3NnTtXrVu31q1bt9SzZ0/NmzdPkhQfH69Ro0bp448/VoECBbRlyxZNmjRJH3zwgUwmk65evapFixbp3Llz6tSpU6oi+pUrV1SlShWFhobKYrGoYcOGatu2rXLlyqWzZ8+qUaNGCg8P16xZs7Ru3TqNHDlSERERWrx4sZ555hktWrQo3descOHCslgs+uuvv6zbWrZsqU8++UStWrWS2WzW119/rTVr1lhnsO/evVs3b97UypUrJUkffvihEhISrMf/+eefevPNN7Vlyxb5+Pho8eLFmjt3riZNmiSTySRJWrJkib777jvNmjXrgUV0k8mkw4cPa9WqVTIYDGrQoIFOnTql/v3767vvvtOyZct0+fJlLVy4UJ988ok8PDy0cuVKvfPOOxo0aJBmzJihzZs3K0+ePHrllVdkNN5e9WjixInq0aOHqlevrmvXrqldu3baunWrJCk2Nlbvv/++YmJitHTpUh06dEguLi765ptvdPnyZYroyFZcXLLtSl+ZzmQy2nxF9kUunQe5dB7kEgAAANlFti+iN23aVJ07d1a/fv20ceNG9erVSytWrMiUvqtVqyZJCggIUL58+XThwoX7tjt8+LBat24t6XaBee7cuZKk3Llza8KECbJYLIqJiZGfn1+aY/32228KCAhQgQIFJEnVq1fX2LFjU8WSP39+RUdHpzrez89Px48fV0hIiFxcXHT16lVFRUUpV65c8vHxUalSpSRJ+fLl05EjRxQVFaUbN27omWeekSTVrFlTH3/8cbqvjdFolJubm/VxnTp1FBERoevXr+vYsWMqWrSoAgMDrfvLlSunGTNmaMCAAapTp45atWolDw8P6/5jx46pTJky8vHxsZ7/pk2bUp1/vnz57nv+96pcubJcXV0l3f7EQkxMjLy8vKz7jx49qqtXr6pXr16SpKSkJOXNm1e//fabAgMDlSdPHklSw4YNtW7dOknSoUOHFBcXpwULFkiSXF1dFRkZKUmqUKGCJMnX11fPPfecOnfurBdeeEGNGjWy5hTILvz9vf65UQ7j4+Pp6BCQScil8yCXzoNcAgAAIKvL9kV0f39/FSxY0LrUR1BQkHXfvWtTp6SkZKhvi8Vi8/2dGcn3a2c2m222JSYmavDgwVq9erVKliyprVu3/mNx/0Hjubg8OFVLly5VbGysVqxYIaPRqObNm6d5rMVisRlLyti1OXXqlDw9PfX4449bt7m5uSk4OFjbtm3ToUOHUs3az5s3r7Zs2aJDhw5p27Ztmjt3bqqbfD7M+d/rfud8r1KlSmnJkiU2244ePWrzc3N3DAaDQXPmzLEW2O929xsK7733nk6ePKmdO3cqNDRUM2bMsL4JAGQHUVFxjg4hyzCZjPLx8VRsbLxSUsz/fACyLHLpPMil88hILnmDFwAAAI6U7YvokvTiiy9q+vTpatKkic12X19fxcXFKTExUW5ubjp8+HCq4qrRaFRiYuJ9+929e7caNmyoa9eu6e+//9bTTz9933YVKlTQd999p0aNGunvv//WgAED9O6778psNqtgwYJKTk7Wli1brOMYDIZUYxYuXFjR0dHW9d137dql8uXLp/saREdHq0iRIjIajTpw4ID+/PPPNM9Luv3mg6enp3799VcVL15cO3fuTPc448aNU9++fVO9SdGyZUstW7ZMv/zyi0aOHGmz77vvvlNiYqKCg4P13HPPaefOnbp48aJ1f1BQkCIiIhQTEyNfX98Mn3963J3roKAgvf766/r777+VN29ebdq0Sa6urqpatap+//13RUVFyd/fX9988431+EqVKmnr1q0KCwvTlStXtGDBAptPC0jSxYsXtW3bNr300ksqUaKELl68qCNHjlBER7aSnExR6l4pKWaui5Mgl86DXDoPcgkAAICszimK6MHBwRo7dqxatmxps93b21sdO3ZUt27dVLx4cfn5+Sk2NtamTbFixRQbG6tu3bqpT58+qfru1q2bIiMjNWTIkDTXte7SpYvGjBmj0NBQJSYmql+/fvL19VXHjh3VqVMn+fv7q1+/fhoyZIiWLl2qKlWqKDw8XJ6entaivoeHh6ZMmaLBgwfL3d1dbm5uGj9+fLqvQfv27TV06FDt27dPVapUUc+ePTV16lSbm6jezWAwaPTo0Ro0aJCefPJJlSlTJtVs+jt++OEHhYWFyWw269atWwoJCVHbtm1TtbtzM9OKFSsqV65cNvuKFSumESNGaMmSJUpOTlb16tUVFBSkVatWSZICAwM1dOhQ9ejRQ+7u7vLz89PEiRPTff7pkTdvXuXPn1/t2rXT0qVLNWbMGPXp00eenp5yd3fXlClT5Ofnp1deeUWhoaF6+umnVbJkSevPzOjRozV27Fht3bpVCQkJ6t27d6oxnnjiCZ08eVIdO3aUq6urPDw8NGjQoEw9DwAAAAAAAAD2Y7Dcb50LKCwsTL169VKdOnUcHQrsbMOGDapbt678/f21cOFCXb58WePGjbPL2AmDp9tlHOBBro/q6+gQsgwXF6P8/b0UFRXHLMlsjlw6D3LpPDKSyzx5ctspKucVGXndLuPwHM3eyF/6ea7dYrex4ts3TVe77JQ/e16/7MJgMMjV1aSkpJT7LkmLrC0n5S+9v5Oyk/S81nSKmehAZrp586b+85//KHfu3HJzc0tzNj8AAAAAAAAA50cRPQ3Lly93dAhwkNDQUIWGhjo6DAAAAAAAAABZgNHRAQAAAAAAAAAAkFVRRAcAAAAAAAAAIA0s5wJkIR6zhmeLm8DgwbLTDX0AAAAAAADwYMxEBwAAAAAAAAAgDRTRAQAAAAAAAABIA0V0AAAAAAAAAADSQBEdAAAAAAAAAIA0cGNRIAtJGDxdno4OApkiQcoWubw+qq+jQwAAAAAAAMjSmIkOAAAAAAAAAEAaKKIDAAAAAAAAAJAGiugAAAAAAAAAAKSBIjoAAAAAAAAAAGmgiA4AAAAAAAAAQBpcHB0A4EhvvPGGTpw4oVu3bun48eOqUKGCJKl58+bq2LGjTdt58+bp1q1bGjp0qM32vXv3aubMmVqzZo3d4gYAAAAAAABgHxTRkaO9/vrrkqRLly6pY8eOWr58uYMjAgAAAAAAAJCVUEQH7vHTTz9p8uTJcnNz082bNzVkyBDVqFFD0u1i+4ABA3Tp0iUVK1ZMU6dOtTn24sWLGj9+vBITE5WYmKi+ffuqbt26jjgNAAAAAAAAAJmAIjpwjytXrmj48OGqVKmS9u/frylTpmj9+vWSpNOnT2v9+vVycXFRhw4d9N1338nd3d167MSJE9WjRw9Vr15d165dU7t27bR161a5ubk56nSAB3Jx4dYYD2IyGW2+Ivsil86DXDoPcgkAAIDsgiI6cA8/Pz8tWLBAt27dUkJCgqKjo637nnvuOWtBvHz58jp9+rRKly5t3X/o0CHFxcVpwYIFkiRXV1dFRkbqySeftOs5AOnl7+/l6BCyBR8fT0eHgExCLp0HuXQe5BIAAABZHUV04B7Dhw/Xa6+9pueff15Hjx7VwIEDrfuMxv+fKWWxWGQwGGyONRgMmjNnjvLkyWO3eIGHERUV5+gQsjSTySgfH0/FxsYrJcXs6HDwEMil8yCXziMjueRNXwAAADgSRXTgHtHR0SpevLgkadOmTUpMTLTu279/v5KSkmQymfTjjz+qQYMGNsdWqlRJW7duVVhYmK5cuaIFCxZo7Nixdo0fyIjkZApQ6ZGSYuZaOQly6TzIpfMglwAAAMjqWIAQuEffvn3Vt29fhYWFqV69evL19dXUqVOVkpKiEiVKaMiQIWrXrp2effZZVatWzebY0aNHa/v27ercubNefvllVa1a1UFnAQAAAAAAACAzMBMdkPTUU0/p+++/lyT17NlTPXv2tO7bvHnzA4+tWrWq1qxZY+3n/ffff3SBAgAAAAAAALArZqIDAAAAAAAAAJAGiugAAAAAAAAAAKSBIjoAAAAAAAAAAGmgiA4AAAAAAAAAQBq4sSiQhXjMGq6oqDglJ5sdHQoegouLUf7+XuQSAAAAAADACTATHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRAQAAAAAAAABIA0V0AAAAAAAAAADSwI1FgSwkYfB0eTo6CGSKBCnL5fL6qL6ODgEAAAAAACDbYSY6AAAAAAAAAABpoIgOAAAAAAAAAEAaKKIDAAAAAAAAAJAGiugAAAAAAAAAAKSBG4tmM5cuXVLjxo1VoUIFSVJSUpIef/xxTZgwQQEBAWket2HDBrVq1Up79+7VzJkztWbNGnuF/EisX79en376qSTp8OHDKlGihDw9PVW0aFE1adLEKc4RAAAAAAAAgONRRM+GfH19tXz5cuvj2bNna+7cuYqIiLhv+8uXL2vlypVq1aqVfQK8i9lsltGY+R94aNOmjdq0aSNJCg4O1rRp01S0aFFJ0t69ezN9PEmyWCwyGAyPpG8AAAAAAAAAWRNFdCdQsWJFLV26VGvXrtXBgwc1depUSdL27du1atUq3bx5U6dPn9aQIUPUoUMHpaSkaOLEiTp27Jhu3bqlRYsWKU+ePFq7dq1Wr14tT09PWSwWTZ8+Xfnz59eRI0c0ZswY+fj4qEmTJjbHjhs3ThcvXlRKSorq1Kmjfv36ae/evXr77bfl6+urqlWrql27dpowYYIuXryopKQkBQcHq3fv3kpISNCoUaN0+fJlJSYmqlKlSho1apQuXLigvn37qkGDBtq/f79iYmI0Z84cFS9ePN3XJK1z3L17t+bPny8XFxeZTCZFRESoYMGCOnr0qKZMmSKj0ajk5GSNGDFCFSpU0MiRI+Xm5qZLly4pIiJCN27csF5fs9ms4cOHq2zZsgoNDVW1atV04sQJnTx5UqNGjdLGjRt16tQpNWrUSOHh4Y8k9wAAAAAAAAAeLYro2ZzZbNZXX32lSpUqqVmzZpo7d65u3Lghb29vbdmyRe3bt5ePj49mzpypmTNnau/evTp79qzefvttPfHEExoyZIi2bt2qsLAwxcfH6+2331ZgYKDmz5+v5cuXa8SIEZoyZYr69eunF154QUuXLrXOLl++fLlcXV21atUqJSUlqV27dqpevbok6ejRo9q2bZsCAgL03nvvKW/evJo8ebLMZrNCQ0NVo0YN+fn5qUqVKgoNDZXFYlHDhg3Vtm1b5cqVS2fPnrUWn2fNmqV169Zp5MiR6b4u9zvH9u3ba/z48Vq1apUCAgK0Y8cOTZkyRQsXLtTw4cM1ceJEVa5cWUeOHNHQoUP11VdfSZJiY2P1/vvvS5JatGihuXPnqnDhwjpz5owGDRqkzz//XCaTScnJyVqwYIHeeecdTZ06VVu2bFFUVJQaNmxIER1ZgosLt8HIKJPJaPMV2Re5dB7k0nmQS8DxPNdusXlsMBiU6GqSW1KKXC2WTBsnvn3TTOsLAABHoIieDcXExCgsLEzS7RnXFStWVO/eveXm5qYXXnhBX3zxhVq2bKkjR45oypQpOnjwoM3xhQsX1hNPPCFJypcvn2JiYiRJuXLl0uDBg2UwGBQZGWldd/3EiROqUqWKJKlRo0aaMmWKpNtrkb/44ouSJFdXV1WuXFlHjx7Vs88+q4IFC1rXaD906JAuXrxojSMuLk4XLlxQoUKFdPz4cYWEhMjFxUVXr15VVFSUcuXKJR8fH5UqVcoa45EjRzJ0je53jufOnVNkZKQGDhwo6fYbEMnJybp+/br++usvVa5cWZJUtmxZRUZGKjY2VpKs1yE2Nlbnzp3T2LFjreMkJCQoKSnJpl1gYKBKlSolNzc3BQYGKiEhQYmJiXJzc8vQOQCZzd/fy9EhZFs+Pp6ODgGZhFw6D3LpPMglAAAAsjqK6NnQvWui361Tp04aP368fH191aBBA7m4pE7xvdssFosuXbqkqVOnatOmTXriiSe0ZMkS/frrr9Y2d9YCv3d9c8tdsxMsFot1/90FY4PBoD59+qhZs2Y2x86fP1+xsbFasWKFjEajmjdv/sAYM+J+xxsMBuXNmzfVtbtTLL/XvediNBqtM/Dvx2Qy3ff7fxM/8ChERcU5OoRsx2QyysfHU7Gx8UpJMTs6HDwEcuk8yKXzyEgueSMYAAAAjkQR3ck888wzMhgM+uCDDzR58mRJt4u/iYmJDzwuNjZWXl5eeuKJJxQXF6dt27ZZZ3IXLlxYhw8fVt26dfX1119bj6lYsaJ27typxo0bKzExUfv27VOrVq0UHx9v03elSpX05ZdfqlmzZjKbzZo0aZL69eun6OhoFSlSREajUQcOHNCff/75j3E+jEKFCik2NlYnTpxQyZIltWfPHp09e1adO3dW/vz5deDAAVWuXFkHDhxQ/vz55e3tbXO8t7e3ChYsqG+//VbBwcE6ffq0Nm/ebJ3ZDmR1yckUm/6tlBQz189JkEvnQS6dB7kEAABAVscChE7oxRdflIuLiwoXLixJKlasmGJjY9WtW7c0j3n22WdVrlw5tW3bVoMHD1b//v21b98+bdmyRcOGDdPUqVPVvXt3JScnW2dZd+7cWcnJyQoJCVHnzp3VunVrlS1bNlXfnTt3lre3tzp06GBdoz0gIEDt27fXtm3bFBoaql27dqlnz56aOnWqoqKiHsl18fDw0MyZMzVmzBh16dJF8+bNU9WqVSVJ06ZN06xZs9SlSxfNnj1b06ZNu28f06ZN03vvvafOnTvrtddeU82aNR9JrAAAAAAAAACyBoOFdSacisViUf/+/dW6dWs9//zzmdLnDz/8ID8/P5UsWVKHDh3SmDFjtHnz5kzpG7YSBk93dAhwYtdH9XV0CNmOi4tR/v5eioqKY5ZkNkcunQe5dB4ZyWWePLntFJXzioy8bpdxeI5mL/e7sairq0lJSSmZuiSlM95Y9N5r9yil9/plp+efPa9fdvGonn+wj5yUP2f8nZ6e15os5+JEjh8/rrFjx6patWqZVkCXbq8v/tprr8nDw0NJSUkaP358pvUNAAAAAAAAAFkZRXQnUqpUKX3yySeZ3m/lypW1fv36TO8XAAAAAAAAALI61kQHAAAAAAAAACANFNEBAAAAwAEuXbqkvn376rnnnlP16tU1fPhwxcTEpGq3bt06lSxZUkFBQTb/rly54oCoAQAAch6K6AAAAADgAH379pWfn5+2b9+ujRs36ty5c5o+PfWN5q9fv64aNWro6NGjNv8ef/xxB0QNAACQ87AmOpCFeMwani3upI4Hc3Exyt/fi1wCAIA0Xb9+XWXKlNHQoUPl5eUlLy8vtWrVSh9++GGqtjExMfL19XVAlAAAAJAoogMAAACA3eXOnVtTpkyx2fb7778rX758qdrGxsbq/PnzatOmjc6fP6+nn35agwYNUt26ddM9ntFokNFoeOi4/4nJZLT5iqzNYDDc8/jur5n38+Li4nw/D/deu0cpvdcvOz3/7Hn9sotH9fyDfeSk/Dnj7/T0oIgOAAAAAA529OhRrVixQnPnzk21z8/PTwEBARo6dKiefvpprVmzRq+++qo+++wzFS1aNF39BwR42bVo5ePjabex8O8lupruu93F5f7b/y0vf69M7S8rSOvaPQoZvX7Z4flnz+uX3WT28w/2lRPy54y/09ODIjoAAAAAONDBgwfVt29fDR8+/L6zy/v372/z+KWXXtKmTZu0ceNGhYeHp2uMa9fi7DYT3cfHU7Gx8UpJYVm7rM4tKcXmscFwuwCUnJwiiyXzxomLisu8zrKIe6/do5Te65ednn/2vH7ZxaN6/sE+clL+nPF3un863higiA5kIQmDpyvrzxlAeiRIds3l9VF97TgaAADILNu3b9ewYcM0fvx4NWvWLN3HPfXUU4qMjEx3e7PZIrPZfn/Vp6SYuTdMNuCaqtJz+40Wi0WyZGIVyBl/FlJfu0cno9cvOzz/7Hn9so9H8/yDveSc/GX13y+PSs5cxAYAAAAAHOzQoUMaMWKE5s6d+8AC+rvvvqvdu3fbbDt37pwKFCjwqEMEAACAKKIDAAAAgN0lJydr9OjRGjhwoGrUqJFqf+PGjXXgwAFJUlRUlCZMmKDffvtNiYmJ+uCDD3ThwgW1adPG3mEDAADkSCznAgAAAAB2dvjwYZ05c0ZTp07V1KlTbfZt3bpV586d082bNyVJgwcPltlsVpcuXRQfH68SJUpo6dKlCgwMdEToAAAAOQ5FdAAAAACws8qVK+vkyZNp7r97n5ubm1577TW99tpr9ggNAAAA92A5FwAAAAAAAAAA0kARHXZ38+ZNTZw4US1btlTnzp3Vrl07rV27VpL0yy+/KCIiIt19rV+/XuHh4Rkaf8+ePQoLC1OHDh3UsWNHvfTSS/r5558z1MeDhIWFaefOnZnWHwAAAAAAAADHYTkX2N2YMWOUJ08ebdiwQUajUVevXlXXrl3l7++vBg0apCqiWywWGQyGTBn79OnTGjNmjBYtWqQiRYpIknbs2KFXXnlFW7duVa5cuaxtzWazjEbeZwIAAAAAAAByMorosKsLFy5o37592rZtm7VA/dhjj2nlypXy8fHR3r17NXPmTK1Zs0ZhYWEqUaKETp8+rYULF2rv3r165513ZDQalTdv3lQ3YDp+/Lh1m9ls1vDhw1W2bFmbNu+++6569OhhLaBLUt26dfXNN9/Izc1NklS+fHl16dJFsbGxGj9+vCZOnKhjx47JYDDoySef1LRp02QwGFS6dGn1799f3333nW7cuKFx48apcuXKkqT9+/drxYoVOnPmjDp37qxu3bo9smsKAAAAAAAA4NGhiA67+vXXX1WiRAlrwfoOX1/f+7Z3d3fX0qVLFR8fr9GjR2v9+vXKkyePpk+frr1799q0HTFihObOnavChQvrzJkzGjRokD7//HObNqdPn1aXLl1SjXN3PPHx8apSpYrq1q2rmJgY5c+fX6NHj5bRaFTXrl21a9cu1a9fXxaLRcWKFdOrr76qb7/9VtOnT9eaNWskSQkJCXr33Xd17tw5derUiSI6HjkXFz418SiYTEabr8i+yKXzIJfOg1wCAAAgu6CIDrtKSkqS2WxOd/sKFSpIkn777Tf5+/srT548kqThw4dLur0muiTFxsbq3LlzGjt2rPXYhIQEJSUlydXV1botOTlZKSkp1sf9+vVTTEyMoqKi1Lt3b7Vs2VKSVLFiRUmSl5eXrly5os6dO8vFxUWnT59WVFSU9fhq1apJkipVqqRff/3Vur1GjRqSpPz58ys6Ojrd5wv8W/7+Xo4Owan5+Hg6OgRkEnLpPMil8yCXAAAAyOooosOunn32WR0/flwJCQny8PCwbr948aLN4zvuzBC3WCyyWCxp9ms0GmU0GrV8+fJ/HP/w4cPWIvn8+fMlSSNHjlRcXFyqcTdt2qTDhw/rww8/lJubm15++WWb/u7EdO/66SaT6YFxAJktKirunxshw0wmo3x8PBUbG6+UlPS/AYish1w6D3LpPDKSS94sBgAAgCNRRIddFSxYUFWqVNEbb7yhcePGycXFRVevXlX//v31yiuvpLmsS+HChXXt2jX9/vvvevLJJzV79mwVK1bMut/b21sFCxbUt99+q+DgYJ0+fVqbN2/WwIEDbfrp3r27Xn75ZVWtWlWlS5eWJF2+fFm//PKLdVb53aKjo1WgQAG5ubnpzJkzOnbsmOrXr2/dv3v3bjVt2lQ//vijSpYsmRmXCPhXkpMpJD1KKSlmrrGTIJfOg1w6D3KJ7MBz7Ra7jRXfvqndxkL2kN6fP4PBoERXk9ySUuT6gEloAPAw7PV/Ylb7/5AiOuxuxowZmjNnjlq3bi0fHx+ZzWb16dNHDRs2TLXO+R2enp6aPHmyBg0aJFdXV+XNm1evvPKKNm/ebG0zbdo0TZo0SUuWLFFSUpJ1yZe7lSxZUm+99ZZmzpypuLg4JSUlycPDQ127dlWrVq1StW/SpIk2btyokJAQFS9eXOHh4VqwYIGqVKkiSTp16pRWrVqlmJgYTZo0KXMuEAAAAAAAAIAsw2B50BoZANJUokQJHTlyRO7u7pnWZ8Lg6ZnWF3KW66P6OjoEp+TiYpS/v5eiouKYJZnNkUvnQS6dR0ZymSdPbjtF5bwiI6/bZRxnfY4660z0e8/LYDDI1dWkpKSUBy6nmVFZbTZhZrDnz0R6Par8wT7IX/ZG/jKfPf/vSM9rTeM/tgAAAAAAAAAAIIdiORfgXzp58qSjQwAAAAAAAADwiDETHQAAAAAAAACANFBEBwAAAAAAAAAgDSznAmQhHrOGO91NmHIiZ72hFgAAAAAAQE7ETHQAAAAAAAAAANJAER0AAAAAAAAAgDRQRAcAAAAAAAAAIA0U0QEAAAAAAAAASAM3FgWykITB0+Xp6CCQKRKkTMvl9VF9M6knAAAAAAAAZBQz0QEAAAAAAAAASANFdAAAAAAAAAAA0kARHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRkalCQ0P15Zdf2mxLSUlRrVq1dPr06Ucy5siRI9WoUSOFhYWpTZs26tatm/bt22fdHx4erj/++OORjA0AAAAAAADAubk4OgA4l/bt2+vTTz9Vo0aNrNt27dqlp556SsWKFXtk47700ksKCQmRJB07dkyvvvqqZs2apYoVK2r27NkP3b/ZbJbRyHtOAAAAAAAAQE5DER2ZqkmTJpo6daquXLmixx9/XJL06aefql27dkpISNCECRN08eJFJSUlKTg4WL1799batWu1d+9eSdLZs2fl5eWlxYsX6++//1bfvn3VoEED7d+/XzExMZozZ46KFy/+wBhKly6tvn37atGiRVq4cKGCg4O1ePFiPfnkkxo1apQuX76sxMREVapUSaNGjZIkzZ8/X19++aWeeOIJlSlTRr///rumT5+u4OBgNWvWTGfOnNGCBQv0zjvv6Ntvv5W7u7s8PDw0Z84ceXt7q27dugoLC9O+fft04cIFjRgxQqtXr9bp06fVs2dPderU6dFeeAAAAAAAAACPBEV0ZCoPDw81btxYGzduVPfu3RUTE6O9e/dq8uTJ+uijj5Q3b15NnjxZZrNZoaGhqlGjhkwmkw4cOKDNmzfLy8tLnTp10u7du/XMM8/o7NmzatSokcLDwzVr1iytW7dOI0eO/Mc4ypUrp3fffddm25UrV1SlShWFhobKYrGoYcOGatu2rdzc3LR69Wp99dVXcnFxUadOnWwK9fny5dOQIUOUnJwso9GopUuXysvLS6NGjdKGDRvUpUsXmUwm5c6dW4sWLdLo0aO1ePFiffTRR/rxxx81evRoiuh4KC4ufArCUUwmo81XZF/k0nmQS+dBLgEAAJBdUERHpuvQoYNGjhyp7t27a/PmzWrQoIG8vLx06NAhXbx4UQcPHpQkxcXF6cKFC5KkoKAgeXl5SbpdtI6JiZEk+fj4qFSpUtbtR44cSVcMBoNBbm5uNtv8/Px0/PhxhYSEyMXFRVevXlVUVJSio6NVunRpeXp6SpKCg4N18eJF63EVK1aUJLm43H669OzZUyaTSZcuXVL+/Pmt7SpUqCBJCgwMVO7cuWU0GhUYGKjr169n4OoBqfn7ezk6hBzPx8fT0SEgk5BL50EunQe5BAAAQFZHER2ZrnTp0nJxcdGxY8f02WefWZdMMRgM6tOnj5o1a2bTfv369dYC9R0Wi0WS0tz+T/bv36+goCCbbUuXLlVsbKxWrFgho9Go5s2bW/s0GAzWdveufe7q6mrtc+3atVq/fr18fHw0YcIEm3Ymk+m+36c3ZiAtUVFxjg4hxzKZjPLx8VRsbLxSUsyODgcPgVw6D3LpPDKSS95QBgAAgCNRRMcj0a5dO73//vu6efOmypcvL0mqVKmSvvzySzVr1kxms1mTJk1Sv379Mn3skydP6r333ku1nEt0dLSKFCkio9GoAwcO6M8//1RiYqKKFCmiX375RYmJiTIajdq+fbuKFCmSqt/o6GjlzZtXPj4+ioyM1A8//KDnn38+0+MH7pWcTJHI0VJSzOTBSZBL50EunQe5BAAAQFZHER2PRIsWLTR9+nSFh4dbt3Xu3FmnT59Whw4dlJKSotq1aysgICBTxlu6dKm2bNmi5ORkubi4aNasWSpRooRNm/bt22vo0KHat2+fqlSpop49e2rq1Klavny5XnjhBbVp00YFCxZU2bJlFR8fn2qMWrVq6cMPP1THjh2t66RHRESoTp06mXIOAAAAAAAAALIeg4W1JpDDJScn69NPP1XLli3l7u6uMWPGqGDBgurZs6fdY0kYPN3uYyLruz6qr6NDyLFcXIzy9/dSVFQcsySzOXLpPMil88hILvPkyW2nqJxXZKR97tPjrM9Rz7Vb7DZWfPumdhvr3vMyGAxydTUpKSklU5eltOc52Ys9fybS61HlD/ZB/rI38pf57Pl/R3peazITHTmei4uL/vjjD7Vv315eXl567LHHNHLkSEeHBQAAAAAAACALoIgOSBo4cKAGDhzo6DAAAACAbC/xvXVyS0qRqx1m4jnjDGc8nKw4QxwAkP0ZHR0AAAAAAAAAAABZFUV0AAAAAAAAAADSQBEdAAAAAAAAAIA0sCY6kIV4zBquqKg4JSebHR0KHoKLi1H+/l7kEgAAAAAAwAkwEx0AAAAAAAAAgDRQRAcAAAAAAAAAIA0U0QEAAAAAAAAASANFdAAAAAAAAAAA0kARHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRAQAAAAAAAABIA0V0AAAAAAAAAADSQBEdAAAAAAAAAIA0UEQHAAAAAAAAACANFNEBAAAAAAAAAEgDRXQAAAAAAAAAANJAER0AAAAAAAAAgDRQRAcAAAAAAAAAIA0U0QEAAAAAAAAASANFdAAAAAAAAAAA0kARHQAAAAAAAACANFBEBwAAAAAAAAAgDRTRAQAAAMABLl26pB49eqh8+fKqXr26ZsyYIbPZfN+2y5YtU/369VW2bFm1b99ex44ds3O0AAAAORdFdAAAAACwM4vFon79+snf3187duzQihUr9MUXX2jp0qWp2n799deaM2eOpkyZor1796pu3bp6+eWXdfPmTfsHDgAAkANRRAcAAAAAOzt69KhOnjyp0aNHy9fXV0WKFFHv3r21evXqVG3Xrl2rdu3aqVq1avL09NSrr74qg8Ggbdu2OSByAACAnMfF0QEAAAAAQE5z/PhxPfnkk/Lz87NuK1WqlH777TfduHFD3t7eNm2bNm1qfWwwGFSyZEkdO3ZMLVq0SNd4RqNBRqMh0+JPi8l0e56WwSBJj348Fxf7zAszGB79udxhr3OSUp/XnYeZnT9HnlNO8qjyB/sgf9kb+ct89vy/Iz0oogNZjL+/l6NDQCYhl86DXDoPcuk8yKXzyKm5jIqKkq+vr822O4+joqJsiuhRUVE2xfY7ba9du5bu8R57zPufG2WWnm3lZqeh7PbT07eDvUay3zlJaZ5XZucvK5xTTmKv5x8eDfKXvZG/zJPVXiFmrZI+AAAAAMBGWjNrc/KMWwAAAHuiiA4AAAAAdvbYY48pOjraZltUVJQkKSAgwGa7v7//fdve2w4AAACPBkV0AAAAALCzoKAg/fHHH9bCuSQdOXJExYoVk5eXV6q2P//8s/VxSkqKjh8/rrJly9otXgAAgJyMIjoAAAAA2Nmzzz6rsmXLatKkSYqNjdXJkye1aNEide7cWZLUuHFjHThwQJLUqVMnrVu3Tj/88IPi4uI0a9YseXh4KDg42JGnAAAAkGNwY1EAAAAAcIC33npLY8eOVe3ateXl5aXQ0FCFhoZKks6dO6ebN29KkurUqaPhw4dr1KhRunr1qsqUKaNFixbJ3d3dkeEDAADkGAaLxWJxdBAAAAAAAAAAAGRFLOcCAAAAAAAAAEAaKKIDAAAAAAAAAJAGiugAAAAAAAAAAKSBIjoAAAAAAAAAAGmgiA5kAZcuXVKPHj1Uvnx5Va9eXTNmzJDZbHZ0WJBUv359lSlTRkFBQdZ/EydOlCTt2bNHLVu2VFBQkF544QVt3LjR5thly5apfv36Klu2rNq3b69jx45Z9926dUtjx47Vc889pwoVKmjAgAG6du2aXc8tJ9i1a5dq1Kih8PDwVPseZf54TmeutPJ48eJFlShRwub5GRQUpC+++MLahjxmLZcuXVLfvn313HPPqXr16ho+fLhiYmIk8ZzMbtLKJc/LnCEjeXhQvuEYGcnfypUr1bBhQ1WoUEEtWrTQN998Y+doca9/83vw8uXLqlChgubNm2enKJGWjOTvzJkz6ty5s8qVK6d69epp6dKl9g0WqaQ3f2azWW+99Zbq169v/f25ZcsWB0SMTGUB4FBms9ny4osvWoYMGWKJjo62nDlzxlK/fn3LkiVLHB0aLBZLxYoVLQcPHky1/a+//rKUK1fOsmzZMsvNmzct27dvt5QtW9by008/WSwWi+Wrr76ylC9f3rJnzx7LzZs3LfPmzbPUrFnTEhcXZ7FYLJaJEydamjVrZrlw4YLl2rVrlpdfftny8ssv2/XcnN2iRYssDRs2tHTq1MkyaNAgm32PMn88pzPXg/J47NgxS+nSpdM8ljxmPc2bN7eMHDnScuPGDcvff/9tadeuneW1117jOZkNpZVLnpfOLyN5+Kd8w/4ykr8vv/zSUqlSJcuhQ4csSUlJlvXr11tKly5tOX/+vAMih8Xy738P9uvXz1KhQgXL3Llz7RQp7icj+YuPj7cEBwdb1qxZY0lISLD88MMPlsaNG1tOnz7tgMhhsWQsf8uXL7fUqlXLcvbsWUtKSopl+/btllKlSll++eUXB0SOzEIRHXCwn376yVKyZElLVFSUdduqVassDRs2dFxQsFgsFktycrKlRIkS932hsnjxYkvLli1ttoWHh1tGjx5tsVgsll69elkmTZpk3Wc2my21atWybNy40ZKUlGSpWLGi5euvv7buP3PmjOWZZ56x/PXXX4/obHKeZcuWWWJjYy0jRoxIVXx9lPnjOZ25HpTH3bt3W2rUqJHmseQxa4mNjbWMHDnScuXKFeu2jz76yNKwYUOek9nMg3LJ89L5ZSQPD8o3HCMj+fvss88sK1eutNlWtWpV8udA/+b34H//+19LkyZNLEOGDKGI7mAZyd/69estvXr1smN0+CcZyd9rr71mGTBggM22atWqWTZs2PCow8QjxHIugIMdP35cTz75pPz8/KzbSpUqpd9++003btxwXGBQbGysLBaL5s2bp1q1aqlWrVoaPXq0bty4oePHj6t06dI27Z999lnrR5Tv3W8wGFSyZEkdO3ZMFy5c0I0bN2z2FylSRJ6ennzEORN17dpVuXPnvu++R5k/ntOZ60F5jI2NldlsVu/evVWlShU1bNhQ7733niwWiyTymNXkzp1bU6ZM0WOPPWbd9vvvvytfvnw8J7OZB+WS56Xzy0geHpRvOEZG8teyZUuFhIRYH8fGxurGjRvKly+fvcLFPTL6ezAhIUETJkxQRESEXFxc7Bgp7icj+Ttw4IAKFSqkAQMGqFKlSvo/9u48Lqqy///4e9hxYUtvy9wqE0zANEzTEiXL1DQ1EKnUu03zNrc0yswltVzKJbXFpULNLJHcbvfMMs2ltBIhtVJLqdtSwAVRZJjfH/6YryMcYXRgGHw9Hw8fOueaOedzrosZr3nP4Zr27duzHIiT2TN+rVq10nfffad9+/YpNzdXX3zxhc6fP6+77767lKuGIxGiA06WkZEhf39/m235tzMyMpxREv6/nJwcNWjQQGFhYVq7dq3mz5+vn376SaNHjy503AICAqzrtmZkZNj85ypdHNf09HTruF7+eD8/P9ZFLyUlOX48p0uPp6enatWqpe7du+ubb77RqFGj9N5772nJkiWSGMeyLjk5WQsXLtSTTz7Jc9LFXTqWPC/LP3vG4UrjDee42ueRxWLRq6++qtDQUDVu3LhEa4Qxe8fvnXfeUZMmTQjuygh7xu/YsWNatmyZoqOjtXXrVj399NMaMmSI9u3bV2r1wpY94/fAAw8oLi5OjzzyiBo0aKAhQ4bojTfe4ENIF8dHkQBgoFq1avr888+ttytVqqShQ4fqueeeU0RERKGPMZlMNn8btRspqh0li/FzLVFRUYqKirLebtGihWJjY5WUlKSYmBjGsQzbtWuX+vbtq/j4eEVGRhp+URbPybLv8rGUxPMSVlc73ihbLly4oJdfflmHDh1SQkKC3Ny4Fs8V/Prrr1q6dGmBL+qGa8jNzVWrVq3UsmVLSdKjjz6qxYsXa9WqVQoJCXFydSjKsmXLtHTpUi1dulR169bVtm3b9MILL6h69eoKDw93dnm4SvzvBzjZDTfcoMzMTJtt+Z9iBgUFOaEiXEmNGjWUl5cnNze3Qsctf8wCAwMN2/N//f3SdovFoszMTJtfjUfJCQoKKrHx4zntXDVq1NDx48clMY5l1aZNm9SnTx+NGjVKjz/+uCSek66qsLEsDM/L8sWecbjSeMM57H0enTt3Tn369NGxY8e0cOFC5qpOZs/4jR49WoMGDeL5VobYM37+/v4FljS8+eabrf+fovTZM34LFixQt27ddMcdd8jLy0uRkZFq2rSpli1bVkrVoiQQogNOFhYWpj///NPm13/27NmjunXrqmLFik6sDPv27dOECRNsth08eND6n+Dl63nu2bPH+qlyWFiY9u7da20zm81KTU1VeHi4atasqYCAAJvH79+/Xzk5OQoNDS3BM0K+sLCwEhs/ntOlZ926dVq4cKHNtoMHD6pmzZqSGMeyaPfu3XrppZc0ffp0dejQwbqd56TrMRpLnpflnz3jcKXxhnPYM34Wi0WDBw+Wl5eXPvzwQ/n5+ZV2ubhMcccvLS1N3333nd588001bdpUTZs21apVqzR37lx16dLFGaVD9j3/GjRoUGBulJaWpptvvrlUakVB9r5+5uXl2WzLzc3lN3lcHKMHOFn9+vUVHh6ucePG6dSpU9q/f79mz559xSu6UDoCAwP12Wefafbs2crJydHhw4f19ttvW9c2S0tLU0JCgs6dO6e1a9dq8+bNio2NlSR1795dSUlJ2r59u7KysjRlyhT5+PgoKipK7u7u6tatm6ZNm6YjR47oxIkTGj9+vB566CFVqVLFyWd9fejYsWOJjR/P6dLj6empSZMmaevWrcrNzdW3336rpKQka18zjmVLbm6uXn31VQ0cOFDNmze3aeM56VquNJY8L8u/osbhoYce0vfffy/pyuMN57Bn/FauXKn9+/dr6tSp8vLycmbZ+P+KO3433nijvv76ay1fvtz6JyoqSt27d9fs2bOdfBbXL3uef507d9b+/fv16aefKicnRytWrFBKSoo6derkzFO4rtkzfq1bt9aSJUv0yy+/yGw2a9u2bdq2bZtatWrlxDPANbMAcLq//vrL8uyzz1rCw8Mt99xzj2XGjBnOLgn/386dOy3dunWz3HnnnZbWrVtb3nzzTcv58+ctFovF8t1331k6depkCQ0NtTz44IOW9evX2zz2k08+sbRq1coSFhZmiYuLsxw4cMDadv78ectrr71miYiIsDRq1Mjywgsvpvk6nAAAlRFJREFUWE6dOlWq51behYaGWkJDQy0hISGWkJAQ6+18JTl+PKcdp6hx/PTTTy0PPvigpWHDhpYOHTpYkpKSbB7POJYd3333naVevXrWMbz0z9GjR3lOupCixpLnZfl3pXGoV6+e5euvv7bevtJ4wzmKO349e/a01K9fv8DzfPjw4c4qHRb7nn+XeumllyzTp08vrTJhwJ7x27lzp+WRRx6xhIeHWx5++GHDsUXpKe745eTkWKZMmWJp3bq1pWHDhpZ27dpZlixZ4qyy4SAmi8VicXaQDwAAAAAAAABAWcRyLgAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAJQhUVFRmjFjhrPLkCT99NNPevDBB9WwYUP99NNPzi4HAAAAAACnIEQHAOASPXr0UEhIiHbs2FGgbcaMGerRo4cTqnKODz74QBUrVtR3332n8PBww/t98803Cg4OVvfu3Qtt37Rpk5KTk623Dxw4oLVr1zq8XntdXhcAAMD1Kjc3V5GRkQoJCdGhQ4ecXQ4AlDmE6AAAXCYwMFAjRozQuXPnnF3KNbtw4cJVPzYjI0N16tSRl5eXTCaT4f0WLFighx9+WHv27FFqamqB9hkzZmjv3r3W259//rnWrVt31XU5yuV1AQAAXK82bNignJwctWjRQgsXLnRaHdcydwWAkkSIDgDAZbp16yYfHx+9/fbbV7xfcHCwFi1aZLOtRYsW1uVYpk6dqk6dOmn58uVq1aqVGjZsqIEDB+rEiRMaMGCAGjVqpMjISK1evdpmH+fOndOwYcMUERGhe++9V2+88YbNG4r169frkUceUXh4uFq1aqXhw4crIyPDpq4PP/xQHTp0UJcuXQzrT0xMVKdOndSoUSO1adNGY8eO1ZkzZyRJbdu21Xfffae1a9cqLCxMu3fvLnQff/zxhzZv3qxnn31WLVq00Mcff1ygP1JSUjRu3DhFRUVp4MCBSkhIsO43/0qnTz/9VG3btlV4eLjatGmjiRMnKjs7W5J0+PBhBQcHa8OGDYqLi1N4eLjatWunvXv3asGCBbrvvvt011136cUXX5TZbLb2ffv27bV06VJFRUWpUaNG6tmzp/74449C65Kkbdu2KTY2VnfddZcaN26sxx57TD/88INh/wEAAJQXH3/8sTp27KhHH31US5cuVVZWlrUtNzdXkydP1n333ac777xT0dHR2rZtm7X98OHDevbZZ9WoUSM1bdpUQ4cOtc5NZ8yYoRYtWtgca9GiRQoODrbeLmzueuLECb3wwgu65557dOedd6pDhw5asWKFzX42b96sLl26KDw8XJGRkZo+fbry8vI0depU3XfffdZ5Yb6ePXtq0KBBDukvANcfQnQAAC7j4eGh119/XQsWLNCePXuuaT9paWnat2+f1q1bp7lz52rt2rXq1auXevXqpZ07d6ply5YaPXq0LBaL9XFLlixRy5Yt9e2332rq1KlavHixEhISJEl79uzRkCFD1L9/f+3atUuLFi3S0aNHNXToUJtjJyYmavLkyQXebORbtmyZXn/9dQ0ePFg7duzQ7NmztXPnTut+1q1bpyZNmuihhx5ScnKyGjduXOh+FixYoNDQUIWEhCgmJkarVq1SZmamtX3r1q2SpFdffVVffvml3n77bZv93nLLLVq7dq2mTJmicePG6YcfftCsWbO0ZcsWjR8/3tqP0sXlZSZNmqStW7fK29tb/fv3199//60vvvhC7733nlasWKFNmzZZH/Pnn39q9+7dWrlypTZu3Ciz2ax+/foVWldubq769eunRx55RDt37tTWrVvVtGlTDR48uMAbMAAAgPLk559/1vfff6+YmBi1adNGnp6eWr58ubV99uzZWr16tT788EN9//336tChg/r06aOjR4/KbDbrueee07/+9S998803WrNmjf73v/8VmJsW5fK56/Dhw3X48GH997//1a5du9SjRw+99NJL+uWXXyRJR44c0X/+8x899thj+v777zV37lwtWrRIc+fOVbdu3XT8+HF99dVX1v0fO3ZM3333nbp163btHQbgukSIDgBAIcLCwtSjRw8NHz78mn6t9OzZs3r++efl7e2tJk2aKCgoSKGhobrrrrvk6empNm3a6OTJkzpx4oT1MQ0bNlS7du3k5eWlJk2aKDIyUl9++aUk6aOPPlLr1q2tb3BuuukmDR06VFu2bNGRI0es+2jRooVCQkLk5lb4f/ULFixQu3bt1Lp1a3l5eenWW29V79699dVXXyk9Pb1Y55aVlaWlS5da34xERUWpcuXKWrJkiV199OGHH+rRRx9VkyZN5O7urttuu039+vVTUlKSTd937NhRNWvWVOXKldW8eXP9888/6t+/v7y9vXX33XcrKChIBw8etN7/3LlzGjx4sCpWrKigoCA9++yzOnDggA4fPlzouWRnZ6tChQpyd3eXr6+vBgwYoE2bNsnd3d2u8wEAAHAl8+fPV6NGjXT77bfLy8tLXbp00SeffGJtX7BggR577DHdfvvt8vDwUK9evfT666/Lw8NDW7Zs0aFDhzRkyBBVqlRJQUFBGjt2rGJjY+2q4fK569tvv60FCxbohhtukLu7u7p27aq8vDzr99ksWrRIderUUUxMjLy8vHT77bdrxowZaty4sW6++Wbdd999SkxMtO5/9erVql69uu655x4H9BiA6xEhOgAABgYOHKjz589r1qxZV72PgIAAVaxY0Xrb19dXN910k/W2j4+PJNmsv37bbbfZ7OPmm2/W//73P0nSwYMH9cUXXygsLMz657HHHpO7u7uOHj1qfUzNmjWvWNcff/yhunXr2myrU6eOLBaLdcmToixbtkxms1nt27eXdPHq765du2rRokXKy8sr1j7yz2n+/Pk25/Tiiy/KYrHor7/+st6vRo0a1n/7+vqqSpUq8vLystl2/vx56+0bbrhBQUFBBR5/6T7z+fv769VXX9WoUaPUtm1bvfbaa9ar2gEAAMqrjIwMrVq1yuYK7W7duumXX37R9u3bdfr0aaWnp9vMLd3c3NSxY0fdeOONOnz4sPWChXy33HKLHnzwQbvquHzuum/fPvXv318tWrSwXoAiyTrX+/33323mhpIUERGhiIgISVJsbKw2b96sY8eOSZJWrlypmJiYK37PDwBciYezCwAAoKzy8fHRuHHj9PTTT6tt27bFeszl4XFhV4IbXR2e7/LJvcVisYbFbm5u6tatm0aPHn3FfVwaLttznMK2F8Zisejjjz/W+fPn1apVK+t2s9mss2fP6quvvrKuNV4UNzc3DRo0SM8++2yh7fkfDlxeV1H9ePlY5J9f/gcXl3v88cfVsWNHbdmyRdu3b9ewYcPUoEEDzZkzh6vRAQBAubR48WKdP39e48aN0xtvvGHdbjKZtHDhQus2owskLBaLzbKExVHYvi6du2ZlZal3795q0qSJli9fripVqigvL0/169cv9nFbtWqlqlWravny5XrggQe0f/9+vf/++3bVCQCXIkQHAOAK7r77bnXp0kWvvPKK7r33Xps2b29vm+VGzp49a7Me+NXK/7LNfGlpaapevbqki1f2pKSk2LRnZ2fr9OnT+te//lXsY9SpU8e6pmS+3377TW5ubqpdu3aRj9+6dasOHjyojz76SLVq1bJpGzFihBYuXFjsEL2wczp58qSki1eIX62MjAxlZGQoMDBQkqzL3Vz6mwCXSk9PV1BQkNq3b6/27dvr2WefVZs2bXTgwAGbN20AAADlgdls1qJFi9S9e/cCFzNs2bJFY8aM0ZkzZxQUFKTffvvNpn3evHlq1qyZbrnlFp09e1Z//fWXdY516NAhffXVV+rZs2eB+bIk/fnnn1es6+DBg8rMzNQzzzyjKlWqSJJ1GZd8derU0YYNG2y2bdu2TSdOnNDDDz8sd3d3xcTEaOXKlcrOzlZkZKRdc2UAuBzLuQAAUIT4+Hj9/fff+vzzz222161bV19//bX1SpgZM2YYXuVsj927d+uLL75QTk6OduzYoa+//tq6ZMq///1vJScn66OPPlJ2drYyMjI0atQoPfnkk3YtodKrVy+tWbNGX3/9tS5cuKADBw7o/fffV7t27RQQEFDk4xcsWKDGjRurefPmqlGjhs2fHj16aOvWrdYPA3x8fHT48GFlZmbKYrHIx8dHaWlpOnnypM6fP68nn3xSGzZs0MqVK5WTk6Njx45p8ODBGjJkyFX1Xz4vLy+99dZbOnXqlE6cOKEPPvhAd911l2688cYCde3atUv333+/Nm/erNzcXOXm5mr37t3y8fGx3h8AAKA82bBhg/73v//pySefLDCfe/TRRxUUFKRPP/1UTzzxhBYtWqS9e/cqNzdXixYt0uTJk+Xr66sWLVqoTp06mjhxok6ePKnMzEyNGzdOmzdvlru7u26//XadPHlSu3fvlnQxYL88/L7cjTfeKA8PD3333XfKy8vT3r17NX36dPn7+1uX5evevbv++usvffTRRzp//rx+++03vfzyyzbfERQTE6PffvtNCxcu5AtFAVwzQnQAAIpQqVIlvfbaa9Z1yfONHDlSx48fV2RkpDp37qz69eurRo0ays3NvepjXbhwQU888YTWrl2rZs2aaciQIerRo4ceffRRSVJ4eLimTZumZcuWqWnTpnrooYeUnZ2tuXPnFrm8yaUefvhhDR48WOPGjVPjxo3Vr18/PfDAAza/xmvkjz/+0ObNm/X4448X2t6qVStVr15dCxculHQxsP/000/18MMP68KFC4qNjdXhw4f1wAMPKDk5WQ899JBeeeUVzZw5U3fddZc6d+6satWqafLkycU+n8JUqVJFd955px555BHrF7G+9dZb1vZL6woLC9Mrr7yiN998U02aNFGzZs302WefadasWdYr2QEAAMqTjz/+2BqCX87T01MxMTFavHixnn32WXXq1EnPPfecIiIilJiYqPfff1+1atWSh4eH3nvvPZ0+fVqtWrVS27ZtFRAQYJ3HtWrVSo899pj69++vtm3basqUKXrmmWckyXDOXLVqVY0cOVLz58/XXXfdpbfeekujR49WbGys5s+fr6lTp6pWrVqaOXOmli9froiICD355JPq3Lmz+vTpY91PtWrV1KpVK/n4+Oi+++5zfAcCuK6YLPYuXgUAAFDGzZgxQ0uXLtWXX37p7FIAAADgJLGxsWrdurWee+45Z5cCwMWxJjoAAAAAAADKjQsXLmj27NlKS0vTE0884exyAJQDLOcCAAAAAACAcmHXrl266667tGbNGr333nuqVKmSs0sCUA6wnAsAAAAAAAAAAAa4Eh0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAQKl6+eWXFRwcbP0TGhqqVq1aaeDAgdq2bVuB+0dFRWnw4MEOr2PGjBkKDg7W+fPnJUk9evRQjx49HH4c6eI5t2jRokT2fbXWr1+vyMhIhYeHa/fu3YXep6i+b9GihV5++eWSKrHYXn31VQUHB2vixInOLgUAADgQ88ayoTjzxnzvvfeegoODNWDAgFKqDpcq6udn8ODBioqKKsWKgPKDEB0AUOqCgoK0ZcsWbdmyRevWrdOECRPk5+enp556Sm+88YbNfZcsWaIxY8YUe9+7d+8u1huPp556Slu2bJG3t7fd9Rdn34sWLbLeHj58uFauXOnw41yLGTNmyN/fX//97391xx13OLucq3b27FmtXr1ad9xxh5YvX64LFy44uyQAAOBAzBudr7jzRovFos8//1x33HGHvvzyS6Wnpxe4T2F9PmLECL311lsOr/talMWaADgXIToAoNS5ubmpatWqqlq1qm6++WY1a9ZMY8eO1YQJEzRv3jwlJiZa7xsUFKTKlSsXe98//vjjFdtzc3NlsVhUsWJFVa1a9WpPwVBeXp5++uknm22VK1dWUFCQw491LTIyMlS/fn3VqlVLPj4+zi7HUFGh+KpVq3ThwgWNHz9e6enp+vrrr0upsiuzWCzKzc11dhkAALg85o3OV9x54/bt2/XHH39o3Lhx8vT01IoVKwrcp7A+L2ocnKEs1pQvLy9PZrPZ2WUYyn/eAOUNIToAoMx45JFH1KJFC82ePdu67fJfy92wYYMeffRRNW7cWI0bN1b37t317bffSrr464sTJ07U8ePHFRwcrBkzZujo0aMKDg5WYmKiunfvrvDwcJ0+fbrAr+XmW7lypR588EGFhoaqXbt22rRpk7WtsMecP3/e5lj169fXmTNnNHr0aAUHB1vruvSKG4vForlz56pt27YKDQ1V06ZNNWDAAB05csR6n6lTpyoiIkK//vqrHnvsMTVs2FAtW7bUzJkzi+zHTZs2qVu3bgoPD9edd96pxx57TNu3b5cka3/8888/WrZsmYKDg7Vjx45ijU9Rdu7cqSeeeEJNmjTRnXfeqS5dumjVqlU29/n222/VvXt3NWzYUI0bN1bv3r3122+/Wds///xzBQcHa/PmzWrTpo1iYmKueMwlS5bo/vvvV0hIiCIiIrRkyZIC97FYLJo9e7batGmjsLAwtW3bVvPnz7e5z+7du/X444+rYcOGuueee/Tiiy/q+PHjkv6vzy69Skwq+OuwUVFRGjdunIYPH66GDRvqq6++kiQlJyfr6aefVtOmTdWwYUO1b99en376abFrXLBggUJCQmx+PiTp2LFjql+/vhYsWHDFPgIAoDxi3lj25o2JiYkKDw9XgwYN9OCDDyopKcmmvbA+Dw4O1oEDBzRnzhwFBwfr6NGjkqSUlBQ9/fTTatSokRo2bKgnnnjCZimZrVu3Kjg4WN9995369Omjhg0bqkWLFvrwww916tQpDR48WI0aNdJ9992nhIQE6+M+++wzBQcHKzk5Wb169VLDhg3VrFkzTZgwwRpMF1ZTTk6OJkyYoKioKIWFhalFixZ66aWXCr3aPt+UKVPUuHFjpaSkKDo6WmFhYbrvvvtsfmYl6cSJExo2bJjuuecehYaGqkOHDgXmtMHBwZo9e7aee+45hYeH68CBA1cY1eJLS0vToEGD1KJFC4WFhalNmzaaMWOGTUj/xx9/qH///mrSpInCwsLUtWtXm591o+cNUN4QogMAypTWrVvrjz/+0F9//VWg7dChQxo0aJDatm2r5cuXKzExUWFhYerdu7f++usvDR8+XO3atbP+2u9TTz1lfexHH32k6OhorVu3TpUqVSr02IcOHdKyZcv01ltvacmSJapevboGDBhQaC2Fuemmm6xXQw0dOlRbtmwp9H4zZszQtGnTFBcXp5UrV2r69Ok6dOiQevXqpezsbEmSh4eHcnNz9dprr+k///mPVq1apfbt22vGjBnauXOnYQ3btm1T3759Va9ePS1evFiffPKJ/vWvf+mZZ57Rvn37dNNNN2nLli0KCgpSu3bttGXLFjVq1KhY53clp0+fVp8+fVS/fn0tXrxYK1asULt27TRkyBDrlTzff/+9nnnmGVWvXl2LFy9WQkKCsrOz9cQTTxR4AzJnzhy9/vrrmjVrluExf/31V/3444+Kjo6WJEVHR+ubb77RP//8Y3O/mTNn6r333tPzzz+vVatWqU+fPpo4caI1fP7tt9/073//W7fccouWLFmi999/X/v371fv3r3tvormm2++UYUKFbRy5Urdc889ysrK0pNPPik3NzfNnz9fq1atUmxsrEaNGqUvv/yyWDV27txZPj4+Wrp0qc2x1q5dK09PT3Xq1MmuGgEAKC+YN5adeWNmZqY2bNhgMy87cOCAkpOTrfcprM/zz/uJJ57Qli1bdNNNN+n333/XE088odzcXM2fP1+LFy9WlSpV9OSTT+rgwYPWc5akSZMmqWvXrlq+fLmaNWumSZMmacCAAWrVqpWWLVume+65RxMnTrR+6JD/uFGjRumpp57SypUr1bt3byUkJOijjz6SpEJrevfdd7Vq1Sq98cYbWrdund5++23t27dP8fHxhv3r4eGhs2fPauLEiYqPj9eKFSvUsWNHTZ48WWvWrJEk5eTk6N///re2b9+uCRMmaOXKlerYsaOGDx+uZcuW2ewvKSlJjRs31po1a3TbbbcZHtceL774otLT0zVnzhytW7dOQ4cO1fz58/XBBx9Iujiujz32mP744w+99957WrZsmRo3bqz//Oc/1g9b8hXneQO4MkJ0AECZctNNN0mS/v777wJtP//8s3Jzc9W1a1fVrFlTt912m4YNG6YFCxbIz89PlStXlo+Pj/XXfitWrGh9bN26dRUdHa2aNWvKza3w//4yMjI0ceJEhYeHKyQkRGPHjlVOTo7WrVtXrNrd3d2tv35bqVKlQn/tNycnRwkJCerYsaM1uG3atKnGjBmjtLQ0rV+/3nrf7OxsPfXUU7r33ntVo0YN9enTR5K0Z88ewxrmzp2rGjVqaOzYsQoJCdEdd9yhCRMmyNfXVwsWLJC7u7uqVq0qNzc3+fj4qGrVqvLy8irW+V3JoUOHdPbsWT388MO65ZZbVKtWLfXu3VuLFy9WnTp1JEmzZ89WtWrVNHHiRAUHBys8PFxTpkzR6dOnC1xt07ZtWzVt2lTVqlUzPObixYt18803q3nz5tbH+Pj42LzhuHDhghISEvT444+rc+fOqlWrlrp27ar+/fvrzJkzkqT58+fL399fY8aM0e23366GDRtq1KhRqlu3rvVq9OI6c+aMXn75ZdWuXVsVK1aUj4+PVq5cqalTpyo4OFg1atRQr169dMMNN+ibb74pVo2VK1dW+/bttWzZMptQf82aNXrggQfk7+9vV40AAJQXzBvLzrxx+fLlcnd3V4cOHSRJTZo0UZ06dWyuRi+sz/PP29fXV1WrVpW7u7v1yvG3335bYWFh1i+Qr1SpkubNm2dz3MjISLVt21Z16tRRr169ZLFYVKtWLT3yyCOqXbu2evTooby8PO3fv1+SZDKZJEmdO3dWZGSkatWqpaeeekpNmza1Lj9TWE0pKSkKDg5Ws2bNVL16dUVERGj27Nl68cUXDfvXZDLJYrGoV69euvvuu3XLLbcoPj5etWrVsh5r48aNOnDggMaOHavIyEjdcssteu655xQVFVXgYpKKFSuqd+/eqlmzpkPm79LFK/7vvfde3XHHHapevboeeughLVq0yDqOS5Ys0fHjxzVlyhRFRETotttu0/Dhw1WvXj3NnTvXZl/Fed4AroyfagBAmZKTkyNJhX5x01133aUbbrhBTzzxhD744APt27dPJpNJjRo1snnjU5jQ0NAij12rVi1VqVLFert69ery9/e3XvHiCAcPHlRWVpaaNGlis71hw4Zyd3fXzz//bLP9zjvvtP47ICBAknTy5EnD/ScnJysiIsL6BkGSfHx8FBISUmDfjnT77berTp066t+/v9555x39+OOPMpvNCg8Pt9b9008/qXHjxvL09LQ+rmrVqrr99tttfj1XKnq8cnJytGLFCj3yyCPKy8tTbm6uPD091b59e5s3awcPHtSZM2fUoEEDm8c/99xz6tu3r6SLfVa/fn2byf5dd92lSZMm2b3+aUhIiNzd3a233d3dtX//fv3nP/9R8+bN1ahRIzVq1Ejp6enKzMwsdo3du3dXWlqatm3bJkn6888/9eOPPxa53A0AAOUZ88ayM29csmSJ9YKG3Nxc5ebmqnPnzlq1alWBZXCK8tNPPyk4ONh6DtLFMW7UqFGBOWNYWJj134GBgZIuzsfy5e/j8uVFGjdubHO7fv36Vxy7Bx54QN9884369++vlStX6vjx46pWrZp1GZ4rufxYISEh1mP99NNPMplMuvvuu23uc8899+jgwYPW+aJUvJ9Le7Vp00YzZ87UuHHj9PXXXys7O1t169bVzTffbK2vatWqNle+m0wmNWvWzO75O+DqPJxdAAAAl/rjjz9kMpkKvQK5WrVqSkxM1IcffqgFCxZo0qRJuvHGG9W/f3/rr44aKc6XTBV2Ra+Pj4/Onj1b/BMoQv7Vz5cfy83NTZUqVbK256tQoYL13/lvcK60xMiZM2cKPQ9/f38dO3bMrlrd3d2v+KVFFy5csAbivr6+WrRokT766COtWLFC06dPV0BAgJ5++mk9++yzMplMOn36tNatW2ezjIl0cX3QS4Nnqejx+uKLL5SRkaF3331X7777boH23bt3q3HjxtY3TFf6EqzTp0+rRo0aVzxecfn5+dncTk1NVb9+/dS4cWO9/fbbqlKlitzc3NSjRw+b4xdVY/76oklJSWrevLlWr16tmjVrqmnTpg6pGwAAV8S8sWzMG3/66ScdOHBABw4cKLAEiSStX79eHTt2LPb+Tp8+rbS0tAJLx+Tk5BSYa106f8o/58K2Xd4Pl4+xr6+vLly4oNzcXOuSL5fq1q2b/vWvf+nTTz/Vq6++qvPnz+vuu+/WqFGjilxapbBj5S/Fc/r0aVkslgJzuvwvqD9+/Lj1g4DLz70w9szfpYvL4XzyySdatWqVFi5cKA8PD3Xs2FHDhg1T5cqVdfr0aR0/frzAWFy4cEEXLlywnkdh5wmUN4ToAIAyZf369brzzjutV5Jc7uabb9aIESM0YsQI/frrr5o/f76GDx+uWrVqFbiCw16FfQHOqVOnrFcrXXqVTr5z587ZdYz8yeXlVwWZzWadPn36mieflStXLvSKo8zMTLvXJqxatarhup6nTp3SyZMndeONN1q3BQUFaciQIRoyZIiOHj2qJUuWaMqUKapataq6dOkiPz8/tWjRQgMGDCiwP3t/JTUxMVF33XWXhg0bVqAtPj7eumZkfn9eehXP5SpXrqyMjAzD9sLGXSre2K9bt04mk0nvv/++9ecoLy/P5k1vcWqUpNjYWE2YMEHZ2dlavXq1YmJiDGsDAOB6wLyxbMwbExMTVatWLU2ZMqVA21tvvaWkpCS7QnQ/Pz/deOONGjduXIE2Ry0Tcvl5nz17Vt7e3oUG6PlatWqlVq1aKScnR9u2bdNbb72l3r1764svvrjinOzUqVPWpXvyj5X/c+Ln5ydvb+9CP3yQ/m/JouKqWrWqMjIylJ2dLV9f3wLtR44csZm/u7u7q0ePHurRo4dOnjyp9evX680335TFYtH48ePl5+enGjVqFFi6JV9hvwUClFcs5wIAKDPmz5+vlJQUPfvss4W2//zzz9blLKSL6+6NGTNGAQEBSklJsW6398sg8/3+++82X3B58OBBZWdn6/bbb5f0f1d/XBqApqamFrovoxpuvfVWVa5cWd99953N9l27dikvL8/mV1KvRsOGDfX999/bHD8rK0upqakKDw+3a1+RkZFKTU3Vb7/9VqDt008/lSQ9+OCDkqTDhw/bXGFeo0YNDRo0SPXr17eOzZ133qlDhw6pdu3aNn9yc3PtWjbl6NGj2rZtm7p06aKwsLACfx5++GGtWbNGZ8+e1a233qpKlSrp+++/t9nHzJkz9fLLL0u6+GvAKSkpNlfS7NmzR3FxcTp8+LD1Cq1Lx/3StTWvJCsrS15eXja/Nr5u3TplZWVZx6g4NUpSx44d5ebmpo8++kj79+9Xly5dittlAACUO8wby8a88ezZs1q9erU6dOhQ6LysS5cu2r59u44ePWp9TGHne+m2/DnjTTfdZDNntFgs+te//nUNZ/x/fvjhB5vbqamp1rG7vKa8vDxt2LBBf/75p6SLF39ERkZq0KBBOnr06BWXzJFks+yJxWKxOdadd96p8+fPKzs72+ZcfXx85OfnZ/eFJpGRkZKk//73vwXafvzxR+3fv986f8/MzNTy5cutV737+/srJiZGXbp00d69e631/e9//1OlSpVs6nN3d9cNN9zA2ue4rvDTDgAodXl5efrnn3/0zz//6H//+5++//57DR8+XG+88Yb69u2r+++/v9DH/fjjj/rPf/6jpKQkHTlyRH/88YcSEhJ0+vRp3XXXXZIuvmHJzMzU9u3b9fvvvxe7JovFooCAAL388svau3ev9u3bpzFjxqhChQpq27atpP9bc3HJkiUym836/fffNWfOHJsrMPLfMH3//fdKTU0tcMWRp6ennnrqKa1cuVLz58/X4cOH9e2332rEiBG69dZb1aZNm+J3ZCGeeeYZpaWladSoUTpw4ICSk5M1ZMgQmc1mmyVEiqNHjx6qW7eunn32Wa1atUqHDx9WcnKypk6dqunTp+u5556zvgH4448/9Pzzz+vDDz/U4cOHdfToUS1btky//vqrdR3PZ555Rvv27dPo0aO1f/9+a/917NhRW7ZsKXZdS5YskYeHh3VcLtehQwdlZWVp7dq18vT0VM+ePbV8+XItWrRIf/zxh1asWKFZs2ZZ18vs0aOHLly4oJdffln79+/Xnj179Nprr+ncuXOqVauWKlWqpDp16mjdunXKzMxUVlaWpkyZory8vCJrbdiwobKyspSQkKAjR44oKSlJn3zyiRo1aqRffvlFR44cKVaN0sVf0e7YsaPeffddRUZG2r1eOwAAroh5Y9meN65atUpZWVl6+OGHC21v06aNvLy8tHTpUkmF97mfn5/27Nmjn3/+WadOnVLPnj2VlZWlIUOGaO/evTpy5IgWL16szp07F/gy+qu1ePFirV+/XocPH9bcuXO1c+dOde3a1dp+aU1nzpzRnDlzNGjQIH3//ff666+/tHfvXi1atEj16tWzWbu9MHPmzNHWrVt16NAhTZw4UWlpadZjtW7dWvXq1dOLL76ob7/9Vmlpafr666/1xBNPaNSoUXafV6NGjRQTE6M33nhDH3zwgfbv369ff/1VixcvVt++fdWkSRPFxsZKuvjcGj16tEaMGKF9+/bpr7/+0rZt2/TFF19Yf1Oja9eu8vf318CBA7V7924dPXpUa9asUUxMjN555x276wNcGcu5AABKXXp6uu69915JF3/VtUqVKgoNDdUHH3ygFi1aGD4uLi5O586d04cffqixY8fK3d1ddevW1fTp061Xy8TExGjTpk3q27evunfvrscff7xYNeXm5uqOO+5Q+/btNXjwYP3111+qU6eO3n33XWtY2ahRIw0aNEgLFy7Uu+++q3r16mnkyJF68sknrVdw+Pn5qVevXlq8eLG+++47LV68uMCx+vbtKx8fH3388ceaOHGiKleurPvuu08vvvii3VebXO7uu+/W+++/r5kzZ+rRRx+Vh4eHGjZsqPnz5xe5XuPlKlSooE8++USzZ8/WjBkz9Oeff8rb21shISF688031a5dO+t9W7ZsqTfeeEPz58/XjBkzJF38wq2RI0da30xGRERo7ty5mjFjhrp16yY3NzfVrVtXU6dOVatWrYpVU15enpYuXaqWLVsargtZp04d6/rhXbt21YABA+Tj46O5c+fq9ddfV/Xq1TVkyBD9+9//lnTxyrSPPvpIU6ZMUUxMjCpUqKB7771X8fHx1qtrJk6cqDFjxigyMlJBQUF6/PHH9cADD2jdunVXrLdDhw5KTk7WrFmzNH36dDVr1kyTJ0/W7t279eqrr+rZZ5/V2rVri6wxX/v27bVo0SJ169atWP0FAICrY95YtueNSUlJCgkJUd26dQttr1Spklq1aqWlS5eqX79+Bfr8pZdeUt++fTVz5kw9/fTTevfdd3XnnXdqwYIFmjp1qnr06KG8vDzVrl1bL730kuLi4q7pnPMNGzZMs2bN0p49e1ShQgU999xzeuyxx6ztl9f0zjvvaNKkSRo0aJAyMzMVEBCgpk2b6rXXXivWscaOHasDBw4oMDBQw4cPt859vby8lJCQoLfeektDhgzR6dOn9a9//Utt27bVwIEDr+rcxo4dq7CwMC1ZskTvvPOO8vLyVKtWLT355JN68sknrUvWBAUF6aOPPtL06dPVq1cvnT17VtWqVdNDDz1kXX4xICBAn3zyid566y316dNH586dU/Xq1dWzZ0/17t37quoDXJXJcrW/uwQAAIBSNW7cOH3zzTdau3Yt66EDAADY6fPPP9ewYcO0ceNGh32xvJEZM2Zo5syZxVoCEEDZx5XoAAAAZVhubq6OHTumL7/8UgsXLtTMmTMJ0AEAAACgFBGiAwAAlGH//POP2rdvr8qVK2vUqFGGa78CAAAAAEoGy7kAAAAAAAAAAGDAzdkFAAAAAAAAAABQVhGiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABjwcHYBAP7PP/+cLpXjuLmZFBRUUenpWcrL47uFyyrGyXUwVq6BcXINjJPrKM2xqlq1conu/3pgsVh4XtmB1yL70F/2o8/sQ3/Zjz6zD/1lv/LUZ8WZa3IlOnAdcnMzyWQyyc3N5OxScAWMk+tgrFwD4+QaGCfXwVi5FsbKPvx824f+sh99Zh/6y370mX3oL/tdb31GiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAgIezCwAAAABQunwTV1/zPkwmk9S3mwOqQWlyxNiXlOyY9s4uAQAAoFBciQ4AAAAAAAAAgAFCdMDAG2+8oeDgYOvtbdu2qVOnTgoLC9MDDzygFStW2Nx/3rx5at26tcLDwxUTE6OUlJTSLhkAAAAAAACAgxGiA4X4+eeftWzZMuvtY8eOqW/fvoqOjtbOnTs1fPhwjRgxQnv27JEkbdiwQdOmTdP48eO1Y8cORUZGqk+fPjp79qyTzgAAAAAAAACAIxCiA5fJy8vTqFGj9OSTT1q3rVy5UrVr11bPnj3l6+urVq1a6f7771diYqIkKTExUdHR0WrWrJl8fX3Vr18/mUwmbdy40VmnAQAAAAAAAMAB+GJR4DKffvqpfHx81LFjR02bNk2SlJqaqgYNGtjcr379+lqzZo21vX37//siJJPJpJCQEKWkpKhjx47FPrabm0lubqZrP4kiuLu72fyNsolxch2MlWtgnFwD41Q6TKZrn2/k74KxAgAAQHlHiA5c4vjx43rnnXe0YMECm+0ZGRkKCQmx2RYQEKD09HRre0BAgE27v7+/tb24goIqOuRNbXH5+fmW2rFw9Rgn18FYuQbGyTUwTiUrx9PdYftirAAAAFDeEaIDlxg/fry6deumW2+9VUePHi3y/vmBt1HwbW8gnp6eVWpXovv5+erUqWyZzXklfjxcHcbJdTBWroFxcg2MU+nwumC+5n2YTJKXVCpjFRhYsUT3DwAAAFwJITrw/23btk179+7VG2+8UaAtKChImZmZNtsyMjIUFBQkSQoMDCy0vV69enbVkJdnUV6exa7HXAuzOU+5uQQUZR3j5DoYK9fAOLkGxqlkeVocMd+4+ME/YwUAAIDyjgUMgf9vxYoV+t///qeWLVuqadOm6tq1qySpadOmCg4OVkpKis399+zZo/DwcElSWFiY9u7da20zm81KTU21tgMAAAAAAABwTYTowP/38ssva926dVq+fLmWL1+u2bNnS5KWL1+uhx9+WGlpaUpISNC5c+e0du1abd68WbGxsZKk7t27KykpSdu3b1dWVpamTJkiHx8fRUVFOfOUAAAAAAAAAFwjlnMB/j9/f3/5+/tbb+fm5kqSbrzxRknSrFmzNHbsWE2ePFnVq1fX5MmTrV822rJlS8XHx2vYsGE6ceKEQkNDNXv2bHl7e5f+iQAAAAAAAABwGEJ0wECNGjW0f/9+6+2IiAgtX77c8P5xcXGKi4srjdIAAAAAAAAAlBKWcwEAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAHCQo0ePqm/fvrr77rt1zz33KD4+XidPnpQkbdu2TZ06dVJYWJgeeOABrVixwuax8+bNU+vWrRUeHq6YmBilpKRY286fP6+RI0fq7rvvVqNGjTRgwAClp6eX6rkBAABcrwjRAQAAAMBB+vbtq4CAAG3atEkrVqzQoUOHNGnSJB07dkx9+/ZVdHS0du7cqeHDh2vEiBHas2ePJGnDhg2aNm2axo8frx07digyMlJ9+vTR2bNnJUlvvvmmdu/eraSkJH355ZfKycnRK6+84sxTBQAAuG4QogMAAACAA5w+fVqhoaEaOnSoKlasqKpVq6pz5876/vvvtXLlStWuXVs9e/aUr6+vWrVqpfvvv1+JiYmSpMTEREVHR6tZs2by9fVVv379ZDKZtHHjRuXm5mrp0qUaNGiQatasqcDAQMXHx2vTpk06duyYk88aAACg/PNwdgEAAAAAUB5UrlxZ48ePt9mWlpamm266SampqWrQoIFNW/369bVmzRpJUmpqqtq3b29tM5lMCgkJUUpKiho0aKAzZ87YPP7WW2+Vr6+vUlJSVK1atWLV5+7uJpPJdLWnV+I8PMrONV7u7m42f+PK6C/70Wf2ob/sR5/Zh/6y3/XWZ4ToAAAAAFACkpOTtXDhQk2fPl0JCQkKCQmxaQ8ICLCua56RkaGAgACbdn9/f6WnpysjI8N6+1J+fn52rYvu5+erHE/3qziT0lExsKKzSyjAz8/X2SW4FPrLfvSZfegv+9Fn9qG/7He99BkhOgAAAAA42K5du9S3b1/Fx8crMjJSCQkJhd4v/8pwoyvEi7py3J4ry0+dypb7BXOx71/asjKynF2Clbu7m/z8fHXqVLbM5jxnl1Pm0V/2o8/sQ3/Zjz6zD/1lv/LUZ4HF+CCfEB0AAAAAHGjTpk168cUX9dprr6lDhw6SpKCgIGVmZtrcLyMjQ0FBQZKkwMDAQtvr1aunG264QZKUmZmpChUqSJIsFosyMzOtbcVhNufJzWK5yrMqebm5Ze8NuNmcVybrKqvoL/vRZ/ahv+xHn9mH/rLf9dJn18eiNQAAAABQCnbv3q2XXnpJ06dPtwbokhQWFqaUlBSb++7Zs0fh4eHW9r1791rbzGazUlNTFR4erpo1ayogIMDm8fv371dOTo5CQ0NL+IwAAABAiA4AAAAADpCbm6tXX31VAwcOVPPmzW3aOnbsqLS0NCUkJOjcuXNau3atNm/erNjYWElS9+7dlZSUpO3btysrK0tTpkyRj4+PoqKi5O7urm7dumnatGk6cuSITpw4ofHjx+uhhx5SlSpVnHGqAAAA1xWWcwEAAAAAB/jxxx/122+/acKECZowYYJN29q1azVr1iyNHTtWkydPVvXq1TV58mTrl422bNlS8fHxGjZsmE6cOKHQ0FDNnj1b3t7ekqT+/fsrKytLXbt2ldlsVuvWrTV69OjSPkUAAIDrEiE6AAAAADhARESE9u/fb9h+8803a/ny5YbtcXFxiouLK7TNy8tLI0eO1MiRI6+5TgAAANiH5VwAAAAAAAAAADBAiA4AAAAAAAAAgAFCdOAyP//8s/79738rIiJCzZo108CBA/X333/ryJEjCg4OVlhYmM2fNWvWWB87b948tW7dWuHh4YqJiVFKSooTzwQAAAAAAADAtSJEBy5x/vx5Pf3002rSpIm+/fZb/fe//9Xx48c1evRonT59Wp6enkpOTrb5065dO0nShg0bNG3aNI0fP147duxQZGSk+vTpo7Nnzzr5rAAAAAAAAABcLUJ04BLnzp3T4MGD1adPH3l5ealKlSp66KGH9Ouvv+rkyZPy9/c3fGxiYqKio6PVrFkz+fr6ql+/fjKZTNq4cWMpngEAAAAAAAAARyJEBy7h7++vmJgYeXh4yGKx6ODBg1q6dKnatWunU6dOKS8vT71791aTJk304IMPau7cubJYLJKk1NRUNWjQwLovk8mkkJAQlnQBAAAAAAAAXJiHswsAyqK0tDQ9+OCDMpvNio2N1cCBA/XVV1+pVq1a6t69u6ZPn65du3ZpwIAB1uA9IyNDAQEBNvvx9/dXenp6sY/r5maSm5vJwWdTkLu7m83fKJsYJ9fBWLkGxsk1ME6lw2S69vlG/i4YKwAAAJR3hOhAIW6++Wbt3btXv//+u0aMGKEXX3xRkydPVlRUlPU+LVq0UGxsrJKSkhQTE2P4ZtSeN6lBQRUd8qa2uPz8fEvtWLh6jJPrYKxcA+PkGhinkpXj6e6wfTFWAAAAKO8I0QEDJpNJderUUXx8vKKjozV8+HAFBQXZ3KdGjRpav369JCkwMFCZmZk27RkZGapXr16xj5menlVqV6L7+fnq1Klsmc15JX48XB3GyXUwVq6BcXINjFPp8LpgvuZ9mEySl1QqYxUYWLFE9w8AAABcCSE6cIlt27ZpxIgRWrt2rTw8Lj498vIuvin87rvvdPz4cT3++OPW+x88eFA1a9aUJIWFhWnv3r3q3LmzJMlsNis1NVXR0dHFPn5enkV5eRYHnU3RzOY85eYSUJR1jJPrYKxcA+PkGhinkuVpccR84+IH/4wVAAAAyjsWMAQuERoaquzsbE2ePFnZ2dlKT0/XjBkzFBERIU9PT02aNElbt25Vbm6uvv32WyUlJVlD9e7duyspKUnbt29XVlaWpkyZIh8fH5slYAAAAAAAAAC4Fq5EBy5RuXJlzZ07VxMnTtR9990nDw8PNW3aVK+//rqqVaumV155RWPGjNGxY8dUo0YNjRgxQm3atJEktWzZUvHx8Ro2bJhOnDih0NBQzZ49W97e3k4+KwAAAAAAAABXixAduEz9+vWVkJBQaFtsbKxiY2MNHxsXF6e4uLgSqgwAAAAAAABAaWM5FwAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAduMzPP/+sf//734qIiFCzZs00cOBA/f3335Kkbdu2qVOnTgoLC9MDDzygFStW2Dx23rx5at26tcLDwxUTE6OUlBRnnAIAAAAAAAAAByFEBy5x/vx5Pf3002rSpIm+/fZb/fe//9Xx48c1evRoHTt2TH379lV0dLR27typ4cOHa8SIEdqzZ48kacOGDZo2bZrGjx+vHTt2KDIyUn369NHZs2edfFYAAAAAAAAArhYhOnCJc+fOafDgwerTp4+8vLxUpUoVPfTQQ/r111+1cuVK1a5dWz179pSvr69atWql+++/X4mJiZKkxMRERUdHq1mzZvL19VW/fv1kMpm0ceNGJ58VAAAAAAAAgKvl4ewCgLLE399fMTExkiSLxaJDhw5p6dKlateunVJTU9WgQQOb+9evX19r1qyRJKWmpqp9+/bWNpPJpJCQEKWkpKhjx47FOr6bm0lubiYHnY0xd3c3m79RNjFOroOxcg2Mk2tgnEqHyXTt8438XTBWAAAAKO8I0YFCpKWl6cEHH5TZbFZsbKwGDhyop59+WiEhITb3CwgIUHp6uiQpIyNDAQEBNu3+/v7W9uIICqrokDe1xeXn51tqx8LVY5xcB2PlGhgn18A4lawcT3eH7YuxAgAAQHlHiA4U4uabb9bevXv1+++/a8SIEXrxxRcN75sfehuF3/aE4unpWaV2Jbqfn69OncqW2ZxX4sfD1WGcXAdj5RoYJ9fAOJUOrwvma96HySR5SaUyVoGBFUt0/wAAAMCVEKIDBkwmk+rUqaP4+HhFR0crMjJSmZmZNvfJyMhQUFCQJCkwMLDQ9nr16hX7mHl5FuXlWa619GIzm/OUm0tAUdYxTq6DsXINjJNrYJxKlqfFEfONix/8M1YAAAAo71jAELjEtm3b1KZNG+Xm5lq35eVdfFPYvHlzpaSk2Nx/z549Cg8PlySFhYVp79691jaz2azU1FRrOwAAAAAAAADXQ4gOXCI0NFTZ2dmaPHmysrOzlZ6erhkzZigiIkIdO3ZUWlqaEhISdO7cOa1du1abN29WbGysJKl79+5KSkrS9u3blZWVpSlTpsjHx0dRUVFOPisAAAAAAAAAV4sQHbhE5cqVNXfuXP3888+677771L59e1WsWFFTpkzRDTfcoFmzZmnp0qVq0qSJpk6dqsmTJ1u/bLRly5aKj4/XsGHDdM899+iHH37Q7Nmz5e3t7eSzAgAAAAAAAHC1WBMduEz9+vWVkJBQaFtERISWL19u+Ni4uDjFxcWVUGUAAAAAAAAAShtXogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAACAg3zzzTdq3ry5Bg8ebLP9yJEjCg4OVlhYmM2fNWvWWO8zb948tW7dWuHh4YqJiVFKSoq17fz58xo5cqTuvvtuNWrUSAMGDFB6enqpnRcAAMD1zMPZBQAAAODq+Saudsh+smPaO2Q/wPVszpw5WrJkiWrXrl2g7fTp0/L09FRycnKhj92wYYOmTZum9957Tw0bNtQHH3ygPn36aP369apQoYLefPNN7d69W0lJSapUqZKGDRumV155Re+//35JnxYAAMB1jyvRAQAAAMABvL29DUP0kydPyt/f3/CxiYmJio6OVrNmzeTr66t+/frJZDJp48aNys3N1dKlSzVo0CDVrFlTgYGBio+P16ZNm3Ts2LGSPCUAAACIK9EBAAAAwCF69uxp2Hbq1Cnl5eWpd+/e+uGHHxQYGKhu3brp6aeflslkUmpqqtq3/7/fCDGZTAoJCVFKSooaNGigM2fOqEGDBtb2W2+9Vb6+vkpJSVG1atWKVZ+7u5tMJtPVn2AJ8/AoO9d4ubu72fyNK6O/7Eef2Yf+sh99Zh/6y37XW58RogMAAABACfP09FStWrXUvXt3TZ8+Xbt27dKAAQPk7++vmJgYZWRkKCAgwOYx/v7+Sk9PV0ZGhvX2pfz8/OxaF93Pz1c5nu7XfC4lpWJgRWeXUICfn6+zS3Ap9Jf96DP70F/2o8/sQ3/Z73rpM0J0AAAAAChhUVFRioqKst5u0aKFYmNjlZSUpJiYGMMrxIu6ctyeK8tPncqW+wVzse9f2rIyspxdgpW7u5v8/Hx16lS2zOY8Z5dT5tFf9qPP7EN/2Y8+sw/9Zb/y1GeBxfggnxAdAAAAAJygRo0aWr9+vSQpMDBQmZmZNu0ZGRmqV6+ebrjhBklSZmamKlSoIEmyWCzKzMy0thWH2ZwnN4vFMcWXgNzcsvcG3GzOK5N1lVX0l/3oM/vQX/ajz+xDf9nveumz62PRGgAAAABwonXr1mnhwoU22w4ePKiaNWtKksLCwrR3715rm9lsVmpqqsLDw1WzZk0FBAQoJSXF2r5//37l5OQoNDS0dE4AAADgOkaIDgAAAAAlzNPTU5MmTdLWrVuVm5urb7/9VklJSXr88cclSd27d1dSUpK2b9+urKwsTZkyRT4+PoqKipK7u7u6deumadOm6ciRIzpx4oTGjx+vhx56SFWqVHHymQEAAJR/LOcCAAAA+Saudsh+smPaO2Q/gCsKCwuTJOXm5kqSvvjiC0lScnKyoqKi9Morr2jMmDE6duyYatSooREjRqhNmzaSpJYtWyo+Pl7Dhg3TiRMnFBoaqtmzZ8vb21uS1L9/f2VlZalr164ym81q3bq1Ro8eXfonCQAAcB0iRAcAAAAAB0hOTr5ie2xsrGJjYw3b4+LiFBcXV2ibl5eXRo4cqZEjR15TjQAAALAfy7kAAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdLi8rKwsTZ061Xp76dKl6tKli4YMGaKTJ086sTIAAACUdcwlAQAAUBRCdLi8N954Q1u3bpUkHTp0SCNHjlRUVJRycnI0YcIEJ1cHAACAsoy5JAAAAIri4ewCgGv19ddfKzExUZK0Zs0aNW/eXP3791dmZqY6derk5OoAACicb+JqZ5cAQMwlAQAAUDSuRIfLy8rK0k033SRJ2r59u1q0aCFJCggI0KlTp5xZGgAAAMo45pIAAAAoCiE6XF5QUJCOHDmi48eP64cfftB9990nSfrzzz9VuXJlJ1cHAACAsoy5JAAAAIrCci5weZ07d1ZcXJzc3d3VokUL3XLLLcrKytJLL72k1q1bO7s8AAAAlGHMJQEAAFAUQnS4vP79++u2227T8ePH9eijj0qSPD09VadOHb300ktOrg4AAABlGXNJAAAAFIUQHS4tNzdXCQkJeuaZZ2y2e3l5aezYsVe1z6NHj+r111/Xrl275O7urvvuu0/Dhw/XqVOn1KZNG3l5edncf9KkSWrXrp0kad68eUpISNCJEycUHBys0aNHq0GDBld3cgAAAChRJTGXBAAAQPnDmuhwaR4eHpozZ47OnTvnsH327dtXAQEB2rRpk1asWKFDhw5p0qRJOn36tDw9PZWcnGzzJz9A37Bhg6ZNm6bx48drx44dioyMVJ8+fXT27FmH1QYAAADHKYm5JAAAAMofQnS4vBdffFFjxozRvn37dObMGeXk5Nj8scfp06cVGhqqoUOHqmLFiqpatao6d+6s77//XidPnpS/v7/hYxMTExUdHa1mzZrJ19dX/fr1k8lk0saNG6/1FAEAAFBCHDmXBAAAQPnEci5wea+//rpyc3O1dOnSQtt//vnnYu+rcuXKGj9+vM22tLQ03XTTTTp16pTy8vLUu3dv/fDDDwoMDFS3bt309NNPy2QyKTU1Ve3bt7c+zmQyKSQkRCkpKerYsePVnRwAAABKlCPnkgAAACifCNHh8kaOHFli+05OTtbChQs1ffp0mc1m1apVS927d9f06dO1a9cuDRgwQP7+/oqJiVFGRoYCAgJsHu/v76/09PRiH8/NzSQ3N5ODz6Igd3c3m79RNjFOroOxcg1lbZxMppJ/vXcGD49r69+yNk7llSN+/vJ34epjVZJzSQAAAJQPhOhweV26dCmR/e7atUt9+/ZVfHy8IiMjJUlRUVHW9hYtWig2NlZJSUmKiYkxfDNqz5vUoKCKpRqq+Pn5ltqxcPUYJ9fBWLmGsjJOOZ7uzi6hRFQMrOiQ/ZSVcSqvHPnz5+pjVVJzSQAAAJQfhOgoF1avXq1Fixbpr7/+0hdffKGcnBzNnz9fzzzzzFXtb9OmTXrxxRf12muvqUOHDob3q1GjhtavXy9JCgwMVGZmpk17RkaG6tWrV+zjpqdnldqV6H5+vjp1Kltmc16JHw9Xh3FyHYyVayhr4+R1wezsEkpEVkbWNT2+rI1TeeWInz+TSfKSSmWsAh304YwRR88lAQAAUL4QosPlJSYmavz48erSpYt++uknSRfD608++cS6hrk9du/erZdeeknTp09X8+bNrdvXrVun48eP6/HHH7duO3jwoGrWrClJCgsL0969e9W5c2dJktlsVmpqqqKjo4t97Lw8i/LyLHbVey3M5jzl5hJQlHWMk+tgrFxDWRknT0vpvd6XJkf1bVkZp/LKMT9/Fz/4d/WxcvRcEgAAAOWPay9gCEj6+OOP9e6772rEiBHWbdWqVdP06dOVmJho175yc3P16quvauDAgTYBuiR5enpq0qRJ2rp1q3Jzc/Xtt98qKSnJGqp3795dSUlJ2r59u7KysjRlyhT5+PjYLAEDAACAssWRc0kAAACUT1yJDpd35MgRNW3aVJLt+uMNGjTQ33//bde+fvzxR/3222+aMGGCJkyYYNO2du1avfLKKxozZoyOHTumGjVqaMSIEWrTpo0kqWXLloqPj9ewYcN04sQJhYaGavbs2fL29r7GMwQAAEBJceRcEgAAAOUTITpcXsWKFXX06FHrsir5kpOTVblyZbv2FRERof379xu2x8bGKjY21rA9Li5OcXFxdh0TAAAAzuPIuSQAAADKJ0J0uLx27drphRde0PPPPy+LxaK9e/dq7969mjVr1hW/FBQAAABgLgkAAICiEKLD5Q0dOlQTJkxQv379lJubq+joaHl4eCg2NlYvvPCCs8sDAABAGcZcEgAAAEUhRIfL8/Ly0siRIzV06FD98ccfMplMqlWrlnx9fZ1dGgAAuAo5c5PkdcEsT4vlmvaTHdPeQRWhPGMuCQAAgKIQoqNc2LJli+69916FhIRo//79mjZtmm699dYrrl8OALj+EM4CKAxzSQAAAFwJITpc3pw5czR37lzt2LFDmZmZ6tWrl26//XZt2rRJ6enp6tu3r7NLLJMcESQRIgEAAFfHXBIAAABFcXN2AcC1SkxM1KxZsyRJq1at0k033aQFCxZo7ty5WrZsmXOLAwAAQJnGXBIAAABFIUSHy/vnn3905513SpK2b9+uNm3aSJJq1aqlv//+24mVAQAAoKxjLgkAAICisJwLXF7FihV1+vRpeXt7a+fOnXryySclSadPn5aXl5eTqwMA1+WbuPqa98GyTwDKOuaSAAAAKAohOlxe8+bNNWjQIHl4eCgoKEiNGzdWbm6uZs6cqYYNGzq7PAAAAJRhzCUBAABQFJZzgct79dVXVaNGDXl6euqdd96RJGVnZ+urr77Syy+/7OTqAAAAUJYxlwQAAEBRuBIdLs/Pz0+vvfaazbbKlStr3bp1TqoIAAAAroK5JAAAAIpCiA6XtWXLlkK3V6hQQbfffrsqV65cyhUBAADAVTCXBAAAQHERosNlPfPMMzKZTLJYLAXafHx81LdvX/Xp08cJlQEAAKCsYy4JAACA4iJEh8vauHFjodvPnDmj7du3a/r06apRo4Y6dOhQypUBAACgrGMuCQAAgOIiRIfLuvnmmw3bgoOD5e/vr4ULF/LGBwAAAAUwlwQAAEBxuTm7AKCktGnTRgcOHHB2GQAAAHBBzCUBAACQjxAd5ZaXl5fMZrOzywAAAIALYi4JAACAfIToKLd27dqlmjVrOrsMAAAAuCDmkgAAAMjHmuhwWTk5OYVuP3funH744QeNHTtWTzzxRClXBQAAAFfAXBIAAADFRYgOlxUeHi6TyWTY/vDDD6tnz56lWBEAAABcBXNJAAAAFBchOlzWG2+8UegbH29vbwUHB+u2225zQlUAAABwBcwlAQAAUFyE6HBZXbt2dXYJAAAAcFHMJQEAAFBcfLEoAAAAAAAAAAAGCNEBAAAAAAAAADDAci4AAAAol3wTVztkP9kx7R2yHwAAAACuiSvR4ZL27Nlj/fePP/7ovEIAAADgcphLAgAAwB6E6HBJvXr1ktlstv4bAAAAKC7mkgAAALAHy7nAJVWtWlUxMTG65ZZbdOHCBQ0ZMsTwvpMnTy7FygAAAFDWMZcEAACAPQjR4ZLeeustffDBB/rnn38kSX///beTKwIAAICrYC4JAAAAexCiwyWFh4fr7bffliQ99NBDWrBggZMrAgAAgKtgLgkAAAB7EKLD5a1du1aSlJGRoSNHjkiS6tSpIz8/P2eWBQAAABfAXBIAAABFIUSHyzt79qyGDh2qr776ShaLRRaLRe7u7mrXrp3eeOMNeXl5ObtEAAAAlFHMJQEAAFAUQnS4vClTpujIkSOaNGmS6tSpI4vFol9//VWzZs3SO++8o8GDBzu7RAAAAJRRzCUBAABQFEJ0uLzNmzdr9uzZqlOnjnVbWFiYGjRooP79+/PGBwAAAIaYSwIAAKAobs4uALhWJ06cUI0aNQpsv/XWW/W///3PCRUBAADAVTCXBAAAQFEI0eHyatSooa1btxbYvnXrVlWrVs0JFQEAAMBVMJcEAABAUVjOBS7viSee0KBBg9S+fXvVrVtXJpNJv/zyi1avXq0XXnjB2eUBAACgDGMuCQAAgKIQosPlxcTEyNPTUx9//LE2bNggSapTp47GjRunDh06OLk6AAAAlGXMJQEAAFAUQnSUC507d1bnzp2dXQYAAABcEHNJAAAAXAlrogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAITpcWm5urjp27OjsMgAAAOCCmEsCAACgOAjR4dI8PDxksVj0yy+/OLsUAAAAuBjmkgAAACgOD2cXAFyrRx99VIMGDdK9996rGjVqyMvLy9pmMpnUrVs3J1YHAACAsoy5JAAAAIpCiA6XN3HiREnSb7/9VqCNNz4AAAC4EuaSAAAAKAohOlzevn37nF0CAAAAXBRzSQAAABSFNdFRbqSlpWnHjh3OLgMAAAAuiLkkAAAAjHAlOlxeZmamhgwZoq1bt8rDw0N79+7VP//8o6eeekpz585VtWrVnF0iAADXDd/E1df0eJPJJHm6O6gaoGjMJQEAAFAUrkSHy3vrrbeUl5enxMREubld/JGuXLmy6tevr9dff93u/R09elR9+/bV3XffrXvuuUfx8fE6efKkJGnbtm3q1KmTwsLC9MADD2jFihU2j503b55at26t8PBwxcTEKCUl5dpPEAAAACXG0XNJAAAAlD+E6HB53377rSZMmKCwsLCLV69J8vHx0bBhw7Rz506799e3b18FBARo06ZNWrFihQ4dOqRJkybp2LFj6tu3r6Kjo7Vz504NHz5cI0aM0J49eyRJGzZs0LRp0zR+/Hjt2LFDkZGR6tOnj86ePevQ8wUAAIDjOHouCQAAgPKHEB0u78SJE6patWqB7b6+vjp37pxd+zp9+rRCQ0M1dOhQVaxYUVWrVlXnzp31/fffa+XKlapdu7Z69uwpX19ftWrVSvfff78SExMlSYmJiYqOjlazZs3k6+urfv36yWQyaePGjQ45TwAAADieI+eSAAAAKJ9YEx0u75ZbbtFXX32lqKgom+2ffvqpbrnlFrv2VblyZY0fP95mW1pamm666SalpqaqQYMGNm3169fXmjVrJEmpqalq3769tc1kMikkJEQpKSnq2LFjsY7v5maSm5vJrpqvhrv7xc/PLl5sdfXH8/Dgc7iSlD9O+X+j7CqvY5V/Rea1KEuvE4567ZMcc16O6N/yKL9bHDFOjlKWfo4dxRE/f/m7cPXXPkfOJQEAAFA+EaLD5f3nP//RoEGDFBUVJbPZrDFjxiglJUXJycmaNm3aNe07OTlZCxcu1PTp05WQkKCQkBCb9oCAAKWnp0uSMjIyFBAQYNPu7+9vbS+OoKCKpRaq5Ejy8Li2L26rGFjRMcXgivz8fJ1dAoqpvI1VjgO+3LGsvU444rVPcsx5OaJ/yzNHjJOjlLWfY0dw5M+fq7/2leRcEgAAAOUDITpc3oMPPqiPP/5YCQkJqlu3rn788UfdcsstGj58uMLDw696v7t27VLfvn0VHx+vyMhIJSQkFHq//NDbKPy2JxRPT88qtSvRfSTl5pplsVz9frIyshxWEwpyd3eTn5+vTp3Kltmc5+xycAXlday8LpiveR9l6XXCUa99kmPOyxH9Wx6ZTBcDdEeMk6OUpZ9jR3HEz5/JJHlJpfLaF1iCH2SU1FwSAAAA5QchOsqF8PBwTZkyxWH727Rpk1588UW99tpr6tChgyQpKChImZmZNvfLyMhQUFCQJCkwMLDQ9nr16hX7uHl5FuXllV5iYLFIlmtIKHJzy09YWJaZzXn0tYsob2Pl6YAEsyz2x7W+9kmOOS9H9G/5dPHDZEeMk6OUxZ/ja+WYn7+LY1UeXvscPZcEAABA+UKIDpdnNpuVkJCgL7/8Un/99Ze8vb1VvXp1dejQQV27drV7f7t379ZLL72k6dOnq3nz5tbtYWFh+vzzz23uu2fPHusVSmFhYdq7d686d+5srSs1NVXR0dFXf3IAAAAoUY6eSwIAAKD8ce1vAQIkTZo0STNnzlTVqlXVrl07tW7dWhUrVtSYMWM0depUu/aVm5urV199VQMHDrQJ0CWpY8eOSktLU0JCgs6dO6e1a9dq8+bNio2NlSR1795dSUlJ2r59u7KysjRlyhT5+PgU+JIqAAAAlB2OnEsCAACgfOJKdLi81atXa86cOYqIiLDZvmPHDg0dOlSDBw8u9r5+/PFH/fbbb5owYYImTJhg07Z27VrNmjVLY8eO1eTJk1W9enVNnjzZ+mWjLVu2VHx8vIYNG6YTJ04oNDRUs2fPlre397WfJAAAAEqEI+eSAAAAKJ8I0eHyzp07pzvvvLPA9saNG+vs2bN27SsiIkL79+83bL/55pu1fPlyw/a4uDjFxcXZdUwAAAA4jyPnkgAAACifWM4FLq9Nmzb68ssvC2zfvHkzS6kAAADgiphLAgAAoChciQ6XNHPmTOu/q1SpopEjR+rzzz/XrbfeKjc3Nx06dEi7du3SE0884cQqAQAAUBYxlwQAAIA9CNHhkj7//HOb2xUqVNCBAwd04MABm22JiYl6/vnnS7s8AAAAlGHMJQEAAGAPQnS4pMJ+5RYAAAAoDuaSAAAAsAchOsqN9PR0nTt3rsD26tWrO6EaAAAAuBLmkgAAADBCiA6Xt3r1ar322ms6deqUzXaLxSKTyaSff/7ZSZUBAACgrGMuCQAAgKIQosPlTZo0SW3bttX9998vX19fZ5cDAAAAF8JcEgAAAEUhRIfLO3nypEaNGiV3d3dnlwIAAAAXw1wSAAAARXFzdgHAtbrvvvuUnJzs7DIAAADggphLAgAAoChciQ6XN3r0aPXq1UuhoaGqXr26TCaTTfvzzz/vpMoAAABQ1jGXBAAAQFEI0eHyxo4dq19++UUnTpyQj4+PTZvJZOKNDwAAAAwxlwQAAEBRCNHh8jZt2qSPP/5YERERzi4FAAAALoa5JAAAAIrCmuhweYGBgQoPD3d2GQAAAHBBjp5LfvPNN2revLkGDx5coG3btm3q1KmTwsLC9MADD2jFihU27fPmzVPr1q0VHh6umJgYpaSkWNvOnz+vkSNH6u6771ajRo00YMAApaenO6xuAAAAGCNEh8sbPHiwpk2bpuzsbGeXAgAAABfjyLnknDlzNG7cONWuXbtA27Fjx9S3b19FR0dr586dGj58uEaMGKE9e/ZIkjZs2KBp06Zp/Pjx2rFjhyIjI9WnTx+dPXtWkvTmm29q9+7dSkpK0pdffqmcnBy98sor11wzAAAAisZyLnB5H3zwgdLS0jRv3jwFBAQU+DKoLVu2OKkyAAAAlHWOnEt6e3tryZIlev3113X+/HmbtpUrV6p27drq2bOnJKlVq1a6//77lZiYqPDwcCUmJio6OlrNmjWTJPXr10+fffaZNm7cqHbt2mnp0qWaOHGiatasKUmKj49Xu3btdOzYMVWrVu1augAAAABFIESHy2vVqpU8PT2dXQYAAABckCPnkvkBeWFSU1PVoEEDm23169fXmjVrrO3t27e3tplMJoWEhCglJUUNGjTQmTNnbB5/6623ytfXVykpKYToAAAAJYwQHS6vsPUmAQAAgOIorblkRkaGQkJCbLYFBARY1zXPyMhQQECATbu/v7/S09OVkZFhvX0pPz8/u9ZFd3d3K3ClfVni4VF2Vht1d3ez+RtXRn/Zjz6zD/1lP/rMPvSX/a63PiNEh8ubOXOmYZvZbNbAgQNLsRoAAAC4EmfPJfNDbaNwu6jQ255Q3M/PVzme7sUvrpRVDKzo7BIK8PPzdXYJLoX+sh99Zh/6y370mX3oL/tdL31GiA6X9+mnn9rczsvLU2Zmpry9vVWrVi1CdAAAABgqrblkUFCQMjMzbbZlZGQoKChIkhQYGFhoe7169XTDDTdIkjIzM1WhQgVJksViUWZmprWtOE6dypb7BfPVn0QJy8rIcnYJVu7ubvLz89WpU9kym/OcXU6ZR3/Zjz6zD/1lP/rMPvSX/cpTnwUW44N8QnS4vMK+7Ons2bOaMWNGgXUnAQAAgEuV1lwyLCxMn3/+uc22PXv2KDw83Nq+d+9ede7cWdLFq+BTU1MVHR2tmjVrKiAgQCkpKapevbokaf/+/crJyVFoaGixazCb8+RmsTjmhEpAbm7ZewNuNueVybrKKvrLfvSZfegv+9Fn9qG/7He99Nn1sWgNrjsVKlRQfHy8PvjgA2eXAgAAABdTEnPJjh07Ki0tTQkJCTp37pzWrl2rzZs3KzY2VpLUvXt3JSUlafv27crKytKUKVPk4+OjqKgoubu7q1u3bpo2bZqOHDmiEydOaPz48XrooYdUpUoVh9UIAACAwnElOsotk8lk/RImAAAAwB5XM5cMCwuTJOXm5kqSvvjiC0lScnKybrjhBs2aNUtjx47V5MmTVb16dU2ePNn6ZaMtW7ZUfHy8hg0bphMnTig0NFSzZ8+Wt7e3JKl///7KyspS165dZTab1bp1a40ePdpBZwsAAIArIUSHy/vss88KbLtw4YK+/vprBQYGOqEiAAAAuApHziWTk5Ov2B4REaHly5cbtsfFxSkuLq7QNi8vL40cOVIjR460qyYAAABcO0J0uLxRo0YV2Obt7a26detydQ4AAACuiLkkAAAAikKIDpe3b98+Z5cAAAAAF8VcEgAAAEXhi0UBAAAAAAAAADDAlehwWT169JDJZLrifUwmk+bNm1dKFQEAAMBVMJcEAABAcRGiw2U1bdrUsC0vL0+ff/65jh07VooVAQAAwFUwlwQAAEBxEaLDZT3//POFbv/111/1yiuvSJLef//90iwJAAAALoK5JAAAAIqLNdFRbpjNZr3zzjt69NFHdccdd+i///2vIiMjnV0WAAAAXABzSQAAABjhSnSUC3v37tUrr7yi8+fPa+7cuWrSpImzSwIAAICLYC4JAACAK+FKdLi0nJwcTZo0SXFxcbr33nu1YsUK3vQAAACgWJhLAgAAoDi4Eh0u67vvvtOrr74qX19fffrpp2rQoIGzSwIAAICLYC4JAACA4iJEh8vq2bOngoKC1K5dO23atEmbNm0q9H5GXxoFAACA6xdzSQAAABQXITpcVkREhCRp165dhvcxmUylVQ4AAABcCHNJAAAAFBchOlzWggULnF0CAKAIvomrHbKf7Jj2DtkPAORjLgkAAIDi4otFAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6MBlvvnmGzVv3lyDBw+22X7kyBEFBwcrLCzM5s+aNWus95k3b55at26t8PBwxcTEKCUlpbTLBwAAAAAAAOBAHs4uAChL5syZoyVLlqh27doF2k6fPi1PT08lJycX+tgNGzZo2rRpeu+999SwYUN98MEH6tOnj9avX68KFSqUdOkAUK75Jq6+5n2YTCbJ090B1QAAAAAAridciQ5cwtvb2zBEP3nypPz9/Q0fm5iYqOjoaDVr1ky+vr7q16+fTCaTNm7cWJIlAwAAAAAAAChBXIkOXKJnz56GbadOnVJeXp569+6tH374QYGBgerWrZuefvppmUwmpaamqn379tb7m0wmhYSEKCUlRR07dizW8d3cTHJzM13zeRTF3f3i52cmkyRd/fE8PPgcriTlj1P+3yi7yutYmUwl/3pUmvJP51pf+yTHvP6Vt/51FEeOk6OUx//vHPHzl7+L8vbaBwAAAFyOEB0oJk9PT9WqVUvdu3fX9OnTtWvXLg0YMED+/v6KiYlRRkaGAgICbB7j7++v9PT0Yh8jKKhiqYUqOZI8PK5tWYOKgRUdUwyuyM/P19kloJjK21jllNOlT671tU9yzOtfee1fR3HEODlKefz/zpE/f+XttQ8AAAC4HCE6UExRUVGKioqy3m7RooViY2OVlJSkmJgYw/DbnlA8PT2r1K5E95GUm2uWxXL1+8nKyHJYTSjI3d1Nfn6+OnUqW2ZznrPLwRWU17HyumB2dgkOZTJdDGav9bVPcszrX3nrX0dx5Dg5Snn8/84RP38mk+QllcprX2A5/CADAAAAroMQHbgGNWrU0Pr16yVJgYGByszMtGnPyMhQvXr1ir2/vDyL8vJKLzGwWCTLNSQUubnlJywsy8zmPPraRZS3sfIsKwmmw1z8kPJaX/skyXPRf6+5mvLWu47juHFylPL0vM7nmOf3xbEqb699AAAAwOVYwBAopnXr1mnhwoU22w4ePKiaNWtKksLCwrR3715rm9lsVmpqqsLDw0u1TgAAAAAAAACOQ4gOFJOnp6cmTZqkrVu3Kjc3V99++62SkpL0+OOPS5K6d++upKQkbd++XVlZWZoyZYp8fHxsloABAAAAAAAA4FpYzgW4RFhYmCQpNzdXkvTFF19IkpKTkxUVFaVXXnlFY8aM0bFjx1SjRg2NGDFCbdq0kSS1bNlS8fHxGjZsmE6cOKHQ0FDNnj1b3t7ezjkZAAAAAAAAANeMEB24RHJy8hXbY2NjFRsba9geFxenuLg4R5cFAAAAAAAAwElYzgUAAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYMDD2QUAAFAe5MxNktcFszwtlqveR3ZMewdWBAAAAAAAHIEr0QEAAAAAAAAAMMCV6AAAAMAV+Caudsh++G0TAAAAwDVxJToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGPJxdAAAAAAAAvomrnV2ClclkUo6nu7wumJUb3c7Z5QAAACfjSnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABjwcHYBQFnzzTff6KWXXlLTpk01depUm7Zt27Zp/PjxOnTokG688Ub1799fnTp1srbPmzdPCQkJOnHihIKDgzV69Gg1aNCgtE8BAACUQb6Jq695H9kx7R1QCQAAAAB7cCU6cIk5c+Zo3Lhxql27doG2Y8eOqW/fvoqOjtbOnTs1fPhwjRgxQnv27JEkbdiwQdOmTdP48eO1Y8cORUZGqk+fPjp79mxpnwYAAAAAAAAAByFEBy7h7e2tJUuWFBqir1y5UrVr11bPnj3l6+urVq1a6f7771diYqIkKTExUdHR0WrWrJl8fX3Vr18/mUwmbdy4sbRPAwAAAAAAAICDEKIDl+jZs6cqV65caFtqamqBpVnq16+vlJSUQttNJpNCQkKs7QAAAAAAAABcD2uiA8WUkZGhkJAQm20BAQFKT0+3tgcEBNi0+/v7W9uLw83NJDc30zXXWhR394ufn5lMknT1x/Pw4HO4kpQ/Tvl/o+wqr88pk6nkX49KU/7pXOs4oWQxTlfmqNcJRzy/83fB/1MAAAAo7wjRgWuU/ybU6M2oPW9Sg4IqllpolSPJw8P9mvZRMbCiY4rBFfn5+Tq7BBRDeXxO5Xhe2/mUVdc6TigdjFPhHPU64cjnN/9PAQAAoLwjRAeKKSgoSJmZmTbbMjIyFBQUJEkKDAwstL1evXrFPkZ6elapXYnuIyk31yyL5er3k5WR5bCaUJC7u5v8/Hx16lS2zOY8Z5eDKyivzymvC2Znl+BQJtPFYPZaxwkli3G6Mke9Tjji+W0ySV5Sqfw/FVjGPmQEAADA9YUQHSimsLAwff755zbb9uzZo/DwcGv73r171blzZ0mS2WxWamqqoqOji32MvDyL8vJKLzGwWCTLNSQUubkEu6XBbM6jr11EeXtOeZa7BPPih5TXOk4oaYzTlTjqdcIxz++LY8X/UwAAACjvWMAQKKaOHTsqLS1NCQkJOnfunNauXavNmzcrNjZWktS9e3clJSVp+/btysrK0pQpU+Tj46OoqCgnVw4AAAAAAADganElOnCJsLAwSVJubq4k6YsvvpAkJScn64YbbtCsWbM0duxYTZ48WdWrV9fkyZOtXzbasmVLxcfHa9iwYTpx4oRCQ0M1e/ZseXt7O+dkgHLON3G1Q/aTHdPeIfsBAAAAAADlEyE6cInk5OQrtkdERGj58uWG7XFxcYqLi3N0WQCuE3wwAAAAAABA2cNyLgAAAAAAAAAAGCBEBwAAAIBS0Lp1a4WGhiosLMz6Z+zYsZKkbdu2qVOnTgoLC9MDDzygFStW2Dx23rx5at26tcLDwxUTE6OUlBRnnAIAAMB1ieVcAAAAAKAUnDp1SvPnz1fjxo1tth87dkx9+/bVCy+8oJiYGO3YsUMDBw5UnTp1FB4erg0bNmjatGl677331LBhQ33wwQfq06eP1q9frwoVKjjpbAAAAK4fXIkOAAAAACXMbDYrKytL/v7+BdpWrlyp2rVrq2fPnvL19VWrVq10//33KzExUZKUmJio6OhoNWvWTL6+vurXr59MJpM2btxY2qcBAABwXSJEBwAAAIASdurUKVksFs2YMUP33nuv7r33Xr366qs6c+aMUlNT1aBBA5v7169f37pky+XtJpNJISEhLOkCAABQSljOBQAAAABKWE5Ojho0aKCwsDCNGzdOf//9twYOHKjRo0crIyNDISEhNvcPCAhQenq6JCkjI0MBAQE27f7+/tb24nJ3d5PJZLqm87he5HeTySR5eHDtWVHc3d1s/kbR6DP70F/2o8/sQ3/Z73rrM0J0AAAAAChh1apV0+eff269XalSJQ0dOlTPPfecIiIiCn1MfuBtFHzbG4j7+fkqx9Pdrsdc7zw83FUhsKKzy3AZfn6+zi7B5dBn9qG/7Eef2Yf+st/10meE6AAAAADgBDVq1FBeXp7c3NyUmZlp05aRkaGgoCBJUmBgYKHt9erVs+t4p05ly/2C+VpKvm5cvALdXbm5ZmVlZDm7nDLP3d1Nfn6+OnUqW2ZznrPLcQn0mX3oL/vRZ/ahv+xXnvossBgfmBOiAwAAAEAJ27dvn5YtW6aXX37Zuu3gwYPy8vJSZGSkli1bZnP/PXv2KDw8XJIUFhamvXv3qnPnzpIufklpamqqoqOj7arBbM6Tm8VyTedx/bh4lb/FIuXmunYwUJrM5jz6y070mX3oL/vRZ/ahv+x3vfTZ9bFoDQAAAAA4UWBgoD777DPNnj1bOTk5Onz4sN5++23FxcXpkUceUVpamhISEnTu3DmtXbtWmzdvVmxsrCSpe/fuSkpK0vbt25WVlaUpU6bIx8dHUVFRTj4rAACA6wNXogMAAABACatWrZpmz56tt956S++9954CAwPVvn17DRgwQF5eXpo1a5bGjh2ryZMnq3r16po8ebL1y0Zbtmyp+Ph4DRs2TCdOnFBoaKhmz54tb29vJ58VAADA9YEQHQAAAABKQZMmTfTZZ58V2hYREaHly5cbPjYuLk5xcXElVRoAAACugOVcAAAAAAAAAAAwQIgOAAAAAAAAAIABlnMBAKCc8U1c7ewSAAAAAAAoN7gSHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGDAw9kFAADgTL6Jq695HyaTSfJ0d0A1AAAAAACgrOFKdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAwP9r787jqq7zPY6/D5sSrmjXTCvMum6AgiK5A7mnoimuqVOOZg9N4kJm6k0bl2nR8Rr1cCRTG8dr5kaaiY3luOWCMrmmpkiOG06KMioIHH73Dx6cG8oPOIoc8Lyej4cPH+e3fs73+z0/vr/P+f6+B4AJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJN0cHAAAwl7VojTyyrXI3jPs6TkZEz1KKCAAAAAAAwLkwEh0AAAAAAAAAABMk0QEAAAAAAAAAMEESHQAAAAAAAAAAE8yJDgAoMc9V39z3MZifHQAAAAAAVCSMRAfsEBoaKl9fX/n5+dn+zZgxQ5K0e/du9enTR35+furSpYvWr1/v4GgBAAAAAAAA3C9GogN2SE9P11/+8hcFBgYWWJ6amqrXXntN//Vf/6WIiAjt3btXkZGR8vHxkb+/v4OiBQAAAAAAAHC/GIkOlJDVatXNmzdVvXr1u9Zt2LBBTz31lEaMGCFPT0+FhITo+eef16pVqxwQKQAAAAAAAIDSwkh0oITS09NlGIZiY2O1f/9+SVJISIgmTZqkY8eOqVmzZgW2b9KkiTZt2mTXOVxcLHJxsZRazGZcXfO+P7NYJOnez+fmxvdwD1Jp1ZNUenVlsdx/+yxPsZSW/FBKo67w4FBPFQP1VLTydA3NP0T+3ysAAADgYUUSHSihrKwsNWvWTH5+fpo5c6YuX76syMhITZ8+XWlpaWrcuHGB7WvUqKGrV6/adQ5vb68ySwxmSXJzc72vY3jV9CqdYGCqNOpJKr26ynJ/uGIpbaVRV3jwqKeKgXoqXHm8hlar5llqxwIAAADKI5LoQAnVqVNHa9eutb2uUqWKYmJiNHbsWLVq1arQfexNiF+9erPMRqJXlpSTY5Vh3PtxbqbdLLWYcLfSqiep9OrKI9t638coT7GUFoslL+FXGnWFB4d6qhiop6KVp2uoxSJ5SEpPz5DVmnv/QRWhJl/cAwAAwIFIogP3oX79+srNzZWLi4uuXbtWYF1aWpq8vb3tOl5urqHc3LLLGBiGZNxHhiIn58HeMCPP/daTVHp15V4KGa3yFEvpyfvyqzTqCg8S9VQxUE9FKV/X0Ly6slpz6RMAAADgoUYSHSih48ePKz4+XpMmTbItS05OloeHhzp16qT4+PgC2x86dEj+/v5lHCUAAACA0uS56htHh2AqI6Kno0MAAMAp8CtAQAnVrFlTK1euVFxcnLKyspSSkqL58+dryJAhCg8P1/nz57V06VJlZmYqISFB27dv16BBgxwdNgAAAAAAAID7QBIdKKE6deooLi5O3333nYKDg/XKK68oJCREMTExqlWrlhYuXKh169YpKChI8+bN09y5c+/6sVEAAAAAAAAAFQvTuQB2CAoK0sqVKwtd16pVK3311VdlHBFQMuX5MWQAAAAAAIDyjJHoAAAAAAAAAACYYCQ6AKBMMSoeAAAAAABUJIxEBwAAAAAAAADABCPRAQAAgAqCp3kAAACAssdIdAAAAAAAAAAATJBEBwAAAAAAAADABEl0AAAAAAAAAABMkEQHAAAAAAAAAMAESXQAAAAAAAAAAEyQRAcAAAAAAAAAwISbowMAAAAAAAD281z1jaNDkCRZLBZlubvKI9sqd8NQRkRPR4cEAECpIokOAA9AadzQWCwWyd21FKIBAAAAAADAvWI6FwAAAAAAAAAATJBEBwAAAAAAAADABEl0AAAAAAAAAABMkEQHAAAAAAAAAMAESXQAAAAAAAAAAEyQRAcAAAAAAAAAwARJdAAAAAAAAAAATJBEBwAAAAAAAADABEl0AAAAAAAAAABMuDk6AAAobzxXfePoEAAAAAAAAFBOkEQH8NAg+Q0AAAAAAIDSxnQuAAAAAAAAAACYIIkOAAAAAAAAAIAJpnMB4HBMwwIAAAAAAIDyipHoAAAAAAAAAACYYCQ6AAAAAAAoNeX5SdOMiJ6ODgEAUAExEh0AAAAAAAAAABMk0QEAAAAAAAAAMEESHQAAAAAAAAAAE8yJDgAAAAAAnEJ5ma/dYrEoy91VHtlWuRsGc7UDQDnHSHQAAAAAAAAAAEwwEh0AAAAAAMCByssI+cIwSh4AGIkOAAAAAAAAAIApkugAAAAAAAAAAJggiQ4AAAAAAAAAgAnmRAdwz8rzvH0AAAAAAABAaSCJDgAAAAAAgEKVh8FTFotFWe6u8si2yt0wJPGDpwDKFtO5AAAAAAAAAABggiQ6AAAAAAAAAAAmSKIDAAAAAAAAAGCCJDoAAAAAAAAAACZIogMAAAAAAAAAYMLN0QEAAAAAAAAA9vBc9Y2jQzCVEdHT0SEAKGUk0QEAAAAAAIBSUh4S/BaLRVnurvLItsrdMCSR3AfuB9O5AAAAAAAAAABggiQ6AAAAAAAAAAAmSKIDAAAAAAAAAGCCOdGBUnTu3DlNmzZNBw4ckKenp1588UVFR0fLxYXvqwAAAHB/6GsCAO5HeZir3Ux5na+dMkM+kuhAKTEMQ+PHj9czzzyjbdu26cqVK/r973+vWrVq6ZVXXnF0eAAAAKjA6GsCAAA4Dkl0oJQcPnxYJ06c0NKlS1W9enVVr15dY8aM0ZIlS7ixAQAAwH2hrwkAeJg5esS3xWJRlrurPLKtcjcMh8ZSUuW1zB7WEfIk0YFScuzYMdWrV081atSwLWvatKlSUlJ048YNValSxXHBAQAAoEKjrwkAACoCRyf3i3I/CX6S6EApSUtLU/Xq1Qssy3+dlpZWohsbFxeLXFwsDyS+33J1zZs302KRpAd/Ptwbi+W3/1NP5Rl1VTFQTxUD9VRx5NdVfr8CD1Zp9DVdXV1ksfC5KgmuRfahvOxHmdmH8rIfZWYfyst+FbHMHlm9qfAVrw0sdl+S6EA5UqtWGY4g+n1/eZTd2XAfqKeKg7qqGKinioF6qjiqVfN0dAgooWrVPEt0k4j/x7XIPpSX/Sgz+1Be9qPM7EN52c9ZyoxhI0ApqVWrlq5du1ZgWVpamiTJ29vbAREBAADgYUFfEwAAwHFIogOlxM/PTxcuXLDdzEjSoUOH9Mwzz8jLy8uBkQEAAKCio68JAADgOCTRgVLSpEkT+fv7a+bMmUpPT9eJEycUFxenYcOGOTo0AAAAVHD0NQEAABzHYhiG4egggIfFpUuX9M4772jv3r3y8vLS0KFDNX78eEeHBQAAgIcAfU0AAADHIIkOAAAAAAAAAIAJpnMBAAAAAAAAAMAESXQAAAAAAAAAAEyQRAcAAAAAAAAAwARJdAAAAAAAAAAATJBEBx5C586d06hRo9SiRQu1adNGH374oXJzcwvd9vPPP1doaKj8/f0VERGho0ePlnG0zs2euvrf//1fde3aVQEBAerdu7e2bNlSxtE6L3vqKV9qaqoCAgIUGxtbRlFCsq+uTp8+rWHDhql58+YKCQnR0qVLyzZYJ1bSesrNzdX8+fMVGhpqu/Z98803DojYee3YsUNt27ZVVFRUsdvSpyif7uVvmLMJDQ2Vr6+v/Pz8bP9mzJghSdq9e7f69OkjPz8/denSRevXr3dwtI5R1LWguDJyxmuDWXn985//VKNGjQq0NT8/P23atMm2jTOW17lz5/Taa6+pdevWatOmjSZOnKjr169Lon2ZMSsz2ljhfvrpJ/3ud79Tq1at9NxzzykyMlKXL1+WRBszY1ZmTt3GDAAPldzcXCM8PNyIjo42rl27Zpw+fdoIDQ01Pvvss7u2/fbbb40WLVoYu3fvNm7dumXExsYa7dq1M27evOmAyJ2PPXW1efNmo2XLlkZSUpKRnZ1trF271mjWrJnxyy+/OCBy52JPPf3W+PHjjYCAAOOjjz4qo0hhT11lZGQYYWFhxpdffmlkZmYae/bsMbp3726cOnXKAZE7F3vqadmyZUb79u2N5ORkw2q1Glu3bjWaNm1q/PTTTw6I3PnExcUZXbt2NQYPHmy88cYbRW5Ln6J8ute/Yc4mMDDQOHDgwF3LL126ZDRv3tz4/PPPjVu3bhlbt241/P39jYMHDzogSscp6lpQXBk547WhqPI6evSo0axZM9N9nbG8DMMwevXqZUyaNMm4ceOGcfnyZWPAgAHG5MmTaV9FMCsz2tjdMjMzjTZt2hgff/yxcfv2beNf//qXMXToUOO1116jjZkoqsycuY0xEh14yBw+fFgnTpzQ1KlTVb16dT399NMaM2aMVq5cede2q1at0oABA/Tcc8/J09NT48aNk8Vi0XfffeeAyJ2PPXWVmZmp6OhoBQQEyM3NTf369VOVKlV08OBBB0TuXOypp3zbtm3T6dOnFRYWVoaRwp662rRpkxo2bKiIiAhVqlRJwcHBtmV4sOypp59++kmBgYFq0KCBXFxcFBISomrVqunEiRMOiNz5VKpUSatXr9ZTTz1V7Lb0Kcqne/kb5mysVqtu3ryp6tWr37Vuw4YNeuqppzRixAh5enoqJCREzz//vFatWuWASB2nqGtBcWXkjNeGosrr+vXrhba1fM5YXv/+97/l6+urmJgYeXl56dFHH1Xfvn21f/9+2peJosqMNna3zMxMRUVF6dVXX5WHh4dq166t7t2769SpU7QxE0WVmTO3MZLowEPm2LFjqlevnmrUqGFb1rRpU6WkpOjGjRt3bdusWTPba4vFosaNGz88j9qUc/bUVZ8+fTRkyBDb6/T0dN24cUN169Ytq3Cdlj31JOV1OP7whz9o+vTpcnNzK8NIYU9d7d+/Xz4+PpowYYJatmypnj17Mk1IGbGnnkJCQpSYmKjjx48rJydHW7Zs0e3bt9W6desyjto5jRgxQlWrVi3RtvQpyid7/4Y5o/T0dBmGodjYWLVv317t27fX1KlTdePGjbvatSQ1adLE6dp1UdeC4srIGa8NRZVXenq6cnNzNWbMGAUFBalr165atGiRDMOQ5JzlVbVqVf3xj39UrVq1bMvOnz+vunXr0r5MFFVmtLG7Va9eXREREXJzc5NhGEpOTta6devUo0cP2piJosrMmdsYSXTgIZOWlnbXt4L5r9PS0u7a9rc3VfnbXr169YHGiDz21NVvGYahqVOnytfXV4GBgQ80RthfT5988omCgoJI8jmAPXWVmpqq+Ph4DRgwQLt27dKoUaMUHR2t48ePl1m8zsqeeurSpYuGDBmi8PBwNWvWTNHR0Zo9ezZfIJZD9CnKp3vtaziTrKwsNWvWTH5+fkpISNBf/vIXHTx4UNOnTy+0/GrUqEG7/o3iyohrQ0Hu7u568sknNXjwYO3YsUPTpk3TggULtHr1akmUl5T3BM3y5cv18ssv075K6LdlRhszd/78efn6+qpnz57y8/NTZGQkbawYhZWZM7cxkuiAE7NYLHYth+NlZ2crJiZGZ86c0SeffCIXFy7j5cmpU6e0bt06TZw40dGhoBg5OTkKCQlRx44dVblyZfXv31/+/v7auHGjo0PDb8THx2vdunVat26dDh8+rI8++khTpkzRoUOHHB0a7kCfAhVVnTp1tHbtWo0aNUpVqlTR008/rZiYGG3cuFE5OTmF7kO7Ll5+GXFtKCgsLEwrV65UWFiYKleurHbt2mnQoEFas2aNJMrrwIEDGjVqlCZOnKhOnTqZbkf7+n93lhltzFy9evV05MgRJSQkKDk5WW+++abptrSxPIWVmTO3MbIvwEOmVq1aunbtWoFl+SONvL29CyyvWbNmodveuR0eDHvqSsqbJuTVV19Vamqqli9fXuDxPTw49tTT9OnT9cYbb/AZchB76qp69ep3PWpdr149/frrrw80RthXT8uWLdPAgQPVtGlTeXh4qFOnTgoODlZ8fHwZRYuSok9RPtnb10Ce+vXrKzc3Vy4uLrTrYnh7exdZRlwbile/fn1b/8OZy2vr1q169dVXNW3aNA0bNkwS7as4hZVZYWhj/89iscjHx0cTJ07U119/LXd3d9pYMe4ss8JGlDtLGyOJDjxk/Pz8dOHChQKP6B46dEjPPPOMvLy87tr2yJEjttdWq1XHjh2Tv79/mcXrzOypK8MwFBUVJQ8PDy1evFjVqlUr63CdVknr6fz580pMTNSHH36o4OBgBQcHa+PGjVq0aJH69evniNCdjj2fqWbNmt01L9/58+dVr169MonVmdl77cvNzS2wLCcnh6dwyiH6FOWTPZ83Z3X8+HG99957BZYlJyfbvri782/FoUOHaNe/4efnV2QZcW0oaPPmzVq+fHmBZcnJyXriiSckOW95JSUl6a233tJHH32kF154wbac9mXOrMxoY3fbvXu3OnfuXODpovz+Zdu2bWljhSiqzBITE522jXEHAjxkmjRpIn9/f82cOVPp6ek6ceKE4uLibN9Md+/eXfv375ckDR48WGvWrNGePXt08+ZN/elPf1LlypUVFhbmyLfgNOypqw0bNujEiROaN2+ePDw8HBm20ylpPT322GPatm2bvvrqK9u/sLAwDR48WHFxcQ5+F87Bns9U3759deLECX3xxRfKysrS+vXrdfToUfXp08eRb8Ep2FNPoaGhWr16tX7++WdZrVbt3r1bu3fvVkhIiAPfAfLRpyj/ivu8IW/E3MqVKxUXF6esrCylpKRo/vz5tt9jOH/+vJYuXarMzEwlJCRo+/btGjRokKPDLjd69+5dZBlxbSjI3d1dH3zwgXbt2qWcnBz98MMPWrNmje0z6YzllZOTo6lTpyoyMlJt27YtsI72Vbiiyow2djdfX19lZGRo7ty5ysjI0NWrVxUbG6tWrVrRxkwUVWbO3MYsRv7PpwJ4aFy6dEnvvPOO9u7dKy8vLw0dOlTjx4+XJDVq1EiffvqpOnbsKElasWKF4uLidOXKFfn6+urdd9/Vs88+68jwnUpJ62rkyJFKTEyUq6trgf3Dw8M1c+ZMR4TuVOz5TP3WpEmTVK9ePb3++utlHbLTsqeuEhMTNWvWLJ05c0ZPPvmk3nzzzULrEaWvpPWUnZ2tjz/+WBs2bNDVq1f1+OOPa9SoUerfv7+D34Fz8PPzkyTbKCQ3NzdJeT9gJtGnqCiK+rwhT2JioubMmaOTJ0+qZs2a6tmzpyZMmCAPDw/t379fM2bMUHJysh5//HHFxMSoS5cujg65TBV3LSiujJzt2lBcea1cuVKLFy9Wamqq6tevr1deeUUvvviibX9nK6/9+/dr2LBhhQ4USkhI0MWLF2lfdyiuzHbu3Ekbu8NPP/2k999/X0eOHJGbm5uCg4M1efJk1alTh2uYiaLKzFmvYyTRAQAAAAAAAAAwwXQuAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAAAAAJggiQ4AAAAAAAAAgAmS6AAAAAAAAAAAmCCJDgAAAAAAAACACZLoAAAAAAAAAACYIIkOAAAAAAAAAIAJkugAAAAAAABOICwsTLGxsY4OQ5J08OBBde3aVc2bN9fBgwcdHQ4AFIkkOgAAD0BYWJjmzJlT6LpJkyZp4MCBZRxRnh07dqhRo0YaPHiwQ84PAADgzIYPH67GjRtr7969d62LjY3V8OHDHRCVY3z22Wfy8vJSYmKi/P39Tbcrrv+6detWHT582Pb65MmTSkhIKPV47XVnXAAqNpLoAAA8RLKzs4tcv2zZMvXq1UuHDh3SsWPHyiiqwhUXKwAAwMOoZs2a+u///m9lZmY6OpT7dj/9ubS0NPn4+MjDw0MWi8V0u+L6r7GxsTpy5Ijt9dq1a7V58+Z7jqu03BkXgIqNJDoAAA62adMmhYeHKyAgQEFBQRo1apSSk5Nt6xMTEzV48GA1b95c7du314QJE3ThwgXb+rCwMM2bN0+DBw9W69atTc9z9uxZbd++XaNHj1a7du3017/+9a5tUlJSNHr0aAUEBCg4OFgxMTFKS0uzrY+Pj1ePHj3k7++vLl26FDhGYaPvBw4cqEmTJknKu6EJCAjQqlWr1Lp1ay1ZskSStG3bNkVERKhly5Zq3bq1Ro8erV9++cV2jJycHM2dO1cdOnRQixYtNGDAAO3evVu5ubkKDQ3Vhx9+WOCcFy5cUOPGjbVt27Yiyx0AAMARBg4cqMqVK2v+/PlFbteoUSOtWLGiwLJ27drZpmOZN2+e+vTpo6+++kohISFq3ry5IiMjdeXKFU2YMEEBAQHq1KmTvvnmmwLHyMzM1Ntvv61WrVqpffv2mj17doFk+Lfffqvw8HD5+/srJCREU6ZMKdAfbNSokRYvXqwXXnhB/fr1M41/1apV6tOnjwICAtS5c2fNmDFDN27ckCR169ZNiYmJSkhIkJ+fn5KSkgo9RnH913bt2uno0aOaOXOmwsLCFBkZqaVLl9qOe+bMGUnSF198oW7dusnf31+dO3fW+++/r4yMDEl5/d9GjRrpb3/7m4YMGSJ/f3/16NFDR44c0bJly9ShQwe1bNlSb775pqxWq63se/bsqXXr1iksLEwBAQEaMWKEzp49W2hckrR7924NGjRILVu2VGBgoIYOHap//OMfpuUHoHwhiQ4AgAOlpqYqOjpaUVFRSkpK0vfff6/HHntMkydPliRdvHhRo0ePVnh4uBITE7V+/Xq5u7tr9OjRtk68lJfcjoyM1P79+03PtWzZMvn6+qpx48aKiIjQxo0bde3aNdt6q9WqsWPH6j/+4z+0Y8cObdq0SZcuXVJMTIwkaf/+/Zo6daomTpyopKQkzZ49Wx988IE2btxY4vebnZ2tpKQkbd26Vb///e/166+/aty4cerUqZP27NmjLVu2yGq16s0337TtExcXp2+++UaLFy/W/v379cILL+jVV1/VhQsXNGDAAK1bt67Ajd/XX3+tunXrqkOHDiWOCwAAoKy4ublp1qxZWrZsmQ4dOnRfxzl//ryOHz+uzZs3a9GiRUpISNDIkSM1cuRI7du3Tx07dtT06dNlGIZtv9WrV6tjx4764YcfNG/ePH355ZdaunSpJOnQoUOKjo7W66+/rgMHDmjFihU6d+6crT+Yb9WqVZo7d67Wr19faGzx8fGaNWuWoqKitHfvXsXFxWnfvn2242zevFlBQUHq3r27Dh8+rMDAwEKPU1z/ddeuXZKkqVOn6vvvv9f8+fMLHLdBgwZKSEjQn/70J82cOVP/+Mc/tHDhQu3cuVN//OMfbeUo5U0v88EHH2jXrl2qVKmSXn/9dV2+fFlbtmzRggULtH79em3dutW2z4ULF5SUlKQNGzbou+++k9Vq1bhx4wqNKycnR+PGjVN4eLj27dunXbt2KTg4WFFRUQX69ADKL5LoAAA4UFpamqxWq7y8vGSxWFS1alXNmDFDX3zxhSRp+fLlatiwoYYMGSIPDw95e3trypQpOnXqlBITE23Hadq0qdq0aSNXV9dCz3Pz5k2tW7fONhd7WFiYqlatqtWrV9u22blzp86cOaPo6GhVqVJF3t7emjFjhgYNGiQp7yamXbt2Cg0NlZubm4KCgvTJJ5+oQYMGJX6/2dnZGj58uLy8vOTi4qLatWtr9+7dGjt2rNzd3VWtWjV169ZNR44cUU5Oju28Q4cO1bPPPis3NzeNHDlSs2bNkpubmyIiInT9+nXbDY0kbdiwQf3795eLC90cAABQPvn5+Wn48OGaMmXKfU2JcuvWLY0fP16VKlVSUFCQvL295evrq5YtW8rd3V2dO3fW9evXdeXKFds+zZs3V48ePeTh4aGgoCB16tRJ33//vSRpyZIlCg0NVefOneXu7q66desqJiZGO3fu1D//+U/bMdq1a6fGjRub9reWLVumHj16KDQ0VB4eHnr66ac1ZswY/f3vf9fVq1dL9N5K0n8ticWLF6t///4KCgqSq6urGjZsqHHjxmnNmjUFyr5379564oknVLVqVbVt21b/+te/9Prrr6tSpUpq3bq1vL29CzwtmpmZqaioKHl5ecnb21ujR4/WyZMnlZKSUuh7ycjI0COPPCJXV1d5enpqwoQJ2rp1q2n/HUD54uboAAAAcGaNGzfW2LFj9fLLL6thw4Zq06aNQkNDFRwcLElKTk7WsWPH5OfnV2A/Nzc3nTt3zvb6iSeeKPI88fHxslqt6tmzp23/F198UStWrNArr7wiFxcXpaSk2G4C8jVo0MCWJP/ll1/UqlWrAse9l9Hed8a6Zs0arV69WhcuXFB2drZyc3NltVpltVqVkZGhq1evFtjHxcVFvXv3tr0ODQ3Vl19+qa5du+rkyZM6deqU4uLi7I4LAACgLEVGRuq7777TwoULNX78+Hs6Ro0aNeTl5WV77enpqbp169peV65cWZIKzL/esGHDAseoV6+ebUR8cnKyfv7557v6nq6urjp37pytT1Zc3/Ps2bO2fmc+Hx8fGYahs2fPFuhvmilJ/7UkkpOTdfTo0bumgjEMQxcvXrQdp379+rZ1np6eql27tjw8PAosu337tu11rVq1CryP/P0vXrwoHx+fAueqXr26pk6dqmnTpmnBggVq27atOnTooNDQ0BK9BwCORxIdAIAHwMPDw/THom7cuKFHHnnE9joqKkrDhw/Xjh07tGfPHo0dO1Y9evTQ7Nmz5eLiovbt2+vTTz8t9nxmDMPQX//6V92+fVshISG25VarVbdu3dLf//53hYWFyTCMAo/6Fnac3NzcIuO4U2Hb/zbWr7/+Wu+9957ee+899ejRQ5UqVdLq1as1ZcqUYo+Tb/DgwRo9erRSU1O1YcMGdejQocDNIwAAQHlUuXJlzZw5U6NGjVK3bt1KtM+dfaLCEsnFJZfv/BFPwzBs/TMXFxcNHDhQ06dPL/IYRfU9izpPYcsLU9L+a0m4uLjojTfe0OjRowtdnz8w5c64iivHO+si//3lf3Fxp2HDhql3797auXOn9uzZo7ffflvNmjXTp59+ymh0oALgOWcAAB6Axo0b68cff7xreWZmpg4ePChfX19JeZ3va9euqXbt2urXr5/ef/99LViwQGvWrFF6eroaNGigEydO2KY2yd/nt6PQi7Nr1y4lJydr0aJFio+Pt/3bsGGD2rZtq+XLl0vKG3V+69YtXbx40bbvmTNntGTJElmtVvn4+Oj06dMFjv3tt9/afsCzUqVKBR6JNQxDly5dKjK2pKQk+fj4qG/fvqpUqZIk6fDhw7b1VatWlbe3913n/fzzz3XixAlJeY8T16tXT19//bU2btxoe+QXAACgvGvdurX69eunyZMn35WUvbNvdevWrQLzgd+r/B/bzHf+/Hk9/vjjkvL6g0ePHi2wPiMjQ5cvX7brHD4+Pvr5558LLDt9+rRcXFz01FNPFbt/SfuvJVHYe7p+/bquX79e4mMUJi0trcAPruZPd2M2mOPq1auqVq2aevbsqT/84Q9avXq1du3apZMnT95XHADKBkl0AAAegLFjx+rUqVOaNWuWLl++LKvVqtOnT+uNN96QxWLRyJEjJeWNxO7Vq5d+/PFH5ebm6vbt2zp48KAeffRRValSRUOHDtW1a9c0Z84c3bhxQzdv3tTcuXMVERGhmzdvliiWZcuWKTAwUG3btlX9+vUL/Bs+fLh27dqlM2fOqF27dvLx8dH777+v69ev69q1a5o5c6a2b98uV1dXvfTSS9q3b582btyo7Oxs/fjjj5o0aZLtBuTZZ5/VDz/8oKysLEl5ie6MjIwiY6tXr54uX76slJQUZWVlaenSpbYbiQsXLkiSXnrpJa1YscI2T/qKFSs0d+5ceXp6SsobNTRw4EAtXLhQWVlZBUYrAQAAlHcTJ07U5cuXtXbt2gLLn3nmGW3bts32tGBsbKzpKGd7JCUlacuWLcrKytLevXu1bds225Qpv/vd73T48GEtWbJEGRkZSktL07Rp0/Tyyy/b9UTiyJEjtWnTJm3btk3Z2dk6efKk/vznP6tHjx6qUaNGsfuXtP8q5Y38TklJ0bVr12QYhipXrqzz58/r+vXrun37tl5++WX97W9/04YNG5SVlaXU1FRFRUUpOjr6nsovn4eHh+bMmaP09HRduXJFn332mVq2bKnHHnvsrrgOHDig559/Xtu3b1dOTo5ycnKUlJSkypUr27YHUL6RRAcA4AFo3LixVq5cqdTUVIWHh6tFixYaNWqUHn30Ua1atUqPPvqopLwfMBoxYoTeeustBQYGqmPHjtq3b58+/fRTubi4qG7duoqLi9OPP/6odu3aqWPHjjp+/LiWLl1aYP5LM2fPntX27ds1bNiwQteHhITo8ccf1/Lly+Xm5qYFCxbo3//+t0JCQtStWzfVqFFDc+fOlSQFBQVp1qxZ+uijjxQYGKiYmBhFRkaqT58+kmT7YaWwsDD17t1bubm5CgoKKjCK/k5DhgxR+/bt1a9fP4WGhurXX3/VggUL9J//+Z8aNGiQjh07pjFjxqhPnz4aO3asWrVqpVWrVunPf/6znnzySdtx+vfvr1u3bunFF1+Umxuz1QEAgIqjSpUqevfdd+96gu+dd97Rr7/+qk6dOqlv375q0qSJ6tevX2TfqjjZ2dl66aWXlJCQoOeee07R0dEaPny4+vfvL0ny9/fX//zP/yg+Pl7BwcHq3r27MjIytGjRIrt+tL1Xr16KiorSzJkzFRgYqHHjxqlLly6aPXt2sfva03+V8hL2X3zxhXr16qXs7GwNGjRIKSkp6tKliw4fPqzu3btr8uTJ+vjjj9WyZUv17dtXderUsfVx71Xt2rXVokULhYeH236Idc6cObb1v43Lz89PkydP1ocffqigoCA999xzWrlypRYuXKiaNWveVxwAyobFKGryUwAAgArg9OnTCg8P17fffmt7HBkAAAB4EGJjY7Vu3Tp9//33jg4FQBlhJDoAAKjQUlNT9fbbbysiIoIEOgAAAACg1JFEBwAAFda7776rrl276oknntBbb73l6HAAAAAAAA8hpnMBAAAAAAAAAMAEI9EBAAAAAAAAADBBEh0AAAAAAAAAABMk0QEAAAAAAAAAMEESHQAAAAAAAAAAEyTRAQAAAAAAAAAwQRIdAAAAAAAAAAATJNEBAAAAAAAAADBBEh0AAAAAAAAAABMk0QEAAAAAAAAAMPF/b9RuBw//exMAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize skill distribution\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Top 20 skills by attempts\n", "top_skills = skill_stats.head(20)\n", "axes[0,0].barh(range(len(top_skills)), top_skills['Total_Attempts'])\n", "axes[0,0].set_yticks(range(len(top_skills)))\n", "axes[0,0].set_yticklabels(top_skills.index, fontsize=8)\n", "axes[0,0].set_xlabel('Number of Attempts')\n", "axes[0,0].set_title('Top 20 Skills by Attempts')\n", "axes[0,0].invert_yaxis()\n", "\n", "# Accuracy distribution\n", "axes[0,1].hist(skill_stats['Accuracy'], bins=20, alpha=0.7)\n", "axes[0,1].set_xlabel('Accuracy')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "axes[0,1].set_title('Distribution of Skill Accuracy')\n", "\n", "# User performance distribution\n", "user_accuracy = df_clean.groupby('user_id')['correct'].mean()\n", "axes[1,0].hist(user_accuracy, bins=30, alpha=0.7)\n", "axes[1,0].set_xlabel('User Accuracy')\n", "axes[1,0].set_ylabel('Number of Users')\n", "axes[1,0].set_title('Distribution of User Accuracy')\n", "\n", "# Attempts per user\n", "user_attempts = df_clean.groupby('user_id').size()\n", "axes[1,1].hist(user_attempts, bins=50, alpha=0.7)\n", "axes[1,1].set_xlabel('Number of Attempts')\n", "axes[1,1].set_ylabel('Number of Users')\n", "axes[1,1].set_title('Distribution of Attempts per User')\n", "axes[1,1].set_xlim(0, user_attempts.quantile(0.95))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Split Data into Train/Test Sets\n", "\n", "Split the data for training and evaluation while maintaining temporal order."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Splitting data into train/test sets...\n", "Training set: 255,487 interactions (3,320 users)\n", "Test set: 70,150 interactions (831 users)\n", "Training accuracy: 0.655\n", "Test accuracy: 0.670\n"]}], "source": ["# Split data by users to avoid data leakage\n", "print(\"Splitting data into train/test sets...\")\n", "\n", "# Get unique users\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "\n", "# Split data based on users\n", "train_data = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_data = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "print(f\"Training set: {len(train_data):,} interactions ({len(train_users):,} users)\")\n", "print(f\"Test set: {len(test_data):,} interactions ({len(test_users):,} users)\")\n", "print(f\"Training accuracy: {train_data['correct'].mean():.3f}\")\n", "print(f\"Test accuracy: {test_data['correct'].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Train PFA Model\n", "\n", "Train the Performance Factors Analysis model on the training data."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Initializing PFA model...\n", "Training PFA model on training data...\n", "Training Enhanced PFA model...\n", "Dataset size: 255,487 interactions\n", "Unique students: 3,320\n", "Unique problems: 16,527\n", "Unique skills: 110\n", "Training set: 203,640 interactions\n", "Validation set: 51,847 interactions\n", "Initializing parameters with data-driven approach...\n", "Initialized parameters for 2656 students, 16284 problems, 108 skills\n", "Epoch 1/10\n", "  Train Loss: nan, Val Loss: nan\n", "Epoch 2/10\n", "  Train Loss: nan, Val Loss: nan\n", "Epoch 3/10\n", "  Train Loss: nan, Val Loss: nan\n", "Early stopping at epoch 3\n", "Enhanced PFA model training completed!\n", "Best epoch: 0\n", "Final training loss: nan\n", "Final validation loss: nan\n", "✅ PFA model training completed!\n"]}], "source": ["# Initialize and train PFA model\n", "print(\"Initializing PFA model...\")\n", "pfa_model = PerformanceFactorsAnalysis(learning_rate=0.1, regularization=0.01)\n", "\n", "print(\"Training PFA model on training data...\")\n", "pfa_model.fit(train_data)\n", "\n", "print(\"✅ PFA model training completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Analyze Model Parameters\n", "\n", "Analyze the learned parameters to understand student abilities, problem difficulties, and skill learning rates."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== SKILL LEARNING RATES ANALYSIS ===\n", "\n", "Top 15 skills by learning rate:\n", "                                       Skill  Learning_Rate\n", "86       Equation Solving Two or Fewer Steps       0.057634\n", "29  Conversion of Fraction Decimals Percents       0.050734\n", "67         Addition and Subtraction Integers       0.041746\n", "70        Addition and Subtraction Fractions       0.039922\n", "43                                Percent Of       0.036270\n", "47                                Proportion       0.035609\n", "13        Probability of Two Distinct Events       0.033572\n", "32                        Ordering Fractions       0.032773\n", "44                 Subtraction Whole Numbers       0.032496\n", "46                          Finding Percents       0.032373\n", "55                          Pattern Finding        0.032279\n", "87      Equation Solving More Than Two Steps       0.032212\n", "14             Probability of a Single Event       0.031155\n", "31                Ordering Positive Decimals       0.031076\n", "53                            Absolute Value       0.030550\n", "\n", "Learning rate statistics:\n", "count    110.000000\n", "mean       0.016159\n", "std        0.011809\n", "min        0.000447\n", "25%        0.006307\n", "50%        0.014223\n", "75%        0.025184\n", "max        0.057634\n", "Name: Learning_Rate, dtype: float64\n"]}], "source": ["# Analyze skill learning rates\n", "print(\"=== SKILL LEARNING RATES ANALYSIS ===\")\n", "skill_learning_rates = {}\n", "for skill in train_data['skill_name'].unique():\n", "    params = pfa_model.get_skill_parameters(skill)\n", "    skill_learning_rates[skill] = params['learning_rate']\n", "\n", "# Convert to DataFrame for analysis\n", "learning_df = pd.DataFrame(list(skill_learning_rates.items()), \n", "                          columns=['Skill', 'Learning_Rate'])\n", "learning_df = learning_df.sort_values('Learning_Rate', ascending=False)\n", "\n", "print(f\"\\nTop 15 skills by learning rate:\")\n", "print(learning_df.head(15))\n", "\n", "print(f\"\\nLearning rate statistics:\")\n", "print(learning_df['Learning_Rate'].describe())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== STUDENT ABILITIES ANALYSIS ===\n", "\n", "Top 10 students by ability:\n", "       User    Ability\n", "3308  90939  18.255705\n", "2     70677   0.000000\n", "2284  84356   0.000000\n", "2250  91953   0.000000\n", "2257  83239   0.000000\n", "2265  83291   0.000000\n", "2266  83293   0.000000\n", "2269  83303   0.000000\n", "2276  83648   0.000000\n", "2280  83664   0.000000\n", "\n", "Ability statistics:\n", "count    6.660000e+02\n", "mean     2.741097e-02\n", "std      7.073942e-01\n", "min     -6.951700e-08\n", "25%      0.000000e+00\n", "50%      0.000000e+00\n", "75%      0.000000e+00\n", "max      1.825570e+01\n", "Name: Ability, dtype: float64\n"]}], "source": ["# Analyze student abilities\n", "print(\"=== STUDENT ABILITIES ANALYSIS ===\")\n", "student_abilities = {}\n", "for user in train_data['user_id'].unique():\n", "    ability = pfa_model.get_student_ability(user)\n", "    student_abilities[user] = ability\n", "\n", "abilities_df = pd.DataFrame(list(student_abilities.items()), \n", "                           columns=['User', 'Ability'])\n", "abilities_df = abilities_df.sort_values('Ability', ascending=False)\n", "\n", "print(f\"\\nTop 10 students by ability:\")\n", "print(abilities_df.head(10))\n", "\n", "print(f\"\\nAbility statistics:\")\n", "print(abilities_df['Ability'].describe())"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PROBLEM DIFFICULTIES ANALYSIS ===\n", "\n", "Top 10 most difficult problems:\n", "      Problem  Difficulty\n", "14082  107745    4.215340\n", "14070  107809    3.999560\n", "14094  107837    3.966910\n", "5378    61116    3.805055\n", "14029  107830    3.803282\n", "14069  107865    3.783325\n", "13895  107872    3.713201\n", "14031  107823    3.686470\n", "5318    61095    3.682981\n", "14068  107724    3.658075\n", "\n", "Top 10 easiest problems:\n", "      Problem  Difficulty\n", "2466    73671   -1.992049\n", "15659   59604   -2.066486\n", "8238    49299   -2.100498\n", "5494    61097   -2.107784\n", "15629   59732   -2.120282\n", "14506  107749   -2.127958\n", "2460    74412   -2.291080\n", "15678   59692   -2.612672\n", "5520    61093   -2.844032\n", "5348    61091   -3.910070\n", "\n", "Difficulty statistics:\n", "count    16527.000000\n", "mean         0.009435\n", "std          0.353896\n", "min         -3.910070\n", "25%         -0.129084\n", "50%         -0.012826\n", "75%          0.102875\n", "max          4.215340\n", "Name: Di<PERSON>iculty, dtype: float64\n"]}], "source": ["# Analyze problem difficulties\n", "print(\"=== PROBLEM DIFFICULTIES ANALYSIS ===\")\n", "problem_difficulties = {}\n", "for problem in train_data['problem_id'].unique():\n", "    difficulty = pfa_model.get_problem_difficulty(problem)\n", "    problem_difficulties[problem] = difficulty\n", "\n", "difficulties_df = pd.DataFrame(list(problem_difficulties.items()), \n", "                              columns=['Problem', 'Difficulty'])\n", "difficulties_df = difficulties_df.sort_values('Difficulty', ascending=False)\n", "\n", "print(f\"\\nTop 10 most difficult problems:\")\n", "print(difficulties_df.head(10))\n", "\n", "print(f\"\\nTop 10 easiest problems:\")\n", "print(difficulties_df.tail(10))\n", "\n", "print(f\"\\nDifficulty statistics:\")\n", "print(difficulties_df['Difficulty'].describe())"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize parameter distributions\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# Learning rates distribution\n", "axes[0,0].hist(learning_df['Learning_Rate'], bins=30, alpha=0.7)\n", "axes[0,0].set_xlabel('Learning Rate')\n", "axes[0,0].set_ylabel('Number of Skills')\n", "axes[0,0].set_title('Distribution of Skill Learning Rates')\n", "\n", "# Student abilities distribution\n", "axes[0,1].hist(abilities_df['Ability'], bins=30, alpha=0.7)\n", "axes[0,1].set_xlabel('Student Ability')\n", "axes[0,1].set_ylabel('Number of Students')\n", "axes[0,1].set_title('Distribution of Student Abilities')\n", "\n", "# Problem difficulties distribution\n", "axes[1,0].hist(difficulties_df['Difficulty'], bins=30, alpha=0.7)\n", "axes[1,0].set_xlabel('Problem Difficulty')\n", "axes[1,0].set_ylabel('Number of Problems')\n", "axes[1,0].set_title('Distribution of Problem Difficulties')\n", "\n", "# Learning rate vs skill accuracy\n", "skill_accuracy = train_data.groupby('skill_name')['correct'].mean()\n", "learning_accuracy_df = learning_df.merge(\n", "    skill_accuracy.reset_index(), left_on='Skill', right_on='skill_name'\n", ")\n", "axes[1,1].scatter(learning_accuracy_df['Learning_Rate'], learning_accuracy_df['correct'], alpha=0.6)\n", "axes[1,1].set_xlabel('Learning Rate')\n", "axes[1,1].set_ylabel('Skill Accuracy')\n", "axes[1,1].set_title('Learning Rate vs Skill Accuracy')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Save Trained Model\n", "\n", "Save the trained PFA model for future use."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saving PFA model to output/pfa_model.joblib...\n", "✅ Model saved successfully!\n"]}], "source": ["# Save the trained model\n", "MODEL_PATH = \"output/pfa_model.joblib\"\n", "os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "\n", "print(f\"Saving PFA model to {MODEL_PATH}...\")\n", "pfa_model.save(MODEL_PATH)\n", "print(\"✅ Model saved successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Training Statistics\n", "\n", "Display comprehensive training statistics and model summary."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PFA MODEL TRAINING STATISTICS ===\n", "\n", "📊 Dataset Statistics:\n", "  • Total training interactions: 255,487\n", "  • Unique students: 3,320\n", "  • Unique problems: 16,527\n", "  • Unique skills: 110\n", "  • Training accuracy: 0.655\n", "\n", "🎯 Model Parameters:\n", "  • Learning rate: 0.1\n", "  • Regularization: 0.01\n", "  • Student ability parameters: 3,320\n", "  • Problem difficulty parameters: 16,527\n", "  • Skill learning rate parameters: 110\n", "\n", "📈 Parameter Statistics:\n", "  • Student abilities - Mean: -0.047, Std: 0.449\n", "  • Problem difficulties - Mean: 0.009, Std: 0.354\n", "  • Skill learning rates - Mean: 0.339, Std: 0.844\n", "\n", "🏆 Top 5 Skills by Learning Rate:\n", "  1. <PERSON><PERSON> - <PERSON>bt<PERSON>, <PERSON><PERSON>, and Right: 2.519\n", "  2. <PERSON><PERSON> Finding : 2.487\n", "  3. Subtraction Whole Numbers: 2.014\n", "  4. Area Trapezoid: 1.669\n", "  5. Pythagorean Theorem: 1.442\n", "\n", "🎓 Top 5 Students by Ability:\n", "  1. <PERSON><PERSON> 92250: 3.058\n", "  2. <PERSON><PERSON> 96210: 1.814\n", "  3. <PERSON><PERSON> 96211: 1.741\n", "  4. <PERSON><PERSON> 92248: 1.672\n", "  5. <PERSON><PERSON> 84981: 1.606\n", "\n", "📚 Top 5 Most Difficult Problems:\n", "  1. Problem 107745: 4.215\n", "  2. Problem 107809: 4.000\n", "  3. Problem 107837: 3.967\n", "  4. Problem 61116: 3.805\n", "  5. Problem 107830: 3.803\n"]}], "source": ["# Training statistics\n", "print(\"=== PFA MODEL TRAINING STATISTICS ===\")\n", "print(f\"\\n📊 Dataset Statistics:\")\n", "print(f\"  • Total training interactions: {len(train_data):,}\")\n", "print(f\"  • Unique students: {len(train_users):,}\")\n", "print(f\"  • Unique problems: {train_data['problem_id'].nunique():,}\")\n", "print(f\"  • Unique skills: {train_data['skill_name'].nunique():,}\")\n", "print(f\"  • Training accuracy: {train_data['correct'].mean():.3f}\")\n", "\n", "print(f\"\\n🎯 Model Parameters:\")\n", "print(f\"  • Learning rate: {pfa_model.learning_rate}\")\n", "print(f\"  • Regularization: {pfa_model.regularization}\")\n", "print(f\"  • Student ability parameters: {len(pfa_model.student_abilities):,}\")\n", "print(f\"  • Problem difficulty parameters: {len(pfa_model.problem_difficulties):,}\")\n", "print(f\"  • Skill learning rate parameters: {len(pfa_model.skill_learning_rates):,}\")\n", "\n", "print(f\"\\n📈 Parameter Statistics:\")\n", "print(f\"  • Student abilities - Mean: {abilities_df['Ability'].mean():.3f}, Std: {abilities_df['Ability'].std():.3f}\")\n", "print(f\"  • Problem difficulties - Mean: {difficulties_df['Difficulty'].mean():.3f}, Std: {difficulties_df['Difficulty'].std():.3f}\")\n", "print(f\"  • Skill learning rates - Mean: {learning_df['Learning_Rate'].mean():.3f}, Std: {learning_df['Learning_Rate'].std():.3f}\")\n", "\n", "print(f\"\\n🏆 Top 5 Skills by Learning Rate:\")\n", "for i, (skill, lr) in enumerate(learning_df.head(5).values):\n", "    print(f\"  {i+1}. {skill}: {lr:.3f}\")\n", "\n", "print(f\"\\n🎓 Top 5 Students by Ability:\")\n", "for i, (user, ability) in enumerate(abilities_df.head(5).values):\n", "    print(f\"  {i+1}. User {user}: {ability:.3f}\")\n", "\n", "print(f\"\\n📚 Top 5 Most Difficult Problems:\")\n", "for i, (problem, difficulty) in enumerate(difficulties_df.head(5).values):\n", "    print(f\"  {i+1}. Problem {problem}: {difficulty:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Prediction Examples\n", "\n", "Demonstrate how to use the trained PFA model for making predictions."]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PREDICTION EXAMPLE 1 ===\n", "Predicting performance for a user on a specific skill...\n", "No history found for user 87580 on skill Box and Whisker\n"]}], "source": ["# Example 1: Predict for a specific user and skill\n", "print(\"=== PREDICTION EXAMPLE 1 ===\")\n", "print(\"Predicting performance for a user on a specific skill...\")\n", "\n", "# Get a sample user and skill\n", "sample_user = train_users[0]\n", "sample_skill = train_data['skill_name'].iloc[0]\n", "sample_problem = train_data['problem_id'].iloc[0]\n", "\n", "# Get user's history on this skill\n", "user_skill_history = train_data[(train_data['user_id'] == sample_user) & \n", "                                (train_data['skill_name'] == sample_skill)]\n", "\n", "if len(user_skill_history) > 0:\n", "    # Create history as (problem_id, correct) tuples\n", "    history = list(zip(user_skill_history['problem_id'], user_skill_history['correct']))\n", "    \n", "    print(f\"User: {sample_user}\")\n", "    print(f\"Skill: {sample_skill}\")\n", "    print(f\"History: {history[:5]}...\")\n", "    \n", "    # Make predictions\n", "    predictions = pfa_model.predict_proba(history, sample_skill, sample_user)\n", "    \n", "    print(f\"\\nPredictions:\")\n", "    for i, (prob_id, correct) in enumerate(history[:10]):\n", "        print(f\"  Problem {prob_id}: Actual={correct}, Predicted={predictions[i]:.3f}\")\n", "else:\n", "    print(f\"No history found for user {sample_user} on skill {sample_skill}\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PREDICTION EXAMPLE 2 ===\n", "Comparing learning patterns across different skills...\n", "User 87580 has only 1 skill(s)\n"]}], "source": ["# Example 2: Compare different skills for the same user\n", "print(\"\\n=== PREDICTION EXAMPLE 2 ===\")\n", "print(\"Comparing learning patterns across different skills...\")\n", "\n", "# Get a user with multiple skills\n", "user_skills = train_data[train_data['user_id'] == sample_user]['skill_name'].unique()\n", "if len(user_skills) >= 2:\n", "    skill1, skill2 = user_skills[:2]\n", "    \n", "    # Get histories for both skills\n", "    history1 = train_data[(train_data['user_id'] == sample_user) & \n", "                          (train_data['skill_name'] == skill1)]\n", "    history2 = train_data[(train_data['user_id'] == sample_user) & \n", "                          (train_data['skill_name'] == skill2)]\n", "    \n", "    if len(history1) > 0 and len(history2) > 0:\n", "        # Create histories\n", "        h1 = list(zip(history1['problem_id'], history1['correct']))\n", "        h2 = list(zip(history2['problem_id'], history2['correct']))\n", "        \n", "        # Get predictions\n", "        pred1 = pfa_model.predict_proba(h1, skill1, sample_user)\n", "        pred2 = pfa_model.predict_proba(h2, skill2, sample_user)\n", "        \n", "        print(f\"User: {sample_user}\")\n", "        print(f\"\\nSkill 1: {skill1}\")\n", "        print(f\"  Learning rate: {pfa_model.get_skill_parameters(skill1)['learning_rate']:.3f}\")\n", "        print(f\"  Final prediction: {pred1[-1]:.3f}\")\n", "        \n", "        print(f\"\\nSkill 2: {skill2}\")\n", "        print(f\"  Learning rate: {pfa_model.get_skill_parameters(skill2)['learning_rate']:.3f}\")\n", "        print(f\"  Final prediction: {pred2[-1]:.3f}\")\n", "        \n", "        # Plot learning curves\n", "        plt.figure(figsize=(10, 6))\n", "        plt.plot(range(len(pred1)), pred1, label=skill1[:30] + '...', marker='o')\n", "        plt.plot(range(len(pred2)), pred2, label=skill2[:30] + '...', marker='s')\n", "        plt.xlabel('Attempt Number')\n", "        plt.ylabel('Predicted Probability of Success')\n", "        plt.title(f'Learning Curves for User {sample_user}')\n", "        plt.legend()\n", "        plt.grid(True, alpha=0.3)\n", "        plt.show()\n", "    else:\n", "        print(\"Insufficient data for comparison\")\n", "else:\n", "    print(f\"User {sample_user} has only {len(user_skills)} skill(s)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. <PERSON> Summary\n", "\n", "Summary of the PFA model training and key insights."]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🎉 PFA MODEL TRAINING COMPLETED SUCCESSFULLY!\n", "============================================================\n", "\n", "📋 SUMMARY:\n", "  ✅ Trained PFA model on 255,487 interactions\n", "  ✅ Learned parameters for 110 skills\n", "  ✅ Estimated abilities for 3320 students\n", "  ✅ Calculated difficulties for 16527 problems\n", "  ✅ Model saved to: output/pfa_model.joblib\n", "\n", "🔍 KEY INSIGHTS:\n", "  • Average skill learning rate: 0.339\n", "  • Most learnable skill: <PERSON>les - Obtuse, Acute, and Right (2.519)\n", "  • Student ability range: -4.271 to 3.058\n", "  • Problem difficulty range: -3.910 to 4.215\n", "\n", "🚀 NEXT STEPS:\n", "  • Use the saved model for real-time predictions\n", "  • Integrate with your educational platform\n", "  • Fine-tune parameters based on domain knowledge\n", "  • Compare with other knowledge tracing models (BKT, DKT, etc.)\n", "\n", "📚 REFERENCES:\n", "  • <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, & <PERSON>, K. <PERSON> (2009). Performance Factors Analysis\n", "  • Performance Factors Analysis: A New Alternative to Knowledge Tracing\n", "  • Online Learning: From Theory to Practice\n", "\n", "============================================================\n"]}], "source": ["print(\"🎉 PFA MODEL TRAINING COMPLETED SUCCESSFULLY!\")\n", "print(\"=\" * 60)\n", "print(\"\\n📋 SUMMARY:\")\n", "print(f\"  ✅ Trained PFA model on {len(train_data):,} interactions\")\n", "print(f\"  ✅ Learned parameters for {len(pfa_model.skill_learning_rates)} skills\")\n", "print(f\"  ✅ Estimated abilities for {len(pfa_model.student_abilities)} students\")\n", "print(f\"  ✅ Calculated difficulties for {len(pfa_model.problem_difficulties)} problems\")\n", "print(f\"  ✅ Model saved to: {MODEL_PATH}\")\n", "\n", "print(\"\\n🔍 KEY INSIGHTS:\")\n", "print(f\"  • Average skill learning rate: {learning_df['Learning_Rate'].mean():.3f}\")\n", "print(f\"  • Most learnable skill: {learning_df.iloc[0]['Skill']} ({learning_df.iloc[0]['Learning_Rate']:.3f})\")\n", "print(f\"  • Student ability range: {abilities_df['Ability'].min():.3f} to {abilities_df['Ability'].max():.3f}\")\n", "print(f\"  • Problem difficulty range: {difficulties_df['Difficulty'].min():.3f} to {difficulties_df['Difficulty'].max():.3f}\")\n", "\n", "print(\"\\n🚀 NEXT STEPS:\")\n", "print(\"  • Use the saved model for real-time predictions\")\n", "print(\"  • Integrate with your educational platform\")\n", "print(\"  • Fine-tune parameters based on domain knowledge\")\n", "print(\"  • Compare with other knowledge tracing models (BKT, DKT, etc.)\")\n", "\n", "print(\"\\n📚 REFERENCES:\")\n", "print(\"  • <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, & <PERSON>, K. <PERSON> (2009). Performance Factors Analysis\")\n", "print(\"  • Performance Factors Analysis: A New Alternative to Knowledge Tracing\")\n", "print(\"  • Online Learning: From Theory to Practice\")\n", "\n", "print(\"\\n\" + \"=\" * 60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}