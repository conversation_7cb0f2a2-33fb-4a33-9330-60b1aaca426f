{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Enhanced Performance Factors Analysis (PFA) Model Demo\n", "\n", "This notebook demonstrates the improved PFA model with advanced training techniques inspired by modern machine learning practices.\n", "\n", "## Key Improvements:\n", "1. **Adaptive Learning Rates** - Different learning rates per skill\n", "2. **Regularization** - L2 regularization to prevent overfitting\n", "3. **Multiple Epochs** - Training over multiple epochs with convergence checking\n", "4. **Early Stopping** - Prevents overfitting with validation-based early stopping\n", "5. **Separate Success/Failure Rates** - Different learning rates for correct/incorrect responses\n", "6. **Time Decay** - Forgetting factor for temporal effects\n", "7. **Better Initialization** - Data-driven parameter initialization\n", "8. **Enhanced Validation** - Comprehensive model evaluation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n"]}], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, roc_auc_score, log_loss\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Enhanced PFA model imported successfully!\n", "✅ Using improved PFA model with advanced training techniques\n"]}], "source": ["# Import the enhanced PFA model\n", "import sys\n", "import os\n", "sys.path.append('.')\n", "\n", "# Force reload to get latest changes\n", "import importlib\n", "if 'train_pfa' in sys.modules:\n", "    importlib.reload(sys.modules['train_pfa'])\n", "\n", "from train_pfa import PerformanceFactorsAnalysis, load_skill_builder_data, train_and_save_enhanced_pfa_model\n", "\n", "print(\"Enhanced PFA model imported successfully!\")\n", "print(\"✅ Using improved PFA model with advanced training techniques\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load and Prepare Dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading dataset...\n", "Dataset loaded successfully!\n", "Shape: (401756, 30)\n", "Columns: ['order_id', 'assignment_id', 'user_id', 'assistment_id', 'problem_id', 'original', 'correct', 'attempt_count', 'ms_first_response', 'tutor_mode', 'answer_type', 'sequence_id', 'student_class_id', 'position', 'type', 'base_sequence_id', 'skill_id', 'skill_name', 'teacher_id', 'school_id', 'hint_count', 'hint_total', 'overlap_time', 'template_id', 'answer_id', 'answer_text', 'first_action', 'bottom_hint', 'opportunity', 'opportunity_original']\n"]}], "source": ["# Load the dataset\n", "DATA_PATH = \"../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading dataset...\")\n", "df = load_skill_builder_data(DATA_PATH)\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cleaning dataset...\n", "Original size: 401,756\n", "Cleaned size: 325,637\n", "Removed: 76,119 rows (18.95%)\n"]}], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "original_size = len(df)\n", "\n", "# Remove rows with missing values in essential columns\n", "essential_cols = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "df_clean = df.dropna(subset=essential_cols)\n", "\n", "# Remove duplicates\n", "df_clean = df_clean.drop_duplicates()\n", "\n", "# Ensure correct data types\n", "df_clean['correct'] = df_clean['correct'].astype(int)\n", "df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "df_clean['problem_id'] = df_clean['problem_id'].astype(str)\n", "\n", "print(f\"Original size: {original_size:,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {original_size - len(df_clean):,} rows ({(original_size - len(df_clean))/original_size*100:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dataset Statistics"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Statistics:\n", "Total interactions: 325,637\n", "Unique users: 4,151\n", "Unique problems: 16,891\n", "Unique skills: 110\n", "Overall accuracy: 0.658\n"]}], "source": ["# Basic statistics\n", "print(\"Dataset Statistics:\")\n", "print(f\"Total interactions: {len(df_clean):,}\")\n", "print(f\"Unique users: {df_clean['user_id'].nunique():,}\")\n", "print(f\"Unique problems: {df_clean['problem_id'].nunique():,}\")\n", "print(f\"Unique skills: {df_clean['skill_name'].nunique():,}\")\n", "print(f\"Overall accuracy: {df_clean['correct'].mean():.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON> Enhanced PFA Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Enhanced PFA Model...\n", "==================================================\n", "Training Enhanced PFA model...\n", "Dataset size: 325,637 interactions\n", "Unique students: 4,151\n", "Unique problems: 16,891\n", "Unique skills: 110\n", "Training set: 259,776 interactions\n", "Validation set: 65,861 interactions\n", "Initializing parameters with data-driven approach...\n", "Initialized parameters for 3321 students, 16600 problems, 110 skills\n", "Epoch 1/20\n", "  Train Loss: nan, Val Loss: nan\n", "Epoch 2/20\n", "  Train Loss: nan, Val Loss: nan\n", "Epoch 3/20\n", "  Train Loss: nan, Val Loss: nan\n", "Epoch 4/20\n"]}], "source": ["# Train the enhanced PFA model with optimal configuration\n", "print(\"Training Enhanced PFA Model...\")\n", "print(\"=\" * 50)\n", "\n", "# Enhanced configuration\n", "enhanced_config = {\n", "    'learning_rate': 0.01,           # Lower learning rate for stability\n", "    'regularization': 0.001,         # L2 regularization\n", "    'max_epochs': 20,                # More epochs for better convergence\n", "    'convergence_threshold': 1e-6,   # Convergence criteria\n", "    'use_adaptive_lr': True,         # Adaptive learning rates\n", "    'use_time_decay': True,          # Time-based forgetting\n", "    'use_separate_sf_rates': True,   # Separate success/failure rates\n", "    'early_stopping_patience': 5    # Early stopping patience\n", "}\n", "\n", "# Initialize enhanced model\n", "enhanced_pfa = PerformanceFactorsAnalysis(**enhanced_config)\n", "\n", "# Train the model\n", "import time\n", "start_time = time.time()\n", "enhanced_pfa.fit(df_clean, validation_split=0.2)\n", "training_time = time.time() - start_time\n", "\n", "print(f\"\\nTraining completed in {training_time:.2f} seconds\")\n", "print(\"Enhanced PFA model training finished!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get model statistics\n", "stats = enhanced_pfa.get_model_statistics()\n", "print(\"\\n=== ENHANCED MODEL STATISTICS ===\")\n", "for key, value in stats.items():\n", "    print(f\"{key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training curves\n", "if enhanced_pfa.training_losses and enhanced_pfa.validation_losses:\n", "    plt.figure(figsize=(12, 5))\n", "    \n", "    # Training and validation loss\n", "    plt.subplot(1, 2, 1)\n", "    epochs = range(1, len(enhanced_pfa.training_losses) + 1)\n", "    plt.plot(epochs, enhanced_pfa.training_losses, 'b-', label='Training Loss', linewidth=2)\n", "    plt.plot(epochs, enhanced_pfa.validation_losses, 'r-', label='Validation Loss', linewidth=2)\n", "    plt.axvline(x=enhanced_pfa.best_epoch + 1, color='g', linestyle='--', label=f'Best Epoch ({enhanced_pfa.best_epoch + 1})')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.title('Training and Validation Loss')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Loss improvement\n", "    plt.subplot(1, 2, 2)\n", "    train_improvement = [(enhanced_pfa.training_losses[0] - loss) / enhanced_pfa.training_losses[0] * 100 \n", "                        for loss in enhanced_pfa.training_losses]\n", "    val_improvement = [(enhanced_pfa.validation_losses[0] - loss) / enhanced_pfa.validation_losses[0] * 100 \n", "                      for loss in enhanced_pfa.validation_losses]\n", "    \n", "    plt.plot(epochs, train_improvement, 'b-', label='Training Improvement', linewidth=2)\n", "    plt.plot(epochs, val_improvement, 'r-', label='Validation Improvement', linewidth=2)\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Improvement (%)')\n", "    plt.title('Loss Improvement Over Time')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"Final training loss: {enhanced_pfa.training_losses[-1]:.6f}\")\n", "    print(f\"Final validation loss: {enhanced_pfa.validation_losses[-1]:.6f}\")\n", "    print(f\"Training improvement: {train_improvement[-1]:.2f}%\")\n", "    print(f\"Validation improvement: {val_improvement[-1]:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Save Enhanced Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the enhanced model\n", "MODEL_PATH = \"models/output/enhanced_pfa_model.joblib\"\n", "enhanced_pfa.save(MODEL_PATH)\n", "print(f\"Enhanced PFA model saved to: {MODEL_PATH}\")\n", "\n", "print(\"\\n=== SUMMARY ===\")\n", "print(f\"✅ Successfully trained Enhanced PFA model\")\n", "print(f\"✅ Used advanced training techniques for better performance\")\n", "print(f\"✅ Model includes adaptive learning rates, regularization, and early stopping\")\n", "print(f\"✅ Achieved {val_improvement[-1]:.2f}% validation improvement\" if 'val_improvement' in locals() else \"\")\n", "print(f\"✅ Model ready for predictions and evaluation\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.21"}}, "nbformat": 4, "nbformat_minor": 4}