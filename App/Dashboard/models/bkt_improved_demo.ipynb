{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# BKT Model with Skill-Specific Parameter Estimation\n", "\n", "This notebook demonstrates the improved BKT model that learns different parameters for each skill."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "import sys\n", "import os\n", "sys.path.append('App/Dashboard/models')\n", "\n", "from train_bkt import BayesianKnowledgeTracer, load_skill_builder_data\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and prepare data\n", "DATA_PATH = \"App/Dashboard/datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "print(\"Loading dataset...\")\n", "df = load_skill_builder_data(DATA_PATH)\n", "\n", "# Clean data\n", "key_columns = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "essential_cols = [col for col in key_columns if col in df.columns]\n", "df_clean = df.dropna(subset=essential_cols).drop_duplicates()\n", "\n", "# Ensure correct data types\n", "if 'correct' in df_clean.columns:\n", "    df_clean['correct'] = df_clean['correct'].astype(int)\n", "if 'user_id' in df_clean.columns:\n", "    df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "\n", "print(f\"Dataset shape: {df_clean.shape}\")\n", "print(f\"Unique skills: {df_clean['skill_name'].nunique()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Split data\n", "unique_users = df_clean['user_id'].unique()\n", "train_users, test_users = train_test_split(unique_users, test_size=0.2, random_state=42)\n", "train_data = df_clean[df_clean['user_id'].isin(train_users)].copy()\n", "test_data = df_clean[df_clean['user_id'].isin(test_users)].copy()\n", "\n", "print(f\"Training set: {len(train_data):,} interactions\")\n", "print(f\"Test set: {len(test_data):,} interactions\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train improved BKT model\n", "print(\"Training BKT model with skill-specific parameter estimation...\")\n", "bkt_model = BayesianKnowledgeTracer(\n", "    p_init=0.1,   # Default values used as fallback\n", "    p_learn=0.15,\n", "    p_guess=0.25,\n", "    p_slip=0.1\n", ")\n", "\n", "import time\n", "start_time = time.time()\n", "bkt_model.fit(train_data, skill_col='skill_name', user_col='user_id', correct_col='correct')\n", "training_time = time.time() - start_time\n", "\n", "print(f\"Training completed in {training_time:.2f} seconds\")\n", "print(f\"Model trained on {len(bkt_model.skill_params)} skills\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze skill-specific parameters\n", "print(\"\\n=== SKILL-SPECIFIC PARAMETERS ===\")\n", "\n", "# Get skill statistics\n", "skill_stats = train_data.groupby('skill_name').agg({\n", "    'correct': ['count', 'mean'],\n", "    'user_id': 'nunique'\n", "}).round(3)\n", "skill_stats.columns = ['Total_Attempts', 'Accuracy', 'Unique_Users']\n", "skill_stats = skill_stats.sort_values('Total_Attempts', ascending=False)\n", "\n", "# Show parameters for top 15 skills\n", "print(\"\\nParameters for top 15 skills by attempts:\")\n", "print(f\"{'Skill':<30} {'Attempts':<8} {'Accuracy':<8} {'P(Init)':<8} {'P(Learn)':<8} {'P(Guess)':<8} {'P(Slip)':<8}\")\n", "print(\"-\" * 100)\n", "\n", "top_skills = skill_stats.head(15)\n", "for skill in top_skills.index:\n", "    if skill in bkt_model.skill_params:\n", "        params = bkt_model.skill_params[skill]\n", "        stats = skill_stats.loc[skill]\n", "        print(f\"{skill[:29]:<30} {stats['Total_Attempts']:<8.0f} {stats['Accuracy']:<8.3f} \"\n", "              f\"{params['p_init']:<8.3f} {params['p_learn']:<8.3f} \"\n", "              f\"{params['p_guess']:<8.3f} {params['p_slip']:<8.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize parameter distributions\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Extract parameters for all skills\n", "params_data = {\n", "    'p_init': [params['p_init'] for params in bkt_model.skill_params.values()],\n", "    'p_learn': [params['p_learn'] for params in bkt_model.skill_params.values()],\n", "    'p_guess': [params['p_guess'] for params in bkt_model.skill_params.values()],\n", "    'p_slip': [params['p_slip'] for params in bkt_model.skill_params.values()]\n", "}\n", "\n", "# Plot distributions\n", "axes[0,0].hist(params_data['p_init'], bins=20, alpha=0.7, color='skyblue')\n", "axes[0,0].set_title('Distribution of P(Init) across skills')\n", "axes[0,0].set_xlabel('P(Init)')\n", "axes[0,0].set_ylabel('Number of Skills')\n", "\n", "axes[0,1].hist(params_data['p_learn'], bins=20, alpha=0.7, color='lightgreen')\n", "axes[0,1].set_title('Distribution of P(Learn) across skills')\n", "axes[0,1].set_xlabel('P(Learn)')\n", "axes[0,1].set_ylabel('Number of Skills')\n", "\n", "axes[1,0].hist(params_data['p_guess'], bins=20, alpha=0.7, color='orange')\n", "axes[1,0].set_title('Distribution of P(Guess) across skills')\n", "axes[1,0].set_xlabel('<PERSON>(Guess)')\n", "axes[1,0].set_ylabel('Number of Skills')\n", "\n", "axes[1,1].hist(params_data['p_slip'], bins=20, alpha=0.7, color='pink')\n", "axes[1,1].set_title('Distribution of P(Slip) across skills')\n", "axes[1,1].set_xlabel('P(Slip)')\n", "axes[1,1].set_ylabel('Number of Skills')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Print summary statistics\n", "print(\"\\nParameter Summary Statistics:\")\n", "for param_name, values in params_data.items():\n", "    print(f\"{param_name}: mean={np.mean(values):.3f}, std={np.std(values):.3f}, \"\n", "          f\"min={np.min(values):.3f}, max={np.max(values):.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare skills with different characteristics\n", "print(\"\\n=== SKILL COMPARISON ===\")\n", "\n", "# Find skills with different parameter patterns\n", "all_skills_params = [(skill, params) for skill, params in bkt_model.skill_params.items()]\n", "\n", "# Sort by different criteria\n", "easiest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'], reverse=True)[:3]\n", "hardest_skills = sorted(all_skills_params, key=lambda x: x[1]['p_init'])[:3]\n", "fastest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'], reverse=True)[:3]\n", "slowest_learning = sorted(all_skills_params, key=lambda x: x[1]['p_learn'])[:3]\n", "\n", "print(\"Easiest skills (highest P(Init)):\")\n", "for skill, params in easiest_skills:\n", "    print(f\"  {skill}: P(Init)={params['p_init']:.3f}\")\n", "\n", "print(\"\\nHardest skills (lowest P(Init)):\")\n", "for skill, params in hardest_skills:\n", "    print(f\"  {skill}: P(Init)={params['p_init']:.3f}\")\n", "\n", "print(\"\\nFastest learning skills (highest P(Learn)):\")\n", "for skill, params in fastest_learning:\n", "    print(f\"  {skill}: P(<PERSON>rn)={params['p_learn']:.3f}\")\n", "\n", "print(\"\\nSlowest learning skills (lowest P(Learn)):\")\n", "for skill, params in slowest_learning:\n", "    print(f\"  {skill}: P(<PERSON>rn)={params['p_learn']:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the improved model\n", "MODEL_PATH = \"models/bkt_model_improved.joblib\"\n", "os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "bkt_model.save(MODEL_PATH)\n", "print(f\"\\nImproved model saved to: {MODEL_PATH}\")\n", "\n", "print(\"\\n=== SUMMARY ===\")\n", "print(f\"✅ Successfully trained BKT model with skill-specific parameters\")\n", "print(f\"✅ Each skill now has unique P(Init), P(Learn), P(Guess), and P(Slip) values\")\n", "print(f\"✅ Parameters are estimated from actual student performance data\")\n", "print(f\"✅ Model can now better capture differences between skills\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}