{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Deep Knowledge Tracing (DKT) Model Demo\n", "\n", "This notebook demonstrates the complete workflow for training a Deep Knowledge Tracing model using LSTM networks:\n", "1. Load and explore the dataset\n", "2. Data preprocessing and sequence creation\n", "3. Model architecture and training\n", "4. Training visualization and analysis\n", "5. Model evaluation and predictions\n", "6. Comparison with BKT and PFA models\n", "\n", "## What is Deep Knowledge Tracing?\n", "\n", "Deep Knowledge Tracing (DKT) uses recurrent neural networks (RNNs), specifically LSTMs, to model student learning over time. Unlike traditional models like BKT and PFA that use fixed parameters, DKT can capture complex, non-linear learning patterns and long-term dependencies in student behavior.\n", "\n", "### Key Features:\n", "- **Sequential Learning**: Models student responses as sequences\n", "- **Deep Learning**: Uses LSTM networks to capture complex patterns\n", "- **Skill Interactions**: Can model interactions between different skills\n", "- **Temporal Dependencies**: Captures long-term learning effects"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import torch\n", "import torch.nn as nn\n", "from sklearn.model_selection import train_test_split\n", "import sys\n", "import os\n", "import warnings\n", "from collections import defaultdict, Counter\n", "import time\n", "\n", "# Add models directory to path\n", "sys.path.append('App/Dashboard/models')\n", "\n", "from train_dkt import DeepKnowledgeTracing, load_skill_builder_data\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Check PyTorch and device\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")\n", "if torch.cuda.is_available():\n", "    print(f\"CUDA device: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load and Explore Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "DATA_PATH = \"App/Dashboard/datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv\"\n", "\n", "print(\"Loading dataset...\")\n", "df = load_skill_builder_data(DATA_PATH)\n", "\n", "print(f\"Dataset loaded successfully!\")\n", "print(f\"Shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display first few rows\n", "print(\"First 5 rows of the dataset:\")\n", "display(df.head())\n", "\n", "print(\"\\nDataset info:\")\n", "print(df.info())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Clean the dataset\n", "print(\"Cleaning dataset...\")\n", "essential_cols = ['user_id', 'problem_id', 'skill_name', 'correct']\n", "df_clean = df.dropna(subset=essential_cols).copy()\n", "df_clean['correct'] = df_clean['correct'].astype(int)\n", "df_clean['user_id'] = df_clean['user_id'].astype(str)\n", "\n", "print(f\"Original size: {len(df):,}\")\n", "print(f\"Cleaned size: {len(df_clean):,}\")\n", "print(f\"Removed: {len(df) - len(df_clean):,} rows ({(len(df) - len(df_clean))/len(df)*100:.2f}%)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Dataset Statistics and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Basic statistics\n", "print(\"=== DATASET STATISTICS ===\")\n", "print(f\"Total interactions: {len(df_clean):,}\")\n", "print(f\"Unique users: {df_clean['user_id'].nunique():,}\")\n", "print(f\"Unique problems: {df_clean['problem_id'].nunique():,}\")\n", "print(f\"Unique skills: {df_clean['skill_name'].nunique():,}\")\n", "print(f\"Overall accuracy: {df_clean['correct'].mean():.3f}\")\n", "\n", "# Sequence length statistics\n", "user_sequences = df_clean.groupby('user_id').size()\n", "print(f\"\\n=== SEQUENCE STATISTICS ===\")\n", "print(f\"Average sequence length: {user_sequences.mean():.1f}\")\n", "print(f\"Median sequence length: {user_sequences.median():.1f}\")\n", "print(f\"Min sequence length: {user_sequences.min()}\")\n", "print(f\"Max sequence length: {user_sequences.max()}\")\n", "print(f\"Sequences with ≥2 interactions: {(user_sequences >= 2).sum():,}\")\n", "print(f\"Sequences with ≥10 interactions: {(user_sequences >= 10).sum():,}\")\n", "print(f\"Sequences with ≥50 interactions: {(user_sequences >= 50).sum():,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize dataset statistics\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. Sequence length distribution\n", "axes[0, 0].hist(user_sequences, bins=50, alpha=0.7, edgecolor='black')\n", "axes[0, 0].set_xlabel('Sequence Length')\n", "axes[0, 0].set_ylabel('Number of Students')\n", "axes[0, 0].set_title('Distribution of Student Sequence Lengths')\n", "axes[0, 0].axvline(user_sequences.mean(), color='red', linestyle='--', label=f'Mean: {user_sequences.mean():.1f}')\n", "axes[0, 0].legend()\n", "\n", "# 2. Skill frequency\n", "skill_counts = df_clean['skill_name'].value_counts().head(15)\n", "axes[0, 1].barh(range(len(skill_counts)), skill_counts.values)\n", "axes[0, 1].set_yticks(range(len(skill_counts)))\n", "axes[0, 1].set_yticklabels([skill[:30] + '...' if len(skill) > 30 else skill for skill in skill_counts.index])\n", "axes[0, 1].set_xlabel('Number of Interactions')\n", "axes[0, 1].set_title('Top 15 Most Frequent Skills')\n", "\n", "# 3. Accuracy by skill\n", "skill_accuracy = df_clean.groupby('skill_name')['correct'].agg(['mean', 'count']).reset_index()\n", "skill_accuracy = skill_accuracy[skill_accuracy['count'] >= 100].sort_values('mean')\n", "top_skills = skill_accuracy.head(10)\n", "axes[1, 0].barh(range(len(top_skills)), top_skills['mean'])\n", "axes[1, 0].set_yticks(range(len(top_skills)))\n", "axes[1, 0].set_yticklabels([skill[:25] + '...' if len(skill) > 25 else skill for skill in top_skills['skill_name']])\n", "axes[1, 0].set_xlabel('Accuracy')\n", "axes[1, 0].set_title('10 Most Difficult Skills (≥100 interactions)')\n", "\n", "# 4. Sequence length vs accuracy\n", "user_stats = df_clean.groupby('user_id').agg({\n", "    'correct': ['mean', 'count']\n", "}).reset_index()\n", "user_stats.columns = ['user_id', 'accuracy', 'sequence_length']\n", "user_stats = user_stats[user_stats['sequence_length'] >= 5]  # Filter short sequences\n", "\n", "# Bin sequence lengths for better visualization\n", "bins = [0, 10, 25, 50, 100, float('inf')]\n", "labels = ['5-10', '11-25', '26-50', '51-100', '100+']\n", "user_stats['length_bin'] = pd.cut(user_stats['sequence_length'], bins=bins, labels=labels, include_lowest=True)\n", "\n", "sns.boxplot(data=user_stats, x='length_bin', y='accuracy', ax=axes[1, 1])\n", "axes[1, 1].set_xlabel('Sequence Length Bin')\n", "axes[1, 1].set_ylabel('Student Accuracy')\n", "axes[1, 1].set_title('Student Accuracy vs Sequence Length')\n", "axes[1, 1].tick_params(axis='x', rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Train Deep Knowledge Tracing Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize DKT model with optimal configuration\n", "print(\"Initializing Deep Knowledge Tracing Model...\")\n", "print(\"=\" * 50)\n", "\n", "# DKT configuration - optimized for the dataset\n", "dkt_config = {\n", "    'hidden_dim': 128,        # LSTM hidden dimension\n", "    'num_layers': 2,          # Number of LSTM layers\n", "    'dropout': 0.2,           # Dropout rate\n", "    'learning_rate': 0.001,   # Learning rate\n", "    'batch_size': 64,         # Batch size\n", "    'max_epochs': 30,         # Maximum epochs\n", "    'patience': 8,            # Early stopping patience\n", "    'max_seq_len': 100        # Maximum sequence length\n", "}\n", "\n", "print(f\"Configuration: {dkt_config}\")\n", "\n", "# Initialize model\n", "dkt_model = DeepKnowledgeTracing(**dkt_config)\n", "\n", "print(f\"Model initialized on device: {dkt_model.device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Train the model\n", "print(\"Training DKT Model...\")\n", "print(\"=\" * 50)\n", "\n", "# Start training\n", "start_time = time.time()\n", "dkt_model.fit(df_clean, validation_split=0.2)\n", "training_time = time.time() - start_time\n", "\n", "print(f\"\\nTraining completed in {training_time:.2f} seconds ({training_time/60:.1f} minutes)\")\n", "print(\"DKT model training finished!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Model Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get model statistics\n", "stats = dkt_model.get_model_statistics()\n", "print(\"\\n=== DKT MODEL STATISTICS ===\")\n", "for key, value in stats.items():\n", "    print(f\"{key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training curves\n", "if dkt_model.training_losses and dkt_model.validation_losses:\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "    \n", "    epochs = range(1, len(dkt_model.training_losses) + 1)\n", "    \n", "    # Loss curves\n", "    axes[0].plot(epochs, dkt_model.training_losses, 'b-', label='Training Loss', linewidth=2)\n", "    axes[0].plot(epochs, dkt_model.validation_losses, 'r-', label='Validation Loss', linewidth=2)\n", "    axes[0].axvline(dkt_model.best_epoch + 1, color='green', linestyle='--', alpha=0.7, label=f'Best Epoch ({dkt_model.best_epoch + 1})')\n", "    axes[0].set_xlabel('Epoch')\n", "    axes[0].set_ylabel('Loss')\n", "    axes[0].set_title('Training and Validation Loss')\n", "    axes[0].legend()\n", "    axes[0].grid(True, alpha=0.3)\n", "    \n", "    # Accuracy curves\n", "    axes[1].plot(epochs, dkt_model.training_accuracies, 'b-', label='Training Accuracy', linewidth=2)\n", "    axes[1].plot(epochs, dkt_model.validation_accuracies, 'r-', label='Validation Accuracy', linewidth=2)\n", "    axes[1].axvline(dkt_model.best_epoch + 1, color='green', linestyle='--', alpha=0.7, label=f'Best Epoch ({dkt_model.best_epoch + 1})')\n", "    axes[1].set_xlabel('Epoch')\n", "    axes[1].set_ylabel('Accuracy')\n", "    axes[1].set_title('Training and Validation Accuracy')\n", "    axes[1].legend()\n", "    axes[1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print improvement metrics\n", "    if len(dkt_model.training_losses) > 1:\n", "        train_improvement = ((dkt_model.training_accuracies[-1] - dkt_model.training_accuracies[0]) / dkt_model.training_accuracies[0]) * 100\n", "        val_improvement = ((dkt_model.validation_accuracies[-1] - dkt_model.validation_accuracies[0]) / dkt_model.validation_accuracies[0]) * 100\n", "        \n", "        print(f\"\\n=== TRAINING IMPROVEMENTS ===\")\n", "        print(f\"Training accuracy improvement: {train_improvement:.2f}%\")\n", "        print(f\"Validation accuracy improvement: {val_improvement:.2f}%\")\n", "        print(f\"Final training accuracy: {dkt_model.training_accuracies[-1]:.4f}\")\n", "        print(f\"Final validation accuracy: {dkt_model.validation_accuracies[-1]:.4f}\")\n", "else:\n", "    print(\"No training history available for plotting.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Model Predictions and Demo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Demo predictions with sample students and skills\n", "print(\"=== DKT MODEL PREDICTIONS DEMO ===\")\n", "\n", "# Get some sample skills from the dataset\n", "sample_skills = df_clean['skill_name'].value_counts().head(5).index.tolist()\n", "print(f\"Sample skills for demo: {sample_skills[:3]}\")\n", "\n", "# Example 1: Simulated learning progression\n", "print(\"\\n=== EXAMPLE 1: Simulated Learning Progression ===\")\n", "skill = sample_skills[0]\n", "print(f\"Skill: {skill}\")\n", "\n", "# Simulate a learning progression: starts with mistakes, then improves\n", "learning_sequence = [0, 0, 1, 0, 1, 1, 1, 1]  # 0=incorrect, 1=correct\n", "print(f\"Learning sequence: {learning_sequence}\")\n", "\n", "try:\n", "    predictions = dkt_model.predict_proba(learning_sequence, skill)\n", "    print(\"\\nLearning progression:\")\n", "    print(\"Step | Actual | Predicted | Interpretation\")\n", "    print(\"-\" * 50)\n", "    \n", "    for i, (actual, pred) in enumerate(zip(learning_sequence, predictions)):\n", "        interpretation = \"Learning\" if i > 0 and pred > predictions[i-1] else \"Struggling\" if pred < 0.5 else \"Stable\"\n", "        print(f\"{i+1:4d} | {actual:6d} | {pred:9.3f} | {interpretation}\")\n", "        \n", "except Exception as e:\n", "    print(f\"Error in prediction: {e}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example 2: Real student data\n", "print(\"\\n=== EXAMPLE 2: Real Student Data ===\")\n", "\n", "# Find a student with a reasonable sequence length\n", "user_sequences = df_clean.groupby('user_id').size()\n", "suitable_users = user_sequences[(user_sequences >= 10) & (user_sequences <= 30)].index\n", "\n", "if len(suitable_users) > 0:\n", "    sample_user = suitable_users[0]\n", "    user_data = df_clean[df_clean['user_id'] == sample_user].copy()\n", "    \n", "    # Sort by order if available\n", "    if 'order_id' in user_data.columns:\n", "        user_data = user_data.sort_values('order_id')\n", "    \n", "    # Get the most frequent skill for this user\n", "    user_skills = user_data['skill_name'].value_counts()\n", "    if len(user_skills) > 0:\n", "        sample_skill = user_skills.index[0]\n", "        skill_data = user_data[user_data['skill_name'] == sample_skill]\n", "        \n", "        if len(skill_data) >= 3:\n", "            print(f\"User: {sample_user}\")\n", "            print(f\"Skill: {sample_skill}\")\n", "            print(f\"Number of interactions: {len(skill_data)}\")\n", "            \n", "            # Create history\n", "            history = list(zip(skill_data['problem_id'], skill_data['correct']))\n", "            actual_sequence = skill_data['correct'].tolist()\n", "            \n", "            print(f\"Actual sequence: {actual_sequence}\")\n", "            \n", "            try:\n", "                predictions = dkt_model.predict_proba(history, sample_skill, sample_user)\n", "                \n", "                print(\"\\nReal student progression:\")\n", "                print(\"Step | Problem | Actual | Predicted\")\n", "                print(\"-\" * 40)\n", "                \n", "                for i, ((prob_id, actual), pred) in enumerate(zip(history[:10], predictions[:10])):\n", "                    print(f\"{i+1:4d} | {prob_id:7d} | {actual:6d} | {pred:9.3f}\")\n", "                    \n", "                # Calculate accuracy\n", "                binary_preds = [1 if p > 0.5 else 0 for p in predictions]\n", "                accuracy = sum(1 for a, p in zip(actual_sequence, binary_preds) if a == p) / len(actual_sequence)\n", "                print(f\"\\nPrediction accuracy: {accuracy:.3f}\")\n", "                \n", "            except Exception as e:\n", "                print(f\"Error in prediction: {e}\")\n", "        else:\n", "            print(\"Not enough skill-specific data for this user\")\n", "    else:\n", "        print(\"No skills found for this user\")\n", "else:\n", "    print(\"No suitable users found for demo\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Skill Analysis and Insights"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze different skills with DKT\n", "print(\"=== SKILL ANALYSIS WITH DKT ===\")\n", "\n", "# Test DKT on different difficulty levels\n", "skill_difficulty = df_clean.groupby('skill_name').agg({\n", "    'correct': ['mean', 'count']\n", "}).reset_index()\n", "skill_difficulty.columns = ['skill_name', 'accuracy', 'count']\n", "skill_difficulty = skill_difficulty[skill_difficulty['count'] >= 50]  # Filter skills with enough data\n", "\n", "# Categorize skills by difficulty\n", "easy_skills = skill_difficulty[skill_difficulty['accuracy'] > 0.8]['skill_name'].tolist()[:3]\n", "medium_skills = skill_difficulty[(skill_difficulty['accuracy'] >= 0.6) & (skill_difficulty['accuracy'] <= 0.8)]['skill_name'].tolist()[:3]\n", "hard_skills = skill_difficulty[skill_difficulty['accuracy'] < 0.6]['skill_name'].tolist()[:3]\n", "\n", "print(f\"Easy skills (>80% accuracy): {easy_skills}\")\n", "print(f\"Medium skills (60-80% accuracy): {medium_skills}\")\n", "print(f\"Hard skills (<60% accuracy): {hard_skills}\")\n", "\n", "# Test learning patterns for different difficulty levels\n", "test_sequences = {\n", "    'Struggling learner': [0, 0, 0, 1, 0, 1, 1],\n", "    'Quick learner': [0, 1, 1, 1, 1, 1, 1],\n", "    'Inconsistent learner': [1, 0, 1, 0, 1, 0, 1]\n", "}\n", "\n", "for learner_type, sequence in test_sequences.items():\n", "    print(f\"\\n=== {learner_type.upper()} ===\")\n", "    print(f\"Sequence: {sequence}\")\n", "    \n", "    # Test on easy, medium, and hard skills\n", "    for difficulty, skills in [('Easy', easy_skills), ('Medium', medium_skills), ('Hard', hard_skills)]:\n", "        if skills:\n", "            skill = skills[0]\n", "            try:\n", "                predictions = dkt_model.predict_proba(sequence, skill)\n", "                final_prob = predictions[-1] if predictions else 0.5\n", "                improvement = predictions[-1] - predictions[0] if len(predictions) > 1 else 0\n", "                print(f\"{difficulty:6s} skill ({skill[:30]}...): Final prob = {final_prob:.3f}, Improvement = {improvement:+.3f}\")\n", "            except Exception as e:\n", "                print(f\"{difficulty:6s} skill: Error - {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Save Model and Summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save the trained model\n", "MODEL_PATH = \"App/Dashboard/models/output/dkt_model.joblib\"\n", "os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)\n", "dkt_model.save(MODEL_PATH)\n", "print(f\"\\nDKT model saved to: {MODEL_PATH}\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"DEEP KNOWLEDGE TRACING MODEL TRAINING SUMMARY\")\n", "print(\"=\" * 60)\n", "print(f\"✅ Successfully trained DKT model using LSTM networks\")\n", "print(f\"✅ Model architecture: {dkt_model.num_layers} LSTM layers, {dkt_model.hidden_dim} hidden units\")\n", "print(f\"✅ Trained on {dkt_model.num_skills} unique skills\")\n", "print(f\"✅ Final validation accuracy: {dkt_model.validation_accuracies[-1]:.4f}\" if dkt_model.validation_accuracies else \"\")\n", "print(f\"✅ Model can capture sequential learning patterns and long-term dependencies\")\n", "print(f\"✅ Suitable for complex student behavior modeling\")\n", "print(f\"✅ Model saved and ready for deployment\")\n", "\n", "# Compare with traditional models\n", "print(f\"\\n=== DKT vs Traditional Models ===\")\n", "print(f\"📊 BKT: Fixed parameters, Markovian assumptions\")\n", "print(f\"📊 PFA: Logistic regression, linear learning\")\n", "print(f\"🧠 DKT: Deep learning, non-linear patterns, sequential modeling\")\n", "print(f\"\\n🎯 DKT Advantages:\")\n", "print(f\"   • Captures complex learning patterns\")\n", "print(f\"   • Models long-term dependencies\")\n", "print(f\"   • No strong parametric assumptions\")\n", "print(f\"   • Can handle variable-length sequences\")\n", "print(f\"   • Learns skill interactions automatically\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}