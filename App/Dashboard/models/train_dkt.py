import os
import pandas as pd
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers, optimizers, losses, metrics
from sklearn.base import BaseEstimator
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import accuracy_score, roc_auc_score, log_loss
from joblib import dump, load
import warnings
import time
from collections import defaultdict
warnings.filterwarnings('ignore')

# Set TensorFlow to use GPU if available
physical_devices = tf.config.list_physical_devices('GPU')
if len(physical_devices) > 0:
    tf.config.experimental.set_memory_growth(physical_devices[0], True)


def prepare_sequences_for_training(sequences, max_seq_len=200):
    """
    Prepare sequences for TensorFlow training
    """
    inputs = []
    targets = []
    lengths = []

    for sequence in sequences:
        # Pad or truncate sequence
        if len(sequence) > max_seq_len:
            sequence = sequence[-max_seq_len:]

        # Create input and target sequences
        input_seq = sequence[:-1]  # All but last
        target_seq = sequence[1:]  # All but first

        # Pad sequences if needed
        seq_len = len(input_seq)
        if seq_len < max_seq_len - 1:
            padding_len = max_seq_len - 1 - seq_len
            input_seq.extend([0] * padding_len)
            target_seq.extend([0] * padding_len)

        inputs.append(input_seq)
        targets.append(target_seq)
        lengths.append(seq_len)

    return np.array(inputs), np.array(targets), np.array(lengths)


def create_dkt_model(num_skills, hidden_dim=128, num_layers=2, dropout=0.2, max_seq_len=200):
    """
    Create Deep Knowledge Tracing Model using TensorFlow/Keras
    """
    # Input dimension: num_skills * 2 (skill + correctness)
    input_dim = num_skills * 2

    # Input layer
    inputs = keras.Input(shape=(max_seq_len - 1,), name='input_sequences')

    # Embedding layer for skill-correctness pairs
    embedded = layers.Embedding(
        input_dim=input_dim + 1,
        output_dim=hidden_dim,
        mask_zero=True,  # Handle padding
        name='embedding'
    )(inputs)

    # LSTM layers
    lstm_out = embedded
    for i in range(num_layers):
        return_sequences = (i < num_layers - 1) or True  # Always return sequences for output
        lstm_out = layers.LSTM(
            hidden_dim,
            return_sequences=return_sequences,
            dropout=dropout,
            recurrent_dropout=dropout,
            name=f'lstm_{i+1}'
        )(lstm_out)

    # Apply dropout
    lstm_out = layers.Dropout(dropout, name='dropout')(lstm_out)

    # Output layer - predict for each skill
    output = layers.Dense(num_skills, activation='sigmoid', name='output')(lstm_out)

    # Create model
    model = keras.Model(inputs=inputs, outputs=output, name='DKT_Model')

    return model


class DeepKnowledgeTracing(BaseEstimator):
    """
    Deep Knowledge Tracing model for educational data.
    
    This implementation uses LSTM networks to model student learning over time,
    predicting the probability of correct responses based on past interactions.
    """
    
    def __init__(self, hidden_dim=128, num_layers=2, dropout=0.2, learning_rate=0.001,
                 batch_size=32, max_epochs=50, patience=10, max_seq_len=200):
        # Model architecture parameters
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout = dropout

        # Training parameters
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.max_epochs = max_epochs
        self.patience = patience
        self.max_seq_len = max_seq_len

        # Check GPU availability
        self.gpu_available = len(tf.config.list_physical_devices('GPU')) > 0

        # Model components
        self.model = None
        self.skill_encoder = None
        self.num_skills = None

        # Training history
        self.training_losses = []
        self.validation_losses = []
        self.training_accuracies = []
        self.validation_accuracies = []
        self.best_epoch = 0

        print(f"DKT Model initialized with GPU: {self.gpu_available}")
    
    def _prepare_data(self, df, skill_col='skill_name', user_col='user_id', 
                     problem_col='problem_id', correct_col='correct'):
        """Prepare data for DKT training"""
        print("Preparing data for DKT...")
        
        # Clean data
        essential_cols = [user_col, problem_col, skill_col, correct_col]
        df_clean = df.dropna(subset=essential_cols).copy()
        df_clean[correct_col] = df_clean[correct_col].astype(int)
        df_clean[user_col] = df_clean[user_col].astype(str)
        
        print(f"Cleaned dataset: {len(df_clean):,} interactions")
        print(f"Unique students: {df_clean[user_col].nunique():,}")
        print(f"Unique skills: {df_clean[skill_col].nunique():,}")
        
        return df_clean
    
    def _encode_skills(self, df, skill_col='skill_name'):
        """Encode skills to integers"""
        self.skill_encoder = LabelEncoder()
        df['skill_id'] = self.skill_encoder.fit_transform(df[skill_col])
        self.num_skills = len(self.skill_encoder.classes_)
        print(f"Encoded {self.num_skills} unique skills")
        return df
    
    def _create_sequences(self, df, user_col='user_id', skill_col='skill_id', 
                         correct_col='correct'):
        """Create sequences for each student"""
        print("Creating student sequences...")
        
        sequences = []
        user_groups = df.groupby(user_col)
        
        for user_id, user_data in user_groups:
            # Sort by some order (assuming chronological if available)
            if 'order_id' in user_data.columns:
                user_data = user_data.sort_values('order_id')
            
            # Create sequence of (skill_id, correct) pairs
            user_sequence = []
            for _, row in user_data.iterrows():
                skill_id = row[skill_col]
                correct = row[correct_col]
                
                # Encode as: skill_id + (correct * num_skills)
                # This creates unique IDs for (skill, correctness) pairs
                encoded_interaction = skill_id + 1 + (correct * self.num_skills)
                user_sequence.append(encoded_interaction)
            
            # Only include sequences with at least 2 interactions
            if len(user_sequence) >= 2:
                sequences.append(user_sequence)
        
        print(f"Created {len(sequences)} student sequences")
        print(f"Average sequence length: {np.mean([len(seq) for seq in sequences]):.1f}")
        
        return sequences
    
    def _split_sequences(self, sequences, validation_split=0.2):
        """Split sequences into train and validation sets"""
        np.random.shuffle(sequences)
        split_idx = int(len(sequences) * (1 - validation_split))
        
        train_sequences = sequences[:split_idx]
        val_sequences = sequences[split_idx:]
        
        print(f"Training sequences: {len(train_sequences)}")
        print(f"Validation sequences: {len(val_sequences)}")
        
        return train_sequences, val_sequences

    def fit(self, df, skill_col='skill_name', user_col='user_id',
            problem_col='problem_id', correct_col='correct', validation_split=0.2):
        """Train the DKT model"""
        print("Training Deep Knowledge Tracing model...")
        print(f"Dataset size: {len(df):,} interactions")

        # Prepare data
        df_clean = self._prepare_data(df, skill_col, user_col, problem_col, correct_col)
        df_encoded = self._encode_skills(df_clean, skill_col)

        # Create sequences
        sequences = self._create_sequences(df_encoded, user_col, 'skill_id', correct_col)
        train_sequences, val_sequences = self._split_sequences(sequences, validation_split)

        # Prepare data for TensorFlow
        train_inputs, train_targets, train_lengths = prepare_sequences_for_training(
            train_sequences, self.max_seq_len
        )
        val_inputs, val_targets, val_lengths = prepare_sequences_for_training(
            val_sequences, self.max_seq_len
        )

        print(f"Training data shape: {train_inputs.shape}")
        print(f"Validation data shape: {val_inputs.shape}")

        # Initialize model
        self.model = create_dkt_model(
            num_skills=self.num_skills,
            hidden_dim=self.hidden_dim,
            num_layers=self.num_layers,
            dropout=self.dropout,
            max_seq_len=self.max_seq_len
        )

        # Compile model
        self.model.compile(
            optimizer=optimizers.Adam(learning_rate=self.learning_rate),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        # Prepare targets for training
        train_targets_processed = self._prepare_targets_for_training(
            train_inputs, train_targets, train_lengths
        )
        val_targets_processed = self._prepare_targets_for_training(
            val_inputs, val_targets, val_lengths
        )

        print(f"\nStarting training with GPU: {self.gpu_available}...")
        print(f"Model parameters: {self.model.count_params():,}")

        # Setup callbacks
        early_stopping = keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=self.patience,
            restore_best_weights=True,
            verbose=1
        )

        # Custom callback to track training history
        class TrainingHistoryCallback(keras.callbacks.Callback):
            def __init__(self, dkt_instance):
                super().__init__()
                self.dkt_instance = dkt_instance

            def on_epoch_end(self, epoch, logs=None):
                self.dkt_instance.training_losses.append(logs.get('loss', 0))
                self.dkt_instance.validation_losses.append(logs.get('val_loss', 0))
                self.dkt_instance.training_accuracies.append(logs.get('accuracy', 0))
                self.dkt_instance.validation_accuracies.append(logs.get('val_accuracy', 0))

        history_callback = TrainingHistoryCallback(self)

        # Train the model
        history = self.model.fit(
            train_inputs,
            train_targets_processed,
            batch_size=self.batch_size,
            epochs=self.max_epochs,
            validation_data=(val_inputs, val_targets_processed),
            callbacks=[early_stopping, history_callback],
            verbose=1
        )

        # Find best epoch
        if 'val_loss' in history.history:
            self.best_epoch = np.argmin(history.history['val_loss'])
            best_val_loss = min(history.history['val_loss'])
        else:
            best_val_loss = 0.0

        print(f"\nTraining completed!")
        print(f"Best epoch: {self.best_epoch + 1}")
        print(f"Best validation loss: {best_val_loss:.4f}")

        return self

    def _prepare_targets_for_training(self, inputs, targets, lengths):
        """
        Prepare targets for TensorFlow training by creating skill-specific targets
        """
        batch_size, seq_len = inputs.shape
        processed_targets = np.zeros((batch_size, seq_len, self.num_skills))

        for i in range(batch_size):
            seq_length = lengths[i]
            for j in range(seq_length):
                target_interaction = targets[i, j]
                if target_interaction == 0:  # Padding
                    continue

                # Decode target: skill_id = (target - 1) % num_skills
                target_skill = (target_interaction - 1) % self.num_skills
                target_correct = (target_interaction - 1) // self.num_skills

                processed_targets[i, j, target_skill] = target_correct

        return processed_targets



    def predict_proba(self, user_history, skill, user_id=None):
        """
        Predict probability of correct response for next interaction

        Args:
            user_history: List of (problem_id, correct) tuples or list of correct values
            skill: Skill name to predict for
            user_id: User ID (optional, for compatibility)

        Returns:
            List of predicted probabilities
        """
        if self.model is None:
            raise ValueError("Model not trained. Call fit() first.")

        # Encode skill
        if skill not in self.skill_encoder.classes_:
            print(f"Warning: Skill '{skill}' not seen during training")
            return [0.5] * len(user_history)

        skill_id = self.skill_encoder.transform([skill])[0]

        # Process user history
        if len(user_history) == 0:
            return []

        # Convert history to sequence
        sequence = []
        for item in user_history:
            if isinstance(item, tuple):
                # (problem_id, correct) format
                correct = item[1]
            else:
                # Just correct value
                correct = item

            # Encode interaction
            encoded_interaction = skill_id + 1 + (correct * self.num_skills)
            sequence.append(encoded_interaction)

        # Prepare input for TensorFlow
        # Pad sequence to max_seq_len - 1
        padded_sequence = sequence[:]
        if len(padded_sequence) < self.max_seq_len - 1:
            padded_sequence.extend([0] * (self.max_seq_len - 1 - len(padded_sequence)))
        elif len(padded_sequence) > self.max_seq_len - 1:
            padded_sequence = padded_sequence[-(self.max_seq_len - 1):]

        input_seq = np.array([padded_sequence])  # Shape: (1, seq_len)

        # Make prediction
        outputs = self.model.predict(input_seq, verbose=0)  # Shape: (1, seq_len, num_skills)

        # Get predictions for the target skill
        predictions = outputs[0, :len(sequence), skill_id]

        return predictions.tolist()

    def get_model_statistics(self):
        """Get model statistics"""
        stats = {
            'model_type': 'Deep Knowledge Tracing (DKT)',
            'num_skills': self.num_skills,
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'dropout': self.dropout,
            'max_seq_len': self.max_seq_len,
            'gpu_available': self.gpu_available,
            'total_parameters': self.model.count_params() if self.model else 0,
            'trainable_parameters': self.model.count_params() if self.model else 0,
            'best_epoch': self.best_epoch + 1 if hasattr(self, 'best_epoch') else 0,
            'training_epochs': len(self.training_losses),
            'final_train_loss': self.training_losses[-1] if self.training_losses else 0,
            'final_val_loss': self.validation_losses[-1] if self.validation_losses else 0,
            'final_train_acc': self.training_accuracies[-1] if self.training_accuracies else 0,
            'final_val_acc': self.validation_accuracies[-1] if self.validation_accuracies else 0,
        }
        return stats

    def save(self, path):
        """Save the trained model"""
        if self.model is None:
            raise ValueError("No model to save. Train the model first.")

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # Save TensorFlow model
        model_dir = path.replace('.joblib', '_tf_model')
        self.model.save(model_dir)

        # Prepare metadata
        model_data = {
            # Model architecture
            'hidden_dim': self.hidden_dim,
            'num_layers': self.num_layers,
            'dropout': self.dropout,
            'num_skills': self.num_skills,
            'max_seq_len': self.max_seq_len,

            # Training parameters
            'learning_rate': self.learning_rate,
            'batch_size': self.batch_size,
            'max_epochs': self.max_epochs,
            'patience': self.patience,

            # Model components
            'skill_encoder': self.skill_encoder,
            'model_dir': model_dir,

            # Training history
            'training_losses': self.training_losses,
            'validation_losses': self.validation_losses,
            'training_accuracies': self.training_accuracies,
            'validation_accuracies': self.validation_accuracies,
            'best_epoch': self.best_epoch,
        }

        # Save metadata using joblib
        dump(model_data, path)
        print(f"DKT model saved to {path}")
        print(f"TensorFlow model saved to {model_dir}")

    @staticmethod
    def load(path):
        """Load a trained model"""
        model_data = load(path)

        # Create model instance
        model = DeepKnowledgeTracing(
            hidden_dim=model_data['hidden_dim'],
            num_layers=model_data['num_layers'],
            dropout=model_data['dropout'],
            learning_rate=model_data['learning_rate'],
            batch_size=model_data['batch_size'],
            max_epochs=model_data['max_epochs'],
            patience=model_data['patience'],
            max_seq_len=model_data['max_seq_len']
        )

        # Load model components
        model.num_skills = model_data['num_skills']
        model.skill_encoder = model_data['skill_encoder']

        # Load TensorFlow model
        model_dir = model_data['model_dir']
        model.model = keras.models.load_model(model_dir)

        # Load training history
        model.training_losses = model_data.get('training_losses', [])
        model.validation_losses = model_data.get('validation_losses', [])
        model.training_accuracies = model_data.get('training_accuracies', [])
        model.validation_accuracies = model_data.get('validation_accuracies', [])
        model.best_epoch = model_data.get('best_epoch', 0)

        print(f"DKT model loaded from {path}")
        print(f"TensorFlow model loaded from {model_dir}")
        return model


# Utility functions
def load_skill_builder_data(csv_path):
    """Load the 2009 skill builder dataset"""
    df = pd.read_csv(csv_path, encoding='latin1')
    return df


def train_and_save_dkt_model(data_path, output_path, **kwargs):
    """Train and save DKT model"""
    print("Loading dataset...")
    df = load_skill_builder_data(data_path)

    # Default configuration
    default_config = {
        'hidden_dim': 128,
        'num_layers': 2,
        'dropout': 0.2,
        'learning_rate': 0.001,
        'batch_size': 32,
        'max_epochs': 50,
        'patience': 10,
        'max_seq_len': 200
    }

    # Update with provided kwargs
    config = {**default_config, **kwargs}

    print("Initializing DKT model...")
    print(f"Configuration: {config}")
    dkt = DeepKnowledgeTracing(**config)

    print("Training DKT model...")
    dkt.fit(df, validation_split=0.2)

    print("Saving model...")
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    dkt.save(output_path)
    print(f"DKT model saved to {output_path}")

    # Print model statistics
    stats = dkt.get_model_statistics()
    print("\n=== MODEL STATISTICS ===")
    for key, value in stats.items():
        print(f"{key}: {value}")

    return dkt


if __name__ == "__main__":
    # Example usage
    DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"
    OUTPUT_PATH = "output/dkt_model.joblib"

    # Train and save model
    dkt_model = train_and_save_dkt_model(DATA_PATH, OUTPUT_PATH)

    # Example prediction
    print("\n=== EXAMPLE PREDICTION ===")
    user_history = [(12345, 1), (12346, 0), (12347, 1)]  # (problem_id, correct)
    skill = 'Addition and Subtraction Integers'

    try:
        predictions = dkt_model.predict_proba(user_history, skill)
        print(f"User history: {user_history}")
        print(f"Skill: {skill}")
        print(f"Predicted probabilities: {predictions}")
    except Exception as e:
        print(f"Prediction error: {e}")
