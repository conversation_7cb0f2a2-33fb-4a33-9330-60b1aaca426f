# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from sklearn.model_selection import train_test_split
import sys
import os
import warnings
from collections import defaultdict, Counter
import time

# Add models directory to path
sys.path.append('App/Dashboard/models')

from train_dkt import DeepKnowledgeTracing, load_skill_builder_data

# Set plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
warnings.filterwarnings('ignore')

# Check TensorFlow and GPU
print(f"TensorFlow version: {tf.__version__}")
print(f"GPU available: {len(tf.config.list_physical_devices('GPU')) > 0}")
if len(tf.config.list_physical_devices('GPU')) > 0:
    gpu_devices = tf.config.list_physical_devices('GPU')
    print(f"GPU devices: {[device.name for device in gpu_devices]}")
    # Enable memory growth to avoid allocating all GPU memory at once
    for device in gpu_devices:
        tf.config.experimental.set_memory_growth(device, True)

print("Libraries imported successfully!")

# Load the dataset
DATA_PATH = "../datasets/2009_skill_builder_data_corrected/skill_builder_data_corrected.csv"

print("Loading dataset...")
df = load_skill_builder_data(DATA_PATH)

print(f"Dataset loaded successfully!")
print(f"Shape: {df.shape}")
print(f"Columns: {list(df.columns)}")

# Display first few rows
print("First 5 rows of the dataset:")
display(df.head())

print("\nDataset info:")
print(df.info())

# Clean the dataset
print("Cleaning dataset...")
essential_cols = ['user_id', 'problem_id', 'skill_name', 'correct']
df_clean = df.dropna(subset=essential_cols).copy()
df_clean['correct'] = df_clean['correct'].astype(int)
df_clean['user_id'] = df_clean['user_id'].astype(str)

print(f"Original size: {len(df):,}")
print(f"Cleaned size: {len(df_clean):,}")
print(f"Removed: {len(df) - len(df_clean):,} rows ({(len(df) - len(df_clean))/len(df)*100:.2f}%)")

# Basic statistics
print("=== DATASET STATISTICS ===")
print(f"Total interactions: {len(df_clean):,}")
print(f"Unique users: {df_clean['user_id'].nunique():,}")
print(f"Unique problems: {df_clean['problem_id'].nunique():,}")
print(f"Unique skills: {df_clean['skill_name'].nunique():,}")
print(f"Overall accuracy: {df_clean['correct'].mean():.3f}")

# Sequence length statistics
user_sequences = df_clean.groupby('user_id').size()
print(f"\n=== SEQUENCE STATISTICS ===")
print(f"Average sequence length: {user_sequences.mean():.1f}")
print(f"Median sequence length: {user_sequences.median():.1f}")
print(f"Min sequence length: {user_sequences.min()}")
print(f"Max sequence length: {user_sequences.max()}")
print(f"Sequences with ≥2 interactions: {(user_sequences >= 2).sum():,}")
print(f"Sequences with ≥10 interactions: {(user_sequences >= 10).sum():,}")
print(f"Sequences with ≥50 interactions: {(user_sequences >= 50).sum():,}")

# Visualize dataset statistics
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. Sequence length distribution
axes[0, 0].hist(user_sequences, bins=50, alpha=0.7, edgecolor='black')
axes[0, 0].set_xlabel('Sequence Length')
axes[0, 0].set_ylabel('Number of Students')
axes[0, 0].set_title('Distribution of Student Sequence Lengths')
axes[0, 0].axvline(user_sequences.mean(), color='red', linestyle='--', label=f'Mean: {user_sequences.mean():.1f}')
axes[0, 0].legend()

# 2. Skill frequency
skill_counts = df_clean['skill_name'].value_counts().head(15)
axes[0, 1].barh(range(len(skill_counts)), skill_counts.values)
axes[0, 1].set_yticks(range(len(skill_counts)))
axes[0, 1].set_yticklabels([skill[:30] + '...' if len(skill) > 30 else skill for skill in skill_counts.index])
axes[0, 1].set_xlabel('Number of Interactions')
axes[0, 1].set_title('Top 15 Most Frequent Skills')

# 3. Accuracy by skill
skill_accuracy = df_clean.groupby('skill_name')['correct'].agg(['mean', 'count']).reset_index()
skill_accuracy = skill_accuracy[skill_accuracy['count'] >= 100].sort_values('mean')
top_skills = skill_accuracy.head(10)
axes[1, 0].barh(range(len(top_skills)), top_skills['mean'])
axes[1, 0].set_yticks(range(len(top_skills)))
axes[1, 0].set_yticklabels([skill[:25] + '...' if len(skill) > 25 else skill for skill in top_skills['skill_name']])
axes[1, 0].set_xlabel('Accuracy')
axes[1, 0].set_title('10 Most Difficult Skills (≥100 interactions)')

# 4. Sequence length vs accuracy
user_stats = df_clean.groupby('user_id').agg({
    'correct': ['mean', 'count']
}).reset_index()
user_stats.columns = ['user_id', 'accuracy', 'sequence_length']
user_stats = user_stats[user_stats['sequence_length'] >= 5]  # Filter short sequences

# Bin sequence lengths for better visualization
bins = [0, 10, 25, 50, 100, float('inf')]
labels = ['5-10', '11-25', '26-50', '51-100', '100+']
user_stats['length_bin'] = pd.cut(user_stats['sequence_length'], bins=bins, labels=labels, include_lowest=True)

sns.boxplot(data=user_stats, x='length_bin', y='accuracy', ax=axes[1, 1])
axes[1, 1].set_xlabel('Sequence Length Bin')
axes[1, 1].set_ylabel('Student Accuracy')
axes[1, 1].set_title('Student Accuracy vs Sequence Length')
axes[1, 1].tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# Hyperparameter tuning for DKT model
print("=== HYPERPARAMETER TUNING ===")
print("This section demonstrates hyperparameter optimization for the DKT model.")
print("We'll use grid search to find the best combination of hyperparameters.")

# Initialize DKT model for tuning
dkt_tuner = DeepKnowledgeTracing(
    hidden_dim=128,  # Will be optimized
    num_layers=2,    # Will be optimized
    dropout=0.2,     # Will be optimized
    learning_rate=0.001,  # Will be optimized
    batch_size=64,   # Will be optimized
    max_epochs=30,
    patience=5,
    max_seq_len=100
)

print(f"\nStarting hyperparameter tuning...")
print(f"This may take several minutes depending on the number of trials.")

# Perform hyperparameter tuning
# Note: Using a smaller number of trials for demonstration
# In practice, you might want to use more trials for better results

tuning_results = dkt_tuner.tune_hyperparameters(
    df_clean,
    method='grid_search',  # Use 'keras_tuner' if keras-tuner is installed
    max_trials=8,  # Limited for demo purposes
    tuning_epochs=10  # Fewer epochs for faster tuning
)

print("\n=== HYPERPARAMETER TUNING COMPLETED ===")
print(f"Best validation accuracy: {tuning_results['best_score']:.4f}")
print(f"Best parameters: {tuning_results['best_params']}")

# Analyze tuning results
if 'results' in tuning_results and not tuning_results['results'].empty:
    results_df = tuning_results['results']
    
    print("\n=== TOP 5 HYPERPARAMETER COMBINATIONS ===")
    top_5 = results_df.nlargest(5, 'val_accuracy')
    display(top_5)
    
    # Visualize hyperparameter effects
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))
    axes = axes.flatten()
    
    # Parameters to analyze
    params_to_plot = ['hidden_dim', 'num_layers', 'dropout', 'learning_rate', 'batch_size']
    
    for i, param in enumerate(params_to_plot):
        if param in results_df.columns:
            # Box plot of validation accuracy by parameter value
            param_values = results_df[param].unique()
            if len(param_values) > 1:
                results_df.boxplot(column='val_accuracy', by=param, ax=axes[i])
                axes[i].set_title(f'Validation Accuracy by {param}')
                axes[i].set_xlabel(param)
                axes[i].set_ylabel('Validation Accuracy')
            else:
                axes[i].text(0.5, 0.5, f'Only one value\nfor {param}', 
                           ha='center', va='center', transform=axes[i].transAxes)
                axes[i].set_title(f'{param} (constant)')
    
    # Remove empty subplot
    if len(params_to_plot) < len(axes):
        fig.delaxes(axes[-1])
    
    plt.tight_layout()
    plt.suptitle('Hyperparameter Analysis', fontsize=16, y=1.02)
    plt.show()
    
    # Parameter correlation analysis
    print("\n=== PARAMETER CORRELATION WITH PERFORMANCE ===")
    numeric_cols = ['hidden_dim', 'num_layers', 'dropout', 'learning_rate', 'batch_size', 'val_accuracy']
    numeric_cols = [col for col in numeric_cols if col in results_df.columns]
    
    if len(numeric_cols) > 1:
        correlations = results_df[numeric_cols].corr()['val_accuracy'].sort_values(ascending=False)
        print("Correlation with validation accuracy:")
        for param, corr in correlations.items():
            if param != 'val_accuracy':
                print(f"  {param}: {corr:.3f}")
else:
    print("No detailed tuning results available for analysis.")

# Initialize DKT model with best parameters from tuning
print("Initializing Deep Knowledge Tracing Model with Best Parameters...")
print("=" * 60)

# Use best parameters from hyperparameter tuning
best_params = tuning_results['best_params']
print(f"Best parameters from tuning: {best_params}")

# Create final configuration combining best params with defaults
final_config = {
    'hidden_dim': best_params.get('hidden_dim', 128),
    'num_layers': best_params.get('num_layers', 2),
    'dropout': best_params.get('dropout', 0.2),
    'learning_rate': best_params.get('learning_rate', 0.001),
    'batch_size': best_params.get('batch_size', 64),
    'max_epochs': 30,         # Keep reasonable for final training
    'patience': 8,            # Early stopping patience
    'max_seq_len': 100        # Maximum sequence length
}

print(f"\nFinal configuration: {final_config}")

# Initialize model with best parameters
dkt_model = DeepKnowledgeTracing(**final_config)

print(f"Model initialized with GPU: {dkt_model.gpu_available}")
print(f"Expected validation accuracy: {tuning_results['best_score']:.4f}")

# Train the model
print("Training DKT Model...")
print("=" * 50)

# Start training
start_time = time.time()
dkt_model.fit(df_clean, validation_split=0.2)
training_time = time.time() - start_time

print(f"\nTraining completed in {training_time:.2f} seconds ({training_time/60:.1f} minutes)")
print("DKT model training finished!")

# Get model statistics
stats = dkt_model.get_model_statistics()
print("\n=== DKT MODEL STATISTICS ===")
for key, value in stats.items():
    print(f"{key}: {value}")

# Plot training curves
if dkt_model.training_losses and dkt_model.validation_losses:
    fig, axes = plt.subplots(1, 2, figsize=(15, 5))
    
    epochs = range(1, len(dkt_model.training_losses) + 1)
    
    # Loss curves
    axes[0].plot(epochs, dkt_model.training_losses, 'b-', label='Training Loss', linewidth=2)
    axes[0].plot(epochs, dkt_model.validation_losses, 'r-', label='Validation Loss', linewidth=2)
    axes[0].axvline(dkt_model.best_epoch + 1, color='green', linestyle='--', alpha=0.7, label=f'Best Epoch ({dkt_model.best_epoch + 1})')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].set_title('Training and Validation Loss')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)
    
    # Accuracy curves
    axes[1].plot(epochs, dkt_model.training_accuracies, 'b-', label='Training Accuracy', linewidth=2)
    axes[1].plot(epochs, dkt_model.validation_accuracies, 'r-', label='Validation Accuracy', linewidth=2)
    axes[1].axvline(dkt_model.best_epoch + 1, color='green', linestyle='--', alpha=0.7, label=f'Best Epoch ({dkt_model.best_epoch + 1})')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('Accuracy')
    axes[1].set_title('Training and Validation Accuracy')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Print improvement metrics
    if len(dkt_model.training_losses) > 1:
        train_improvement = ((dkt_model.training_accuracies[-1] - dkt_model.training_accuracies[0]) / dkt_model.training_accuracies[0]) * 100
        val_improvement = ((dkt_model.validation_accuracies[-1] - dkt_model.validation_accuracies[0]) / dkt_model.validation_accuracies[0]) * 100
        
        print(f"\n=== TRAINING IMPROVEMENTS ===")
        print(f"Training accuracy improvement: {train_improvement:.2f}%")
        print(f"Validation accuracy improvement: {val_improvement:.2f}%")
        print(f"Final training accuracy: {dkt_model.training_accuracies[-1]:.4f}")
        print(f"Final validation accuracy: {dkt_model.validation_accuracies[-1]:.4f}")
else:
    print("No training history available for plotting.")

# Demo predictions with sample students and skills
print("=== DKT MODEL PREDICTIONS DEMO ===")

# Get some sample skills from the dataset
sample_skills = df_clean['skill_name'].value_counts().head(5).index.tolist()
print(f"Sample skills for demo: {sample_skills[:3]}")

# Example 1: Simulated learning progression
print("\n=== EXAMPLE 1: Simulated Learning Progression ===")
skill = sample_skills[0]
print(f"Skill: {skill}")

# Simulate a learning progression: starts with mistakes, then improves
learning_sequence = [0, 0, 1, 0, 1, 1, 1, 1]  # 0=incorrect, 1=correct
print(f"Learning sequence: {learning_sequence}")

try:
    predictions = dkt_model.predict_proba(learning_sequence, skill)
    print("\nLearning progression:")
    print("Step | Actual | Predicted | Interpretation")
    print("-" * 50)
    
    for i, (actual, pred) in enumerate(zip(learning_sequence, predictions)):
        interpretation = "Learning" if i > 0 and pred > predictions[i-1] else "Struggling" if pred < 0.5 else "Stable"
        print(f"{i+1:4d} | {actual:6d} | {pred:9.3f} | {interpretation}")
        
except Exception as e:
    print(f"Error in prediction: {e}")

# Example 2: Real student data
print("\n=== EXAMPLE 2: Real Student Data ===")

# Find a student with a reasonable sequence length
user_sequences = df_clean.groupby('user_id').size()
suitable_users = user_sequences[(user_sequences >= 10) & (user_sequences <= 30)].index

if len(suitable_users) > 0:
    sample_user = suitable_users[0]
    user_data = df_clean[df_clean['user_id'] == sample_user].copy()
    
    # Sort by order if available
    if 'order_id' in user_data.columns:
        user_data = user_data.sort_values('order_id')
    
    # Get the most frequent skill for this user
    user_skills = user_data['skill_name'].value_counts()
    if len(user_skills) > 0:
        sample_skill = user_skills.index[0]
        skill_data = user_data[user_data['skill_name'] == sample_skill]
        
        if len(skill_data) >= 3:
            print(f"User: {sample_user}")
            print(f"Skill: {sample_skill}")
            print(f"Number of interactions: {len(skill_data)}")
            
            # Create history
            history = list(zip(skill_data['problem_id'], skill_data['correct']))
            actual_sequence = skill_data['correct'].tolist()
            
            print(f"Actual sequence: {actual_sequence}")
            
            try:
                predictions = dkt_model.predict_proba(history, sample_skill, sample_user)
                
                print("\nReal student progression:")
                print("Step | Problem | Actual | Predicted")
                print("-" * 40)
                
                for i, ((prob_id, actual), pred) in enumerate(zip(history[:10], predictions[:10])):
                    print(f"{i+1:4d} | {prob_id:7d} | {actual:6d} | {pred:9.3f}")
                    
                # Calculate accuracy
                binary_preds = [1 if p > 0.5 else 0 for p in predictions]
                accuracy = sum(1 for a, p in zip(actual_sequence, binary_preds) if a == p) / len(actual_sequence)
                print(f"\nPrediction accuracy: {accuracy:.3f}")
                
            except Exception as e:
                print(f"Error in prediction: {e}")
        else:
            print("Not enough skill-specific data for this user")
    else:
        print("No skills found for this user")
else:
    print("No suitable users found for demo")

# Analyze different skills with DKT
print("=== SKILL ANALYSIS WITH DKT ===")

# Test DKT on different difficulty levels
skill_difficulty = df_clean.groupby('skill_name').agg({
    'correct': ['mean', 'count']
}).reset_index()
skill_difficulty.columns = ['skill_name', 'accuracy', 'count']
skill_difficulty = skill_difficulty[skill_difficulty['count'] >= 50]  # Filter skills with enough data

# Categorize skills by difficulty
easy_skills = skill_difficulty[skill_difficulty['accuracy'] > 0.8]['skill_name'].tolist()[:3]
medium_skills = skill_difficulty[(skill_difficulty['accuracy'] >= 0.6) & (skill_difficulty['accuracy'] <= 0.8)]['skill_name'].tolist()[:3]
hard_skills = skill_difficulty[skill_difficulty['accuracy'] < 0.6]['skill_name'].tolist()[:3]

print(f"Easy skills (>80% accuracy): {easy_skills}")
print(f"Medium skills (60-80% accuracy): {medium_skills}")
print(f"Hard skills (<60% accuracy): {hard_skills}")

# Test learning patterns for different difficulty levels
test_sequences = {
    'Struggling learner': [0, 0, 0, 1, 0, 1, 1],
    'Quick learner': [0, 1, 1, 1, 1, 1, 1],
    'Inconsistent learner': [1, 0, 1, 0, 1, 0, 1]
}

for learner_type, sequence in test_sequences.items():
    print(f"\n=== {learner_type.upper()} ===")
    print(f"Sequence: {sequence}")
    
    # Test on easy, medium, and hard skills
    for difficulty, skills in [('Easy', easy_skills), ('Medium', medium_skills), ('Hard', hard_skills)]:
        if skills:
            skill = skills[0]
            try:
                predictions = dkt_model.predict_proba(sequence, skill)
                final_prob = predictions[-1] if predictions else 0.5
                improvement = predictions[-1] - predictions[0] if len(predictions) > 1 else 0
                print(f"{difficulty:6s} skill ({skill[:30]}...): Final prob = {final_prob:.3f}, Improvement = {improvement:+.3f}")
            except Exception as e:
                print(f"{difficulty:6s} skill: Error - {e}")

# Save the trained model
MODEL_PATH = "models/output/dkt_model.h5"
os.makedirs(os.path.dirname(MODEL_PATH), exist_ok=True)
dkt_model.save(MODEL_PATH)
print(f"\nDKT model saved to: {MODEL_PATH}")

print("\n" + "=" * 60)
print("DEEP KNOWLEDGE TRACING MODEL TRAINING SUMMARY")
print("=" * 60)
print(f"✅ Successfully trained DKT model using TensorFlow/Keras LSTM networks")
print(f"✅ Model architecture: {dkt_model.num_layers} LSTM layers, {dkt_model.hidden_dim} hidden units")
print(f"✅ Trained on {dkt_model.num_skills} unique skills")
print(f"✅ GPU acceleration: {dkt_model.gpu_available}")
print(f"✅ Final validation accuracy: {dkt_model.validation_accuracies[-1]:.4f}" if dkt_model.validation_accuracies else "")
print(f"✅ Model can capture sequential learning patterns and long-term dependencies")
print(f"✅ Suitable for complex student behavior modeling")
print(f"✅ Model saved and ready for deployment")

# Hyperparameter tuning summary
print(f"\n=== HYPERPARAMETER TUNING SUMMARY ===")
print(f"🔧 Tuning method: Grid Search")
print(f"🔧 Best validation accuracy: {tuning_results['best_score']:.4f}")
print(f"🔧 Optimal parameters found:")
for param, value in tuning_results['best_params'].items():
    print(f"   • {param}: {value}")

if 'results' in tuning_results and not tuning_results['results'].empty:
    results_df = tuning_results['results']
    improvement = ((tuning_results['best_score'] - results_df['val_accuracy'].min()) / 
                   results_df['val_accuracy'].min()) * 100
    print(f"🔧 Performance improvement from tuning: {improvement:.1f}%")
    print(f"🔧 Total trials evaluated: {len(results_df)}")

# Compare with traditional models
print(f"\n=== DKT vs Traditional Models ===")
print(f"📊 BKT: Fixed parameters, Markovian assumptions")
print(f"📊 PFA: Logistic regression, linear learning")
print(f"🧠 DKT: TensorFlow deep learning, non-linear patterns, sequential modeling")
print(f"\n🎯 DKT Advantages:")
print(f"   • Captures complex learning patterns")
print(f"   • Models long-term dependencies")
print(f"   • No strong parametric assumptions")
print(f"   • Can handle variable-length sequences")
print(f"   • Learns skill interactions automatically")
print(f"   • GPU acceleration with TensorFlow")
print(f"   • Easy deployment with TensorFlow Serving")
print(f"   • Hyperparameter optimization for best performance")
print(f"   • Automated architecture search capabilities")

