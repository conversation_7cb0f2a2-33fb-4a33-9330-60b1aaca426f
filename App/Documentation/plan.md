using math dataset

1- bkt model
2- pfa model
3- dkt model

4- create ensambled model
5- apply hyperparam<PERSON> tunning
6- check for data mining techniques to apply

generate skills report

demo
- create testing questions for the same skills (2 kills out of the 4 groups, 20 questions per skill)
- show mastery learning after each attempt (answer) per skill
- calculate user profile (session) score throught trained model